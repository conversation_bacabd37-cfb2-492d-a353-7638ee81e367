// 姓名详批页面JavaScript功能

// Canvas动画变量
let canvas, ctx;
let particles = [];
let animationId;

// AI服务变量
let aiService = null;
let nameAnalysisAI = null;
let currentUserData = null;
let currentBasicResult = null;

// 宝宝日期选择器变量
let currentYear = new Date().getFullYear();
let currentMonth = new Date().getMonth() + 1;
let selectedDate = null;
let selectedHour = null;
// 年份选择器变量
let yearRangeStart = Math.floor(currentYear / 10) * 10;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    window.unifiedAuthService = new UnifiedAuthService();
    window.memberService = new MemberService();
    window.orderPaymentManager = new OrderPaymentManager();
    initCanvas();
    initForm();
    generateParticles();
    animateCanvas();
    initializeSmartServices();
    initBabyDatePicker();
    
    // 根据URL参数初始化页面类型
    initPageByUrlParams();
});

// 根据URL参数初始化页面类型
function initPageByUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    
    const headerTitle = document.querySelector('.header-title');
    const introTitle = document.querySelector('.intro-title');
    const introDesc = document.querySelector('.intro-desc');
    const submitBtn = document.querySelector('.submit-btn .btn-text');
    const submitNote = document.querySelector('.submit-note');
    
    switch(type) {
        case 'boy':
            console.log('👦 进入男孩名字模式');
            if (headerTitle) headerTitle.textContent = '👦 男孩起名';
            if (introTitle) introTitle.textContent = '男孩姓名详批';
            if (introDesc) introDesc.textContent = '为男孩起一个响亮有力、寓意深远的好名字';
            if (submitBtn) submitBtn.textContent = '开始男孩起名';
            if (submitNote) submitNote.innerHTML = '🎯 专为男孩设计，阳刚大气的好名字';
            
            // 默认选中男性并隐藏性别选择
            const maleRadio = document.querySelector('input[name="gender"][value="male"]');
            if (maleRadio) maleRadio.checked = true;
            hideGenderSelection();
            break;
            
        case 'girl':
            console.log('👧 进入女孩名字模式');
            if (headerTitle) headerTitle.textContent = '👧 女孩起名';
            if (introTitle) introTitle.textContent = '女孩姓名详批';
            if (introDesc) introDesc.textContent = '为女孩起一个优雅美丽、内涵丰富的好名字';
            if (submitBtn) submitBtn.textContent = '开始女孩起名';
            if (submitNote) submitNote.innerHTML = '🌸 专为女孩设计，温柔典雅的好名字';
            
            // 默认选中女性并隐藏性别选择
            const femaleRadio = document.querySelector('input[name="gender"][value="female"]');
            if (femaleRadio) femaleRadio.checked = true;
            hideGenderSelection();
            break;
            
        case 'analysis':
            console.log('📝 进入姓名详批模式');
            if (headerTitle) headerTitle.textContent = '📝 姓名详批';
            if (introTitle) introTitle.textContent = '姓名五行详批';
            if (introDesc) introDesc.textContent = '深度解析您的姓名五行配置、运势吉凶';
            if (submitBtn) submitBtn.textContent = '开始姓名详批';
            if (submitNote) submitNote.innerHTML = '🔮 专业解析您的姓名奥秘，洞察人生运势';
            break;
            
        case 'matching':
            console.log('💕 进入姓名配对模式');
            if (headerTitle) headerTitle.textContent = '💕 姓名配对';
            if (introTitle) introTitle.textContent = '姓名缘分配对';
            if (introDesc) introDesc.textContent = '通过姓名测算两人的缘分指数和配对程度';
            if (submitBtn) submitBtn.textContent = '开始姓名配对';
            if (submitNote) submitNote.innerHTML = '💖 测算姓名缘分，寻找命中注定的Ta';
            
            // 为配对模式添加特殊标识
            document.body.classList.add('matching-mode');
            break;
            
        default:
            console.log('📝 默认姓名详批模式');
            // 保持默认设置
    }
    
    // 更新页面图标
    updatePageIcon(type);
}

// 更新页面图标
function updatePageIcon(type) {
    const namingIcon = document.querySelector('.naming-icon');
    if (namingIcon) {
        const icons = {
            'boy': '👦',
            'girl': '👧', 
            'analysis': '📝',
            'matching': '💕'
        };
        namingIcon.textContent = icons[type] || '📝';
    }
}

// 隐藏性别选择区域
function hideGenderSelection() {
    // 方式1: 使用 :has 选择器（现代浏览器支持）
    const genderGroup = document.querySelector('.input-group:has(.gender-options)');
    if (genderGroup) {
        genderGroup.classList.add('gender-hidden');
        genderGroup.style.display = 'none';
        console.log('✅ 性别选择区域已隐藏（性别已确定）');
        return;
    }
    
    // 方式2: 通过 gender-options 查找父元素
    const genderOptions = document.querySelector('.gender-options');
    if (genderOptions) {
        const genderInputGroup = genderOptions.closest('.input-group');
        if (genderInputGroup) {
            genderInputGroup.classList.add('gender-hidden');
            genderInputGroup.style.display = 'none';
            console.log('✅ 性别选择区域已隐藏（通过父级查找）');
            return;
        }
    }
    
    // 方式3: 遍历所有 input-group，找到包含性别的那个
    const inputGroups = document.querySelectorAll('.input-group');
    inputGroups.forEach(group => {
        const genderLabel = group.querySelector('label .label-text');
        if (genderLabel && genderLabel.textContent.includes('性别')) {
            group.classList.add('gender-hidden');
            group.style.display = 'none';
            console.log('✅ 性别选择区域已隐藏（通过标签文本查找）');
        }
    });
}

// 初始化智能服务
function initializeSmartServices() {
    try {
        // 检查必要的类是否存在
        if (typeof NameAnalysisAI === 'undefined') {
            console.error('❌ NameAnalysisAI 类未找到，请检查模块是否正确加载');
            return;
        }
        if (typeof AIService === 'undefined' && window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
            window.AI_CONFIG.SERVICE_TYPE = 'offline';
        }
        // 初始化智能服务
        if (window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
            try {
                aiService = new AIService(window.AI_CONFIG);
                window.aiService = aiService;
            } catch (error) {
                window.AI_CONFIG.SERVICE_TYPE = 'offline';
            }
        }
        // 初始化姓名分析
        nameAnalysisAI = new NameAnalysisAI();
        
        // 初始化认证服务
        window.unifiedAuthService = new UnifiedAuthService();
        window.memberService = new MemberService();
        window.orderPaymentManager = new OrderPaymentManager();
        console.log('✅ 认证服务初始化成功');
        
        // 更新服务类型显示
        updateAIServiceDisplay();
    } catch (error) {
        // 继续使用基础功能，不影响主要功能
        console.error('❌ 智能服务初始化失败:', error);
    }
}

// 更新AI服务类型显示
function updateAIServiceDisplay() {
    const serviceTypeElement = document.getElementById('aiServiceType');
    if (serviceTypeElement) {
        serviceTypeElement.textContent = "易海堂";
    }
}

// Canvas初始化
function initCanvas() {
    canvas = document.getElementById('namingCanvas');
    ctx = canvas.getContext('2d');
    
    // 设置canvas尺寸
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
}

// 生成粒子
function generateParticles() {
    particles = [];
    const particleCount = 40;
    
    for (let i = 0; i < particleCount; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            size: Math.random() * 3 + 1,
            speedX: (Math.random() - 0.5) * 0.4,
            speedY: (Math.random() - 0.5) * 0.4,
            opacity: Math.random() * 0.4 + 0.2,
            color: getRandomBlueColor(),
            symbol: getRandomNameSymbol()
        });
    }
}

// 获取随机蓝紫色
function getRandomBlueColor() {
    const colors = [
        'rgba(102, 126, 234, ',
        'rgba(118, 75, 162, ',
        'rgba(132, 94, 194, ',
        'rgba(91, 134, 229, '
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}

// 获取随机姓名符号
function getRandomNameSymbol() {
    const symbols = ['📝', '✨', '🌟', '💫', '📚', '🔤', '💎', '🎭'];
    return symbols[Math.floor(Math.random() * symbols.length)];
}

// Canvas动画
function animateCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制渐变背景
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, 'rgba(102, 126, 234, 0.08)');
    gradient.addColorStop(0.5, 'rgba(118, 75, 162, 0.04)');
    gradient.addColorStop(1, 'rgba(102, 126, 234, 0.08)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 更新和绘制粒子
    particles.forEach(particle => {
        // 更新位置
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // 边界检查
        if (particle.x < 0 || particle.x > canvas.width) particle.speedX *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.speedY *= -1;
        
        // 绘制粒子
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        ctx.font = `${particle.size * 8}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText(particle.symbol, particle.x, particle.y);
        ctx.restore();
        
        // 绘制发光效果
        ctx.save();
        ctx.globalAlpha = particle.opacity * 0.3;
        ctx.fillStyle = particle.color + (particle.opacity * 0.3) + ')';
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size * 3, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    });
    
    animationId = requestAnimationFrame(animateCanvas);
}

// 表单初始化
function initForm() {
    const form = document.getElementById('namingForm');
    form.addEventListener('submit', handleFormSubmit);
}

// 表单提交处理
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const birthDate = formData.get('birthDate');
    const birthHour = formData.get('birthHourSelect'); // 更新字段名
    const date = new Date(birthDate);
    
    const userData = {
        name: formData.get('userName'),
        gender: formData.get('gender'),
        birthYear: date.getFullYear(),
        birthMonth: date.getMonth() + 1,
        birthDay: date.getDate(),
        birthDate: birthDate,
        birthHour: birthHour,
        focus: formData.getAll('focus')
    };
    
    // 验证表单数据
    if (!validateFormData(userData)) {
        return;
    }

    // 服务配置
    const serviceConfig = {
        type: 'name-analysis',
        name: '姓名详批',
        price: 15.9,
        description: '全面分析姓名寓意，解读人生命运'
    };

    // 创建订单并支付
    try {
        await window.orderPaymentManager.createOrderAndPay(
            serviceConfig,
            userData,
            // 支付成功回调
            async function(order, paymentResult) {
                console.log('支付成功，开始姓名分析');
                await performNameAnalysis(userData);
            },
            // 取消支付回调
            function(order) {
                console.log('用户取消支付');
            }
        );
    } catch (error) {
        console.error('订单创建失败:', error);
        alert('创建订单失败，请稍后重试');
    }
}

// 验证表单数据
function validateFormData(userData) {
    if (!userData.name || userData.name.trim() === '') {
        alert('请输入姓名');
        return false;
    }
    
    if (!userData.gender) {
        alert('请选择性别');
        return false;
    }
    
    if (!userData.birthDate) {
        alert('请选择出生日期');
        return false;
    }
    
    if (!userData.birthHour) {
        alert('请选择出生时辰');
        return false;
    }
    
    return true;
}

// 执行姓名分析
async function performNameAnalysis(userData) {
    try {
        // 显示加载动画
        showLoading();
        
        // 模拟分析过程
        await simulateAnalysis();
        
        // 生成姓名分析结果
        const result = generateNameAnalysis(userData);
        
        // 保存数据供AI分析使用
        currentUserData = userData;
        currentBasicResult = result;
        
        // 隐藏加载动画并显示结果
        hideLoading();
        showResult(result);
        
        // 启动AI深度分析
        startSmartAnalysis(userData, result);
    } catch (error) {
        console.error('姓名分析失败:', error);
        hideLoading();
        alert('分析失败，请稍后重试');
    }
}

// 表单验证
function validateForm(data) {
    if (!data.name || data.name.length < 2) {
        alert('请输入正确的姓名');
        return false;
    }
    
    if (!data.gender) {
        alert('请选择性别');
        return false;
    }
    
    if (!data.birthDate) {
        alert('请选择出生日期');
        return false;
    }
    
    if (!data.birthHour) {
        alert('请选择出生时辰');
        return false;
    }
    
    return true;
}

// 显示加载动画
function showLoading() {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = 'flex';
    
    // 动态更新加载文本
    const loadingTexts = [
        '正在分析您的姓名...',
        '计算五行属性...',
        '分析三才数理...',
        '生成详批报告...'
    ];
    
    let textIndex = 0;
    const loadingText = document.querySelector('.loading-text');
    
    const textInterval = setInterval(() => {
        if (textIndex < loadingTexts.length) {
            loadingText.textContent = loadingTexts[textIndex];
            textIndex++;
        } else {
            clearInterval(textInterval);
        }
    }, 800);
}

// 隐藏加载动画
function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = 'none';
}

// 模拟分析过程
function simulateAnalysis() {
    return new Promise(resolve => {
        setTimeout(resolve, 3200);
    });
}

// 生成姓名分析结果
function generateNameAnalysis(userData) {
    const { name, gender, birthYear, birthMonth, birthDay, birthHour, focus } = userData;
    
    // 计算姓名笔画数
    const strokes = calculateNameStrokes(name);
    
    // 计算五行属性
    const wuxing = calculateNameWuxing(name, birthYear, birthMonth, birthDay, birthHour);
    
    // 计算姓名分数
    const nameScore = calculateNameScore(strokes, wuxing, gender);
    
    // 生成具体分析
    const analysis = generateNameAnalysis_Detail(name, strokes, wuxing, nameScore, focus, gender);
    
    // 生成建议
    const suggestions = generateNameSuggestions(strokes, wuxing, nameScore, focus);
    
    return {
        name,
        nameScore,
        strokes,
        wuxing,
        analysis,
        suggestions,
        personality: generatePersonality(strokes, wuxing),
        luckyNumbers: generateLuckyNumbers(strokes),
        luckyColors: getLuckyColors(wuxing),
        career: getBestCareer(wuxing, gender)
    };
}

// 计算姓名笔画数（简化版）
function calculateNameStrokes(name) {
    // 这里使用简化的笔画计算，实际应用中需要更精确的字典
    const strokeMap = {
        // 常见姓氏笔画
        '王': 4, '李': 7, '张': 11, '刘': 15, '陈': 16, '杨': 13, '黄': 12, '赵': 14, '周': 8, '吴': 7,
        '徐': 10, '孙': 10, '胡': 9, '朱': 6, '高': 10, '林': 8, '何': 7, '郭': 15, '马': 10, '罗': 19,
        // 常见名字用字
        '伟': 11, '芳': 10, '娜': 10, '敏': 11, '静': 16, '丽': 7, '强': 12, '磊': 15, '军': 9, '洋': 9,
        '勇': 9, '艳': 24, '杰': 12, '涛': 18, '明': 8, '超': 12, '秀': 7, '霞': 17, '平': 5, '刚': 10,
        '桂': 10, '娟': 10, '兰': 5, '凤': 14, '洁': 16, '梅': 11, '琳': 12, '素': 10, '云': 4, '莲': 17,
        '真': 10, '环': 8, '雪': 11, '荣': 14, '爱': 13, '妹': 8, '霞': 17, '香': 9, '月': 4, '莺': 21,
        '媛': 12, '艳': 24, '瑞': 13, '凡': 3, '佳': 8, '嘉': 14, '琼': 20, '勤': 13, '珍': 9, '贞': 9,
        '毅': 15, '智': 12, '淑': 12, '惠': 12, '晶': 12, '妍': 9, '茜': 12, '秋': 9, '珊': 9, '莎': 13,
        '锦': 16, '黛': 17, '青': 8, '倩': 10, '婷': 12, '姣': 9, '婉': 11, '娴': 15, '瑾': 15, '颖': 16,
        '露': 21, '瑶': 14, '怡': 8, '婵': 15, '雁': 12, '蓓': 16, '纨': 10, '仪': 15, '荷': 13, '丹': 4,
        '蓉': 16, '眉': 9, '君': 7, '琴': 12, '蕊': 18, '薇': 19, '菁': 14, '梦': 14, '岚': 12, '苑': 8,
        '婕': 11, '馨': 20, '瑗': 13, '琰': 12, '韵': 19, '融': 16, '园': 13, '艺': 21, '咏': 8, '卿': 11,
        '聪': 17, '澜': 21, '纯': 10, '毓': 14, '悦': 11, '昭': 9, '冰': 6, '爽': 11, '琬': 12, '茗': 12,
        '羽': 6, '希': 7, '宁': 14, '欣': 8, '飘': 20, '育': 10, '滢': 19, '馥': 18, '筠': 13, '柔': 9,
        '竹': 6, '霭': 24, '凝': 16, '晓': 16, '欢': 22, '霄': 15, '枫': 13, '芸': 10, '菲': 14, '寒': 12,
        '伊': 6, '亚': 8, '宜': 8, '可': 5, '姬': 10, '舒': 12, '影': 15, '荔': 12, '枝': 8, '思': 9,
        '丽': 7, '秀': 7
    };
    
    let totalStrokes = 0;
    let tianGe = 0, renGe = 0, diGe = 0;
    
    for (let i = 0; i < name.length; i++) {
        const char = name[i];
        const stroke = strokeMap[char] || (char.charCodeAt(0) % 20 + 5); // 默认笔画数
        totalStrokes += stroke;
        
        if (i === 0) tianGe = stroke; // 姓氏笔画
        if (i === 1) renGe += stroke; // 名字第一字
        if (i === 2) diGe += stroke; // 名字第二字
    }
    
    // 单名的情况
    if (name.length === 2) {
        renGe = tianGe + (strokeMap[name[1]] || 10);
        diGe = (strokeMap[name[1]] || 10) + 1;
    }
    
    return {
        total: totalStrokes,
        tianGe: tianGe,
        renGe: renGe,
        diGe: diGe,
        waiGe: totalStrokes - renGe,
        zongGe: totalStrokes
    };
}

// 计算姓名五行属性
function calculateNameWuxing(name, year, month, day, hour) {
    // 简化的五行计算
    const wuxingMap = {
        '木': ['甲', '乙', '木', '林', '森', '杨', '柳', '桃', '梅', '兰', '竹', '菊'],
        '火': ['丙', '丁', '火', '炎', '焱', '阳', '明', '亮', '辉', '灿', '烁', '煜'],
        '土': ['戊', '己', '土', '地', '山', '岩', '石', '田', '圭', '坤', '培', '城'],
        '金': ['庚', '辛', '金', '银', '铜', '铁', '钢', '锋', '锐', '钧', '铭', '鑫'],
        '水': ['壬', '癸', '水', '江', '河', '海', '洋', '湖', '波', '涛', '泽', '润']
    };
    
    let nameWuxing = [];
    
    for (let char of name) {
        let found = false;
        for (let [element, chars] of Object.entries(wuxingMap)) {
            if (chars.includes(char)) {
                nameWuxing.push(element);
                found = true;
                break;
            }
        }
        if (!found) {
            // 根据字的笔画数判断五行
            const stroke = char.charCodeAt(0) % 20 + 1;
            if (stroke % 5 === 1) nameWuxing.push('木');
            else if (stroke % 5 === 2) nameWuxing.push('火');
            else if (stroke % 5 === 3) nameWuxing.push('土');
            else if (stroke % 5 === 4) nameWuxing.push('金');
            else nameWuxing.push('水');
        }
    }
    
    // 计算八字五行
    const baziWuxing = calculateBaziWuxing(year, month, day, hour);
    
    return {
        name: nameWuxing,
        bazi: baziWuxing,
        main: nameWuxing[0] || '土'
    };
}

// 计算八字五行
function calculateBaziWuxing(year, month, day, hour) {
    const yearWuxing = ['金', '木', '水', '火', '土'][year % 5];
    const monthWuxing = ['水', '土', '木', '木', '土', '火', '火', '土', '金', '金', '土', '水'][month - 1];
    const dayWuxing = ['金', '木', '水', '火', '土'][day % 5];
    
    const hourMap = {
        'zi': '水', 'chou': '土', 'yin': '木', 'mao': '木',
        'chen': '土', 'si': '火', 'wu': '火', 'wei': '土',
        'shen': '金', 'you': '金', 'xu': '土', 'hai': '水'
    };
    const hourWuxing = hourMap[hour] || '土';
    
    return {
        year: yearWuxing,
        month: monthWuxing,
        day: dayWuxing,
        hour: hourWuxing
    };
}

// 计算姓名分数
function calculateNameScore(strokes, wuxing, gender) {
    let score = 70; // 基础分数
    
    // 笔画数理评分
    const luckyNumbers = [1, 3, 5, 6, 7, 8, 11, 13, 15, 16, 17, 18, 21, 23, 24, 25, 29, 31, 32, 33, 35, 37, 39, 41, 45, 47, 48, 52, 57, 61, 63, 65, 67, 68, 81];
    
    if (luckyNumbers.includes(strokes.zongGe % 81)) score += 10;
    if (luckyNumbers.includes(strokes.renGe % 81)) score += 8;
    if (luckyNumbers.includes(strokes.diGe % 81)) score += 6;
    
    // 五行配置评分
    const nameElements = wuxing.name;
    const baziElements = Object.values(wuxing.bazi);
    
    // 检查五行是否相生
    const shengRelations = {
        '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
    };
    
    for (let i = 0; i < nameElements.length - 1; i++) {
        if (shengRelations[nameElements[i]] === nameElements[i + 1]) {
            score += 5;
        }
    }
    
    // 检查与八字的配合
    const baziSet = new Set(baziElements);
    nameElements.forEach(element => {
        if (baziSet.has(element)) score += 3;
    });
    
    // 性别适合度
    if (gender === 'male') {
        score += Math.random() * 8 - 4;
    } else {
        score += Math.random() * 6 - 3;
    }
    
    return Math.min(100, Math.max(30, Math.round(score)));
}

// 生成详细分析
function generateNameAnalysis_Detail(name, strokes, wuxing, score, focus, gender) {
    const analyses = [];
    
    // 基础姓名分析
    if (score >= 90) {
        analyses.push('您的姓名寓意深远，五行配置极佳，是难得的好名字。');
    } else if (score >= 80) {
        analyses.push('您的姓名整体运势良好，各方面发展较为顺利。');
    } else if (score >= 70) {
        analyses.push('您的姓名基本合格，但某些方面还有提升空间。');
    } else if (score >= 60) {
        analyses.push('您的姓名存在一些不足，建议考虑适当调整。');
    } else {
        analyses.push('您的姓名配置需要改善，建议重新考虑。');
    }
    
    // 五行分析
    const mainElement = wuxing.main;
    const elementAnalysis = {
        '木': '木性主仁，您性格温和善良，富有同情心，适合从事文教、医疗等行业。',
        '火': '火性主礼，您性格热情开朗，具有领导才能，适合从事管理、销售等工作。',
        '土': '土性主信，您性格稳重可靠，做事踏实认真，适合从事实业、建筑等工作。',
        '金': '金性主义，您性格坚毅果断，具有决断力，适合从事金融、执法等工作。',
        '水': '水性主智，您性格聪明机敏，善于思考，适合从事科研、创意等工作。'
    };
    
    analyses.push(elementAnalysis[mainElement]);
    
    // 笔画数理分析
    if (strokes.zongGe % 2 === 1) {
        analyses.push('您的姓名总格为奇数，性格较为主动积极，具有开拓精神。');
    } else {
        analyses.push('您的姓名总格为偶数，性格较为稳重内敛，善于配合他人。');
    }
    
    // 关注重点分析
    if (focus.includes('meaning')) {
        analyses.push('字义分析：您的姓名寓意美好，字字珠玑，体现了深厚的文化底蕴。');
    }
    if (focus.includes('wuxing')) {
        analyses.push('五行分析：您的姓名五行搭配较为合理，有利于平衡个人运势。');
    }
    if (focus.includes('stroke')) {
        analyses.push('笔画分析：您的姓名笔画数理总体吉利，有助于人生发展。');
    }
    if (focus.includes('personality')) {
        analyses.push('性格分析：从姓名可以看出您具有独特的个人魅力和领导潜质。');
    }
    
    return analyses;
}

// 生成建议
function generateNameSuggestions(strokes, wuxing, score, focus) {
    const suggestions = [];
    
    // 基础建议
    if (score >= 85) {
        suggestions.push('您的姓名配置很好，建议保持现有的名字');
        suggestions.push('可以考虑在名片或印章上使用繁体字增强运势');
    } else if (score >= 70) {
        suggestions.push('姓名整体不错，如有条件可微调一下笔画');
        suggestions.push('在重要场合使用全名，避免使用简称');
    } else {
        suggestions.push('建议考虑改名或使用化名改善运势');
        suggestions.push('可以考虑添加字辈或使用艺名');
    }
    
    // 五行建议
    const elementSuggestions = {
        '木': ['多接触绿色植物增旺运势', '适合在东方发展事业'],
        '火': ['穿红色衣物增强能量', '适合在南方发展事业'],
        '土': ['佩戴玉石类饰品', '适合在中央地区发展'],
        '金': ['佩戴金银饰品', '适合在西方发展事业'],
        '水': ['保持环境整洁流通', '适合在北方发展事业']
    };
    
    suggestions.push(...elementSuggestions[wuxing.main]);
    
    return suggestions;
}

// 生成性格特征
function generatePersonality(strokes, wuxing) {
    const personalities = [];
    
    // 根据五行判断性格
    const elementPersonality = {
        '木': ['善良仁慈', '富有同情心', '做事有条理', '适应能力强'],
        '火': ['热情开朗', '积极主动', '具有领导力', '富有创造力'],
        '土': ['稳重踏实', '诚实可靠', '做事认真', '有责任心'],
        '金': ['意志坚强', '行动果断', '有组织能力', '追求完美'],
        '水': ['聪明机智', '善于变通', '富有想象力', '学习能力强']
    };
    
    personalities.push(...elementPersonality[wuxing.main]);
    
    // 根据笔画数补充性格
    if (strokes.total % 3 === 0) {
        personalities.push('具有艺术天赋');
    }
    if (strokes.total % 7 === 0) {
        personalities.push('具有神秘气质');
    }
    
    return personalities.slice(0, 4); // 返回前4个特征
}

// 生成幸运数字
function generateLuckyNumbers(strokes) {
    const base = strokes.zongGe % 10;
    return [base, (base + 3) % 10, (base + 7) % 10].filter(n => n > 0);
}

// 获取幸运颜色
function getLuckyColors(wuxing) {
    const colors = {
        '木': ['绿色', '青色', '蓝色'],
        '火': ['红色', '紫色', '橙色'],
        '土': ['黄色', '棕色', '咖啡色'],
        '金': ['白色', '银色', '金色'],
        '水': ['黑色', '蓝色', '灰色']
    };
    
    return colors[wuxing.main] || ['白色', '灰色'];
}

// 获取最佳职业
function getBestCareer(wuxing, gender) {
    const careers = {
        '木': ['教育工作者', '医生', '园艺师', '作家'],
        '火': ['销售经理', '演艺人员', '广告策划', '律师'],
        '土': ['建筑师', '农业专家', '房地产', '会计师'],
        '金': ['金融分析师', '珠宝设计师', '机械工程师', '警察'],
        '水': ['研究员', 'IT工程师', '航海员', '心理咨询师']
    };
    
    const list = careers[wuxing.main] || ['公务员', '自由职业者'];
    return list.slice(0, 3); // 返回前3个
}

// 显示结果
function showResult(result) {
    const modal = document.getElementById('resultModal');
    modal.style.display = 'flex';
    // 保存结果到本地存储
    localStorage.setItem('namingResult', JSON.stringify(result));
}



// 获取分数描述
function getScoreDescription(score) {
    if (score >= 95) return '姓名极佳';
    if (score >= 85) return '姓名很好';
    if (score >= 75) return '姓名良好';
    if (score >= 65) return '姓名一般';
    if (score >= 55) return '姓名较差';
    return '建议改名';
}

// 关闭结果
function closeResult() {
    const modal = document.getElementById('resultModal');
    modal.style.display = 'none';
}

// 关闭结果弹窗（新的关闭按钮调用）
function closeResultModal() {
    const modal = document.getElementById('resultModal');
    if (modal) {
        modal.style.display = 'none';
        console.log('✅ 结果弹窗已关闭');
    }
}

// 保存结果
function saveResult() {
    const result = localStorage.getItem('namingResult');
    if (result) {
        const blob = new Blob([result], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '姓名详批结果.json';
        a.click();
        URL.revokeObjectURL(url);
    }
}

// 分享结果
function shareResult() {
    const result = JSON.parse(localStorage.getItem('namingResult') || '{}');
    if (result.name) {
        const shareText = `我在易海堂测了姓名详批，${result.name}得分${result.nameScore}分！五行主属性是${result.wuxing?.main}，${getScoreDescription(result.nameScore)}！`;
        
        if (navigator.share) {
            navigator.share({
                title: '姓名详批结果',
                text: shareText,
                url: window.location.href
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareText).then(() => {
                alert('结果已复制到剪贴板');
            });
        }
    }
}

// 页面卸载时清理动画
window.addEventListener('beforeunload', function() {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
});

// ========== AI功能相关函数 ==========

// 启动深度分析
async function startSmartAnalysis(userData, basicResult) {
    // 显示分析区域
    const aiSection = document.getElementById('aiAnalysisSection');
    if (aiSection) {
        aiSection.style.display = 'block';
        setTimeout(() => {
            aiSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 1000);
    }
    if (!nameAnalysisAI) {
        showAIError('分析服务未初始化');
        return;
    }
    try {
        showAILoading();
        // 保存基础分析结果供后续使用
        window.currentBasicResult = basicResult;
        const aiResult = await nameAnalysisAI.analyzeNameWithAI(userData, basicResult);
        displayAIResult(aiResult);
        window.currentResult = { ...basicResult, aiResult: aiResult };
    } catch (error) {
        showAIError(error.message);
    }
}

// 显示AI加载动画
function showAILoading() {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';
    
    // 模拟加载步骤
    const steps = ['aiStep1', 'aiStep2', 'aiStep3', 'aiStep4'];
    const statusTexts = [
        '正在分析字义内涵...',
        '正在计算五行配置...',
        '正在预测人生运势...',
        '正在生成专业建议...'
    ];
    
    let currentStep = 0;
    
    const updateStep = () => {
        // 清除之前的状态
        steps.forEach(stepId => {
            const stepEl = document.getElementById(stepId);
            if (stepEl) {
                stepEl.classList.remove('active', 'completed');
            }
        });
        
        // 标记完成的步骤
        for (let i = 0; i < currentStep; i++) {
            const stepEl = document.getElementById(steps[i]);
            if (stepEl) {
                stepEl.classList.add('completed');
            }
        }
        
        // 标记当前步骤
        if (currentStep < steps.length) {
            const currentEl = document.getElementById(steps[currentStep]);
            if (currentEl) {
                currentEl.classList.add('active');
            }
            
            // 更新状态文本
            const statusEl = document.querySelector('.ai-loading-text');
            if (statusEl) {
                statusEl.textContent = statusTexts[currentStep];
            }
        }
        
        currentStep++;
        
        if (currentStep <= steps.length) {
            setTimeout(updateStep, 2000);
        }
    };
    
    updateStep();
}

// 显示AI分析结果
function displayAIResult(aiResult) {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');

    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';

        // 适配新的JSON格式
        let analysisData;
        if (aiResult.nameAnalysis) {
            // 新格式
            analysisData = aiResult.nameAnalysis;
        } else if (aiResult.aiAnalysis) {
            // 兼容旧格式
            analysisData = aiResult.aiAnalysis;
        } else {
            // 直接传入分析数据
            analysisData = aiResult;
        }

        // 如果分析数据中没有基础信息，尝试从全局数据中获取
        if (!analysisData.strokes && window.currentBasicResult) {
            analysisData.strokes = window.currentBasicResult.strokes;
        }
        if (!analysisData.wuxing && window.currentBasicResult) {
            analysisData.wuxing = window.currentBasicResult.wuxing;
        }

        contentDiv.innerHTML = generateSmartContentHTML(analysisData);
    }
}

// 生成智能内容HTML
function generateSmartContentHTML(aiAnalysis) {
    let html = '';

    // 获取评分（适配新旧格式）
    const score = aiAnalysis.basic?.score || aiAnalysis.score;
    if (score) {
        html += `
            <div class="ai-section ai-summary">
                <div class="ai-score-display">
                    <div class="ai-score-circle">${score}分</div>
                    <div class="ai-score-info">
                        <div class="ai-score-label">智能评分</div>
                        <div class="ai-score-desc">${getAIScoreDescription(score)}</div>
                    </div>
                </div>
            </div>
        `;
    }

    // 显示三才数理分析
    if (aiAnalysis.strokes) {
        html += `
            <div class="ai-section">
                <h4>🔢 三才数理分析</h4>
                <div class="strokes-grid">
                    <div class="stroke-item">
                        <span class="stroke-label">天格：</span>
                        <span class="stroke-value">${aiAnalysis.strokes.tianGe}</span>
                    </div>
                    <div class="stroke-item">
                        <span class="stroke-label">人格：</span>
                        <span class="stroke-value">${aiAnalysis.strokes.renGe}</span>
                    </div>
                    <div class="stroke-item">
                        <span class="stroke-label">地格：</span>
                        <span class="stroke-value">${aiAnalysis.strokes.diGe}</span>
                    </div>
                    <div class="stroke-item">
                        <span class="stroke-label">外格：</span>
                        <span class="stroke-value">${aiAnalysis.strokes.waiGe}</span>
                    </div>
                    <div class="stroke-item">
                        <span class="stroke-label">总格：</span>
                        <span class="stroke-value">${aiAnalysis.strokes.zongGe}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 显示五行配置
    if (aiAnalysis.wuxing) {
        html += `
            <div class="ai-section">
                <h4>⚖️ 五行配置</h4>
                <div class="wuxing-info">
                    ${aiAnalysis.wuxing.name ? `
                        <div class="wuxing-item">
                            <span class="wuxing-label">姓名五行：</span>
                            <span class="wuxing-value">${aiAnalysis.wuxing.name.join(' → ')}</span>
                        </div>
                    ` : ''}
                    ${aiAnalysis.wuxing.bazi ? `
                        <div class="wuxing-item">
                            <span class="wuxing-label">八字五行：</span>
                            <span class="wuxing-value">${Object.values(aiAnalysis.wuxing.bazi).join(' ')}</span>
                        </div>
                    ` : ''}
                    ${aiAnalysis.wuxing.main ? `
                        <div class="wuxing-item">
                            <span class="wuxing-label">主要属性：</span>
                            <span class="wuxing-value">${aiAnalysis.wuxing.main}</span>
                        </div>
                    ` : ''}
                    ${aiAnalysis.wuxing.balance ? `
                        <div class="wuxing-item">
                            <span class="wuxing-label">平衡状况：</span>
                            <span class="wuxing-value">${aiAnalysis.wuxing.balance}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    if (aiAnalysis.summary) {
        html += `
            <div class="ai-section ai-summary">
                <h4>📋 智能摘要</h4>
                <p>${aiAnalysis.summary}</p>
            </div>
        `;
    }
    if (aiAnalysis.sections) {
        const sectionTitles = {
            overall: '🎯 姓名综合评价',
            meaning: '💎 字义深度解析',
            character: '👤 性格命运预测',
            personality: '👤 性格命运预测', // 兼容旧格式
            career: '💼 事业财运指导',
            marriage: '❤️ 感情婚姻运势',
            love: '❤️ 感情婚姻运势', // 兼容旧格式
            health: '🏥 健康运势评估',
            enhancement: '💡 改名优化建议',
            suggestions: '💡 改名优化建议', // 兼容旧格式
            guidance: '🌟 人生发展指导',
            general: '🔮 综合分析'
        };
        Object.keys(aiAnalysis.sections).forEach(key => {
            const content = aiAnalysis.sections[key];
            if (content && content.trim()) {
                html += `
                    <div class="ai-section">
                        <h4>${sectionTitles[key] || '📊 分析内容'}</h4>
                        <p>${content}</p>
                    </div>
                `;
            }
        });
    }

    // 显示建议
    if (aiAnalysis.recommendations && aiAnalysis.recommendations.length > 0) {
        html += `
            <div class="ai-section">
                <h4>💡 专业建议</h4>
                <ul class="recommendations-list">
                    ${aiAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // 显示开运要素
    if (aiAnalysis.luckyElements) {
        html += `<div class="ai-section"><h4>🍀 开运要素</h4><div class="lucky-elements-grid">`;

        if (aiAnalysis.luckyElements.colors && aiAnalysis.luckyElements.colors.length > 0) {
            html += `
                <div class="lucky-element-item">
                    <span class="lucky-label">🎨 幸运颜色：</span>
                    <span class="lucky-values">${aiAnalysis.luckyElements.colors.join('、')}</span>
                </div>
            `;
        }

        if (aiAnalysis.luckyElements.numbers && aiAnalysis.luckyElements.numbers.length > 0) {
            html += `
                <div class="lucky-element-item">
                    <span class="lucky-label">🔢 幸运数字：</span>
                    <span class="lucky-values">${aiAnalysis.luckyElements.numbers.join('、')}</span>
                </div>
            `;
        }

        if (aiAnalysis.luckyElements.directions && aiAnalysis.luckyElements.directions.length > 0) {
            html += `
                <div class="lucky-element-item">
                    <span class="lucky-label">🧭 有利方位：</span>
                    <span class="lucky-values">${aiAnalysis.luckyElements.directions.join('、')}</span>
                </div>
            `;
        }

        if (aiAnalysis.luckyElements.stones && aiAnalysis.luckyElements.stones.length > 0) {
            html += `
                <div class="lucky-element-item">
                    <span class="lucky-label">💎 幸运宝石：</span>
                    <span class="lucky-values">${aiAnalysis.luckyElements.stones.join('、')}</span>
                </div>
            `;
        }

        html += `</div></div>`;
    }

    return html;
}

// 获取AI评分描述
function getAIScoreDescription(score) {
    if (score >= 90) return '评估：卓越姓名';
    if (score >= 80) return '评估：优秀姓名';
    if (score >= 70) return '评估：良好姓名';
    if (score >= 60) return '评估：一般姓名';
    return '评估：需要改善';
}

// 显示AI错误
function showAIError(errorMessage) {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';
        contentDiv.innerHTML = `
            <div class="ai-section" style="background: rgba(220, 53, 69, 0.2); border-left-color: #dc3545;">
                <h4>⚠️ 分析暂时不可用</h4>
                <p>原因：${errorMessage}</p>
                <p>已为您提供基础的姓名学分析，如需深度分析，请稍后重试或联系客服。</p>
            </div>
        `;
    }
}

// ====== 宝宝日期选择器功能 ======

// 初始化宝宝日期选择器
function initBabyDatePicker() {
    try {
        // 初始化当前年月
        const today = new Date();
        currentYear = today.getFullYear();
        currentMonth = today.getMonth() + 1;
        
        // 检查必要的DOM元素是否存在
        const babyDatePicker = document.getElementById('babyDatePicker');
        const babyDateModal = document.getElementById('babyDateModal');
        const calendarGrid = document.getElementById('calendarGrid');
        
        console.log('🗓️ 宝宝日期选择器初始化检查:');
        console.log('- babyDatePicker:', !!babyDatePicker);
        console.log('- babyDateModal:', !!babyDateModal);
        console.log('- calendarGrid:', !!calendarGrid);
        console.log('- currentYear:', currentYear);
        console.log('- currentMonth:', currentMonth);
        
        if (!babyDatePicker) {
            console.error('❌ 找不到宝宝日期选择器元素');
        }
        if (!babyDateModal) {
            console.error('❌ 找不到宝宝日期选择器模态框');
        }
        if (!calendarGrid) {
            console.error('❌ 找不到日历网格元素');
        }
        
        console.log('✅ 宝宝日期选择器初始化完成');
    } catch (error) {
        console.error('❌ 宝宝日期选择器初始化失败:', error);
    }
}

// 打开宝宝日期选择器
function openBabyDatePicker() {
    console.log('🔍 尝试打开宝宝日期选择器...');
    
    const modal = document.getElementById('babyDateModal');
    const babyDatePicker = document.getElementById('babyDatePicker');
    
    console.log('- modal元素:', !!modal);
    console.log('- babyDatePicker元素:', !!babyDatePicker);
    
    if (modal) {
        modal.style.display = 'flex';
        
        // 生成日历
        generateCalendar();
        updateMonthDisplay();
        
        console.log('📅 宝宝日期选择器已打开');
    } else {
        console.error('❌ 找不到宝宝日期选择器模态框');
        alert('日期选择器初始化失败，请刷新页面重试');
    }
}

// 关闭宝宝日期选择器
function closeBabyDatePicker() {
    const modal = document.getElementById('babyDateModal');
    
    if (modal) {
        modal.style.display = 'none';
        console.log('📅 宝宝日期选择器已关闭');
    }
}

// 上一个月
function prevMonth() {
    currentMonth--;
    if (currentMonth < 1) {
        currentMonth = 12;
        currentYear--;
    }
    generateCalendar();
    updateMonthDisplay();
    console.log('📅 切换到上月:', currentYear + '年' + currentMonth + '月');
}

// 下一个月
function nextMonth() {
    currentMonth++;
    if (currentMonth > 12) {
        currentMonth = 1;
        currentYear++;
    }
    generateCalendar();
    updateMonthDisplay();
    console.log('📅 切换到下月:', currentYear + '年' + currentMonth + '月');
}

// 生成日历
function generateCalendar() {
    const calendarGrid = document.getElementById('calendarGrid');
    if (!calendarGrid) return;
    
    calendarGrid.innerHTML = '';
    
    // 获取当前月份第一天
    const firstDay = new Date(currentYear, currentMonth - 1, 1);
    // 获取当前月份最后一天
    const lastDay = new Date(currentYear, currentMonth, 0);
    // 获取上个月最后一天
    const prevLastDay = new Date(currentYear, currentMonth - 1, 0);
    
    // 计算第一天是星期几（0=周日，1=周一...）
    let firstDayOfWeek = firstDay.getDay();
    if (firstDayOfWeek === 0) firstDayOfWeek = 7; // 调整为周一开始
    
    // 添加上个月的日期
    for (let i = firstDayOfWeek - 1; i > 0; i--) {
        const day = prevLastDay.getDate() - i + 1;
        const dayElement = createDayElement(day, true, currentMonth === 1 ? currentYear - 1 : currentYear, currentMonth === 1 ? 12 : currentMonth - 1);
        calendarGrid.appendChild(dayElement);
    }
    
    // 添加当前月份的日期
    for (let day = 1; day <= lastDay.getDate(); day++) {
        const dayElement = createDayElement(day, false, currentYear, currentMonth);
        calendarGrid.appendChild(dayElement);
    }
    
    // 添加下个月的日期（填满42格）
    const totalCells = 42;
    const filledCells = calendarGrid.children.length;
    const remainingCells = totalCells - filledCells;
    
    for (let day = 1; day <= remainingCells; day++) {
        const dayElement = createDayElement(day, true, currentMonth === 12 ? currentYear + 1 : currentYear, currentMonth === 12 ? 1 : currentMonth + 1);
        calendarGrid.appendChild(dayElement);
    }
}

// 创建日期元素
function createDayElement(day, isOtherMonth, year, month) {
    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day';
    dayElement.textContent = day;
    dayElement.dataset.year = year;
    dayElement.dataset.month = month;
    dayElement.dataset.day = day;
    
    if (isOtherMonth) {
        dayElement.classList.add('other-month');
    }
    
    // 检查是否为今天
    const today = new Date();
    if (year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate()) {
        dayElement.classList.add('today');
    }
    
    // 检查是否为选中日期
    if (selectedDate && selectedDate.year === year && selectedDate.month === month && selectedDate.day === day) {
        dayElement.classList.add('selected');
    }
    
    // 添加点击事件
    dayElement.addEventListener('click', () => {
        selectDate(year, month, day);
    });
    
    return dayElement;
}

// 更新月份显示
function updateMonthDisplay() {
    const monthDisplay = document.getElementById('currentMonthDisplay');
    if (monthDisplay) {
        monthDisplay.textContent = `${currentYear}年${currentMonth.toString().padStart(2, '0')}月`;
    }
}

// 选择日期
function selectDate(year, month, day) {
    // 更新选中的日期
    selectedDate = { year, month, day };
    
    // 更新隐藏字段
    const birthDate = document.getElementById('birthDate');
    const selectedYear = document.getElementById('selectedYear');
    const selectedMonth = document.getElementById('selectedMonth');
    const selectedDay = document.getElementById('selectedDay');
    
    if (birthDate) {
        birthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }
    if (selectedYear) selectedYear.value = year;
    if (selectedMonth) selectedMonth.value = month;
    if (selectedDay) selectedDay.value = day;
    
    // 更新显示
    updateDateDisplay();
    updateModalDateDisplay();
    
    // 重新生成日历以更新选中状态
    generateCalendar();
    
    console.log('📅 选择日期:', year + '年' + month + '月' + day + '日');
    
    // 延迟关闭模态框，让用户看到选中效果
    setTimeout(() => {
        closeBabyDatePicker();
    }, 300);
}

// 清除日期
function clearDate() {
    selectedDate = null;
    
    // 清除隐藏字段
    const birthDate = document.getElementById('birthDate');
    const selectedYear = document.getElementById('selectedYear');
    const selectedMonth = document.getElementById('selectedMonth');
    const selectedDay = document.getElementById('selectedDay');
    
    if (birthDate) birthDate.value = '';
    if (selectedYear) selectedYear.value = '';
    if (selectedMonth) selectedMonth.value = '';
    if (selectedDay) selectedDay.value = '';
    
    // 更新显示
    updateDateDisplay();
    updateModalDateDisplay();
    
    // 重新生成日历
    generateCalendar();
    
    console.log('🗑️ 日期已清除');
    
    // 延迟关闭模态框
    setTimeout(() => {
        closeBabyDatePicker();
    }, 300);
}

// 选择今天
function selectToday() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const day = today.getDate();
    
    // 跳转到今天所在的月份
    currentYear = year;
    currentMonth = month;
    
    // 更新选中的日期（不要调用selectDate，避免双重关闭）
    selectedDate = { year, month, day };
    
    // 更新隐藏字段
    const birthDate = document.getElementById('birthDate');
    const selectedYear = document.getElementById('selectedYear');
    const selectedMonth = document.getElementById('selectedMonth');
    const selectedDay = document.getElementById('selectedDay');
    
    if (birthDate) {
        birthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }
    if (selectedYear) selectedYear.value = year;
    if (selectedMonth) selectedMonth.value = month;
    if (selectedDay) selectedDay.value = day;
    
    // 更新显示
    updateDateDisplay();
    updateModalDateDisplay();
    updateMonthDisplay();
    generateCalendar();
    
    console.log('📅 选择今天:', year + '年' + month + '月' + day + '日');
    
    // 延迟关闭模态框
    setTimeout(() => {
        closeBabyDatePicker();
    }, 500);
}

// 确认日期选择
function confirmDate() {
    if (selectedDate) {
        // 更新时辰选择
        const hourSelect = document.getElementById('birthHourSelect');
        if (hourSelect && selectedHour) {
            hourSelect.value = selectedHour;
        }
        
        // 关闭模态框
        closeBabyDatePicker();
        
        console.log('✅ 确认选择:', selectedDate.year + '年' + selectedDate.month + '月' + selectedDate.day + '日');
    } else {
        alert('请先选择一个日期');
    }
}

// 更新日期显示（主界面）
function updateDateDisplay() {
    const dateDisplayText = document.getElementById('dateDisplayText');
    if (dateDisplayText) {
        if (selectedDate) {
            const { year, month, day } = selectedDate;
            dateDisplayText.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
            dateDisplayText.classList.add('selected');
        } else {
            dateDisplayText.textContent = '请选择日期';
            dateDisplayText.classList.remove('selected');
        }
    }
}

// 更新模态框日期显示
function updateModalDateDisplay() {
    const modalDateDisplay = document.getElementById('modalDateDisplay');
    if (modalDateDisplay) {
        if (selectedDate) {
            const { year, month, day } = selectedDate;
            modalDateDisplay.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
            modalDateDisplay.classList.add('selected');
        } else {
            modalDateDisplay.textContent = '请选择日期';
            modalDateDisplay.classList.remove('selected');
        }
    }
}

// 显示年份选择器
function showYearSelector() {
    const yearSelector = document.getElementById('yearSelector');
    const calendarContainer = document.querySelector('.calendar-container');
    const calendarFooter = document.querySelector('.calendar-footer');
    const monthNavigation = document.querySelector('.month-navigation');
    
    if (yearSelector) {
        yearSelector.style.display = 'block';
        if (calendarContainer) calendarContainer.style.display = 'none';
        if (calendarFooter) calendarFooter.style.display = 'none';
        if (monthNavigation) monthNavigation.style.display = 'none';
        
        generateYearGrid();
        updateYearRangeDisplay();
    }
}

// 隐藏年份选择器
function hideYearSelector() {
    const yearSelector = document.getElementById('yearSelector');
    const calendarContainer = document.querySelector('.calendar-container');
    const calendarFooter = document.querySelector('.calendar-footer');
    const monthNavigation = document.querySelector('.month-navigation');
    
    if (yearSelector) {
        yearSelector.style.display = 'none';
        if (calendarContainer) calendarContainer.style.display = 'block';
        if (calendarFooter) calendarFooter.style.display = 'flex';
        if (monthNavigation) monthNavigation.style.display = 'flex';
    }
}

// 改变年份范围
function changeYearRange(delta) {
    yearRangeStart += delta;
    if (yearRangeStart < 1900) yearRangeStart = 1900;
    if (yearRangeStart > 2100) yearRangeStart = 2100;
    
    generateYearGrid();
    updateYearRangeDisplay();
}

// 更新年份范围显示
function updateYearRangeDisplay() {
    const yearRangeDisplay = document.getElementById('yearRangeDisplay');
    if (yearRangeDisplay) {
        yearRangeDisplay.textContent = `${yearRangeStart}-${yearRangeStart + 9}`;
    }
}

// 生成年份网格
function generateYearGrid() {
    const yearGrid = document.getElementById('yearGrid');
    if (!yearGrid) return;
    
    yearGrid.innerHTML = '';
    const currentYearValue = new Date().getFullYear();
    
    for (let i = 0; i < 10; i++) {
        const year = yearRangeStart + i;
        const yearItem = document.createElement('div');
        yearItem.className = 'year-item';
        yearItem.textContent = year;
        yearItem.dataset.year = year;
        
        // 添加当前年份标记
        if (year === currentYearValue) {
            yearItem.classList.add('current');
        }
        
        // 添加选中年份标记
        if (year === currentYear) {
            yearItem.classList.add('selected');
        }
        
        // 添加点击事件
        yearItem.addEventListener('click', () => {
            selectYear(year);
        });
        
        yearGrid.appendChild(yearItem);
    }
}

// 选择年份
function selectYear(year) {
    currentYear = year;
    
    // 更新月份显示
    updateMonthDisplay();
    
    // 重新生成日历
    generateCalendar();
    
    // 隐藏年份选择器
    hideYearSelector();
}