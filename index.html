<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>易海堂算命网 - 专业在线算命平台</title>
    <meta name="description" content="易海堂算命网，提供八字测算、星座运势、起名测名、大师亲算等专业服务">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components/header.css">
    <link rel="stylesheet" href="css/components/banner.css">
    <link rel="stylesheet" href="css/components/services.css">
    <link rel="stylesheet" href="components/bottom-nav/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 顶部导航和功能按钮 -->
    <header class="header">
        <div class="nav-container">
            <div class="nav-item">
                <img src="images/logo.png" alt="易海堂" class="logo-icon">
            </div>
            <div class="nav-item active" data-target="home">首页</div>
            <div class="nav-item" data-target="zodiac-animals">生肖</div>
            <div class="nav-item" data-target="naming">起名</div>
            <div class="nav-item master-nav" data-target="master">大师亲算</div>
            <div class="nav-item" onclick="window.location.href='pages/profile/index.html'">
                <span class="user-icon">👤</span>
                <span>个人中心</span>
            </div>
        </div>
    </header>

    <!-- 轮播图区域 -->
    <section id="home" class="banner-carousel">
        <div class="carousel-container">
            <div class="carousel-wrapper" id="carouselWrapper">
                <div class="carousel-item active">
                    <img src="images/banner/banner1.png" alt="姓名详批 - 超吉避凶 好运一整年" class="banner-image">
                    <div class="banner-content name-banner">
                        <div class="banner-text">
                            <h2>姓名详批</h2>
                            <p>超吉避凶 好运一整年</p>
                            <a href="#" class="banner-btn">查看更多 →</a>
                        </div>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="images/banner/banner2.png" alt="生肖运势 - 2025年运程详解" class="banner-image">
                    <div class="banner-content zodiac-banner">
                        <div class="banner-text">
                            <h2>生肖运势</h2>
                            <p>2025年运程详解</p>
                            <a href="pages/zodiac/index.html" class="banner-btn">立即测算 →</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 轮播控制按钮 -->
            <button class="carousel-control prev" id="prevBtn">‹</button>
            <button class="carousel-control next" id="nextBtn">›</button>
        </div>

        <!-- 轮播指示器 -->
        <div class="carousel-dots">
            <span class="dot active" data-slide="0"></span>
            <span class="dot" data-slide="1"></span>
        </div>
    </section>

    <!-- 精选服务区域 -->
    <section id="hot" class="featured-services">
        <div class="section-header">
            <h2 class="section-title">✨ 精选服务</h2>
        </div>
        
        <div class="services-container">
            <div class="service-item" data-service="bazi">
                <div class="service-bg red-gold">
                    <div class="service-icon">
                        <span class="icon-symbol">☯️</span>
                    </div>
                    <div class="service-info">
                        <h3>八字精批</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">吉</span>
                    </div>
                </div>
            </div>

            <div class="service-item" data-service="marriage">
                <div class="service-bg pink-red">
                    <div class="service-icon">
                        <span class="icon-symbol">💕</span>
                    </div>
                    <div class="service-info">
                        <h3>八字合婚</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">喜</span>
                    </div>
                </div>
            </div>

            <div class="service-item" data-service="baby-name">
                <div class="service-bg orange-yellow">
                    <div class="service-icon">
                        <span class="icon-symbol">👶</span>
                    </div>
                    <div class="service-info">
                        <h3>宝宝起名</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">福</span>
                    </div>
                </div>
            </div>

            <div class="service-item" data-service="portrait">
                <div class="service-bg purple-blue">
                    <div class="service-icon">
                        <span class="icon-symbol">💝</span>
                    </div>
                    <div class="service-info">
                        <h3>姻缘画像</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">缘</span>
                    </div>
                </div>
            </div>

            <div class="service-item" data-service="fortune">
                <div class="service-bg green-emerald">
                    <div class="service-icon">
                        <span class="icon-symbol">💰</span>
                    </div>
                    <div class="service-info">
                        <h3>财运预测</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">财</span>
                    </div>
                </div>
            </div>

            <div class="service-item" data-service="name-analysis">
                <div class="service-bg blue-cyan">
                    <div class="service-icon">
                        <span class="icon-symbol">🔍</span>
                    </div>
                    <div class="service-info">
                        <h3>姓名详批</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">名</span>
                    </div>
                </div>
            </div>

            <div class="service-item" data-service="zodiac">
                <div class="service-bg gold-orange">
                    <div class="service-icon">
                        <span class="icon-symbol">🐉</span>
                    </div>
                    <div class="service-info">
                        <h3>生肖运势</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">旺</span>
                    </div>
                </div>
            </div>

            <div class="service-item" data-service="phone">
                <div class="service-bg teal-green">
                    <div class="service-icon">
                        <span class="icon-symbol">📱</span>
                    </div>
                    <div class="service-info">
                        <h3>手机号测吉凶</h3>
                    </div>
                    <div class="service-decoration">
                        <span class="lucky-char">顺</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提示信息 -->
        <div class="service-notice">
            <p>🎉 新年钜惠，专业测算！</p>
        </div>
    </section>



    <!-- 年度运势专题美化版 -->
    <section class="fortune-2025-card">
      <div class="fortune-canvas-container">
        <canvas id="yearlyFortuneCanvas" width="360" height="260"></canvas>
        </div>
      <div class="fortune-info">
        <h2 class="fortune-title">2025蛇年运势</h2>
        <div class="fortune-subtitle">开启你的好运年</div>
      </div>
    </section>
    
    <!-- 生肖算命 -->
    <section class="zodiac-animals">
        <div class="section-header">
            <h3>生肖算命</h3>
            <span class="more">更多</span>
        </div>
        <div class="animals-grid">
            <div class="animal-item">
                <div class="animal-icon purple">🐭</div>
                <span>鼠</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon yellow">🐮</div>
                <span>牛</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon orange">🐯</div>
                <span>虎</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon blue">🐰</div>
                <span>兔</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon pink">🐲</div>
                <span>龙</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon green">🐍</div>
                <span>蛇</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon red">🐴</div>
                <span>马</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon brown">🐑</div>
                <span>羊</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon gold">🐵</div>
                <span>猴</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon cyan">🐔</div>
                <span>鸡</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon indigo">🐶</div>
                <span>狗</span>
            </div>
            <div class="animal-item">
                <div class="animal-icon violet">🐷</div>
                <span>猪</span>
            </div>
        </div>
    </section>

    <!-- 起名测名 -->
    <section id="naming" class="naming-section">
        <div class="section-header">
            <h3>起名测名</h3>
            <span class="more">更多</span>
        </div>
        <div class="naming-tags">
            <span class="name-tag" onclick="openBoyNaming()">男孩名字</span>
            <span class="name-tag" onclick="openGirlNaming()">女孩名字</span>
            <span class="name-tag" onclick="openNameAnalysis()">姓名详批</span>
            <span class="name-tag featured" onclick="openNameMatching()">姓名配对</span>
        </div>
        <div class="naming-promo">
            <div class="promo-background">
                <div class="decorative-elements">
                    <div class="star star-1">⭐</div>
                    <div class="star star-2">✨</div>
                    <div class="star star-3">🌟</div>
                    <div class="heart heart-1">💖</div>
                    <div class="heart heart-2">💝</div>
                </div>
                
                <div class="promo-content">
                    <div class="content-wrapper">
                        <div class="promo-text">
                            <h4>好名字成就宝宝</h4>
                            <p class="subtitle">一生幸福、顺遂</p>
                            <div class="promo-features">
                                <div class="feature-item">
                                    <span class="feature-icon">🎯</span>
                                    <span>平衡八字五行，有利成长</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-icon">📚</span>
                                    <span>引经据典，雕琢字形音义</span>
                                </div>
                            </div>
                            
                            <div class="cta-button">
                                <button class="name-consultation-btn" onclick="window.location.href='pages/baby-naming/index.html'">
                                    <span class="btn-text">专业起名咨询</span>
                                    <span class="btn-arrow">→</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 综合测算特色区域 -->
    <section id="master" class="comprehensive-calculation">
        <div class="comp-header">
            <h3>⌘ 综合测算 ⌘</h3>
        </div>
        
        <!-- 月老红线何时牵 -->
        <div class="special-calculation single-calc">
            <div class="calc-bg purple-gradient">
                <h4>月老红线何时牵</h4>
                <h2>正缘什么时候出现？</h2>
                <button class="calc-btn pink">求姻缘</button>
            </div>
        </div>

        <!-- 推荐测算列表 -->
        <div class="calculation-list">

            <div class="calc-item">
                <div class="calc-icon">
                    <!-- 使用emoji代替不存在的图片 -->
                    <span style="font-size: 2em;">💕</span>
                </div>
                <div class="calc-content">
                    <h4>婚前合婚，选对伴侣</h4>
                    <p>八字合婚，选对伴侣；婚后合婚，守住婚姻？</p>
                    <div class="calc-stats">
                        <span class="views">👥 25.10万人查看</span>
                    </div>
                </div>
                <button class="calc-action red">立即测算</button>
            </div>

            <div class="calc-item">
                <div class="calc-icon">
                    <!-- 使用emoji代替不存在的图片 -->
                    <span style="font-size: 2em;">📝</span>
                </div>
                <div class="calc-content">
                    <h4>好意、好记的吉祥美名！</h4>
                    <p>精选100套好听、好意、好记的吉祥美名！</p>
                    <div class="calc-stats">
                        <span class="views">👥 11.25万人查看</span>
                    </div>
                </div>
                <button class="calc-action red">立即起名</button>
            </div>

            <div class="calc-item">
                <div class="calc-icon">
                    <!-- 使用emoji代替不存在的图片 -->
                    <span style="font-size: 2em;">💼</span>
                </div>
                <div class="calc-content">
                    <h4>解读你的事业财富？</h4>
                    <p>感情婚姻、健康变化！</p>
                    <div class="calc-stats">
                        <span class="views">👥 38.78万人查看</span>
                    </div>
                </div>
                <button class="calc-action red">立即测算</button>
            </div>

            <div class="calc-item">
                <div class="calc-icon">
                    <!-- 使用emoji代替不存在的图片 -->
                    <span style="font-size: 2em;">💰</span>
                </div>
                <div class="calc-content">
                    <h4>你想成为有钱人吗？</h4>
                    <p>八字财运告诉你发财机会！</p>
                    <div class="calc-stats">
                        <span class="views">👥 45.68万人查看</span>
                    </div>
                </div>
                <button class="calc-action red" onclick="window.location.href='pages/wealth/index.html'">立即测算</button>
            </div>

            <div class="calc-item">
                <div class="calc-icon">
                    <!-- 使用emoji代替不存在的图片 -->
                    <span style="font-size: 2em;">🐍</span>
                </div>
                <div class="calc-content">
                    <h4>2025年八字精批？</h4>
                    <p>2025运势、批算事业财运、感情婚姻？</p>
                    <div class="calc-stats">
                        <span class="views">👥 26.64万人查看</span>
                    </div>
                </div>
                <button class="calc-action red">立即测算</button>
            </div>

            <div class="calc-item">
                <div class="calc-icon">
                    <!-- 使用emoji代替不存在的图片 -->
                    <span style="font-size: 2em;">🔮</span>
                </div>
                <div class="calc-content">
                    <h4>姻缘画像生成？</h4>
                    <p>描绘理想另一半，寻找真爱缘分</p>
                    <div class="calc-stats">
                        <span class="views">👥 8.89万人查看</span>
                    </div>
                </div>
                <button class="calc-action red" onclick="window.location.href='pages/tarot/index.html'">立即测算</button>
            </div>
        </div>
    </section>

    <!-- 底部联系信息 -->
    <footer class="footer">
        <div class="contact-info">
            <p>商务微信：18088245313</p>
        </div>
        <div class="copyright">
            <p>Copyright 2025 乙巳蛇年2025年运势运程-易海堂在线生辰八字算命婚姻配对最准的网站</p>
        </div>
    </footer>

    <!-- 底部导航栏 -->
    <nav class="bottom-nav">
        <div class="nav-item active" onclick="scrollToTop()">
            <span class="nav-icon">🏠</span>
            <span class="nav-text">首页</span>
        </div>
        <a href="pages/order/index.html" class="nav-item">
            <i class="nav-icon">📋</i>
            <span class="nav-text">订单查询</span>
        </a>
        <a href="pages/profile/index.html" class="nav-item">
            <span class="nav-icon">👤</span>
            <span class="nav-text">个人中心</span>
        </a>
    </nav>

    <!-- 宝宝起名模态框 -->
    <div id="babyNameModal" class="modal-overlay baby-name-modal">
        <div class="modal-container">
            <div class="modal-header baby-header">
                <h2>👶 宝宝起名</h2>
                <span class="close-btn" onclick="closeBabyNameModal()">&times;</span>
            </div>
            
            <div class="modal-content">
                <!-- Canvas动画背景 -->
                <canvas id="babyNameCanvas" class="baby-name-canvas"></canvas>
                
                <div class="baby-name-form-container">
                    <div class="form-title">
                        <h3>🍼 为宝贝取个好名字</h3>
                        <p>美好寓意，伴随一生</p>
                    </div>
                    
                    <form class="baby-name-form" id="babyNameForm">
                        <!-- 父母姓氏 -->
                        <div class="input-group">
                            <label for="parentSurname">
                                <span class="label-icon">👨‍👩‍👧‍👦</span>
                                <span class="label-text">父母姓氏</span>
                            </label>
                            <input type="text" id="parentSurname" name="parentSurname" placeholder="请输入宝宝的姓氏" required>
                        </div>
                        
                        <!-- 宝宝性别 -->
                        <div class="input-group">
                            <label>
                                <span class="label-icon">👶</span>
                                <span class="label-text">宝宝性别</span>
                            </label>
                            <div class="gender-options baby-gender">
                                <label class="gender-option">
                                    <input type="radio" name="babyGender" value="male">
                                    <span class="gender-btn male">
                                        <span class="gender-icon">👦</span>
                                        <span>男宝</span>
                                    </span>
                                </label>
                                <label class="gender-option">
                                    <input type="radio" name="babyGender" value="female">
                                    <span class="gender-btn female">
                                        <span class="gender-icon">👧</span>
                                        <span>女宝</span>
                                    </span>
                                </label>
                                <label class="gender-option">
                                    <input type="radio" name="babyGender" value="unknown">
                                    <span class="gender-btn unknown">
                                        <span class="gender-icon">🤱</span>
                                        <span>未知</span>
                                    </span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- 宝宝出生日期 -->
                        <div class="input-group">
                            <label for="babyBirthDate">
                                <span class="label-icon">📅</span>
                                <span class="label-text">宝宝出生日期</span>
                            </label>
                            <input type="date" 
                                   id="babyBirthDate" 
                                   name="babyBirthDate" 
                                   required
                                   min="2020-01-01"
                                   max="2030-12-31">
                        </div>
                        
                        <!-- 出生时辰 -->
                        <div class="input-group">
                            <label for="babyBirthHour">
                                <span class="label-icon">⏰</span>
                                <span class="label-text">出生时辰</span>
                            </label>
                            <select id="babyBirthHour" name="babyBirthHour" required>
                                <option value="">选择时辰</option>
                                <option value="zi">子时 (23:00-01:00)</option>
                                <option value="chou">丑时 (01:00-03:00)</option>
                                <option value="yin">寅时 (03:00-05:00)</option>
                                <option value="mao">卯时 (05:00-07:00)</option>
                                <option value="chen">辰时 (07:00-09:00)</option>
                                <option value="si">巳时 (09:00-11:00)</option>
                                <option value="wu">午时 (11:00-13:00)</option>
                                <option value="wei">未时 (13:00-15:00)</option>
                                <option value="shen">申时 (15:00-17:00)</option>
                                <option value="you">酉时 (17:00-19:00)</option>
                                <option value="xu">戌时 (19:00-21:00)</option>
                                <option value="hai">亥时 (21:00-23:00)</option>
                            </select>
                        </div>
                        
                        <!-- 起名要求 -->
                        <div class="input-group">
                            <label>
                                <span class="label-icon">✨</span>
                                <span class="label-text">起名要求（可选）</span>
                            </label>
                            <div class="name-preferences">
                                <div class="preference-item">
                                    <input type="checkbox" id="classic" name="nameStyle" value="classic">
                                    <label for="classic" class="preference-label">古典雅致</label>
                                </div>
                                <div class="preference-item">
                                    <input type="checkbox" id="modern" name="nameStyle" value="modern">
                                    <label for="modern" class="preference-label">现代时尚</label>
                                </div>
                                <div class="preference-item">
                                    <input type="checkbox" id="literary" name="nameStyle" value="literary">
                                    <label for="literary" class="preference-label">文艺书香</label>
                                </div>
                                <div class="preference-item">
                                    <input type="checkbox" id="auspicious" name="nameStyle" value="auspicious">
                                    <label for="auspicious" class="preference-label">吉祥如意</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 避免用字 -->
                        <div class="input-group">
                            <label for="avoidChars">
                                <span class="label-icon">🚫</span>
                                <span class="label-text">避免用字（可选）</span>
                            </label>
                            <input type="text" id="avoidChars" name="avoidChars" placeholder="请输入不希望使用的字，如：明、华、丽">
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="submit-section">
                            <button type="submit" class="submit-btn baby-submit">
                                <span class="btn-icon">🎁</span>
                                <span>开始起名</span>
        
                            </button>
                            <p class="submit-note">💝 为宝宝起一个美好的名字，陪伴成长每一天</p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 八字精批模态框 -->
    <div id="baziModal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h2>八字精批</h2>
                <span class="close-btn" onclick="closeBaziModal()">&times;</span>
            </div>
            
            <div class="modal-content">
                <!-- Canvas动画背景 -->
                <canvas id="baziCanvas" class="bazi-canvas"></canvas>
                
                <div class="bazi-form-container">
                    <div class="form-title">
                        <h3>📿 请输入您的生辰信息</h3>
                        <p>准确的生辰八字，助您洞察命运玄机</p>
                    </div>
                    
                    <form class="bazi-form" id="baziForm">
                        <!-- 姓名输入 -->
                        <div class="input-group">
                            <label for="userName">
                                <span class="label-icon">👤</span>
                                <span class="label-text">姓名</span>
                            </label>
                            <input type="text" id="userName" name="userName" placeholder="请输入您的姓名" required>
                        </div>
                        
                        <!-- 性别选择 -->
                        <div class="input-group">
                            <label>
                                <span class="label-icon">👥</span>
                                <span class="label-text">性别</span>
                            </label>
                            <div class="gender-options">
                                <label class="gender-option">
                                    <input type="radio" name="gender" value="male" required>
                                    <span class="gender-btn male">
                                        <span class="gender-icon">♂</span>
                                        <span>男</span>
                                    </span>
                                </label>
                                <label class="gender-option">
                                    <input type="radio" name="gender" value="female" required>
                                    <span class="gender-btn female">
                                        <span class="gender-icon">♀</span>
                                        <span>女</span>
                                    </span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- 出生年份 -->
                        <div class="input-group">
                            <label for="birthYear">
                                <span class="label-icon">📅</span>
                                <span class="label-text">出生年份</span>
                            </label>
                            <select id="birthYear" name="birthYear" required>
                                <option value="">请选择年份</option>
                            </select>
                        </div>
                        
                        <!-- 出生月份 -->
                        <div class="input-group">
                            <label for="birthMonth">
                                <span class="label-icon">🌙</span>
                                <span class="label-text">出生月份</span>
                            </label>
                            <select id="birthMonth" name="birthMonth" required>
                                <option value="">请选择月份</option>
                                <option value="1">正月 (1月)</option>
                                <option value="2">二月 (2月)</option>
                                <option value="3">三月 (3月)</option>
                                <option value="4">四月 (4月)</option>
                                <option value="5">五月 (5月)</option>
                                <option value="6">六月 (6月)</option>
                                <option value="7">七月 (7月)</option>
                                <option value="8">八月 (8月)</option>
                                <option value="9">九月 (9月)</option>
                                <option value="10">十月 (10月)</option>
                                <option value="11">十一月 (11月)</option>
                                <option value="12">腊月 (12月)</option>
                            </select>
                        </div>
                        
                        <!-- 出生日期 -->
                        <div class="input-group">
                            <label for="birthDay">
                                <span class="label-icon">🌅</span>
                                <span class="label-text">出生日期</span>
                            </label>
                            <select id="birthDay" name="birthDay" required>
                                <option value="">请选择日期</option>
                            </select>
                        </div>
                        
                        <!-- 出生时辰 -->
                        <div class="input-group">
                            <label for="birthHour">
                                <span class="label-icon">⏰</span>
                                <span class="label-text">出生时辰</span>
                            </label>
                            <select id="birthHour" name="birthHour" required>
                                <option value="">请选择时辰</option>
                                <option value="zi">子时 (23:00-01:00)</option>
                                <option value="chou">丑时 (01:00-03:00)</option>
                                <option value="yin">寅时 (03:00-05:00)</option>
                                <option value="mao">卯时 (05:00-07:00)</option>
                                <option value="chen">辰时 (07:00-09:00)</option>
                                <option value="si">巳时 (09:00-11:00)</option>
                                <option value="wu">午时 (11:00-13:00)</option>
                                <option value="wei">未时 (13:00-15:00)</option>
                                <option value="shen">申时 (15:00-17:00)</option>
                                <option value="you">酉时 (17:00-19:00)</option>
                                <option value="xu">戌时 (19:00-21:00)</option>
                                <option value="hai">亥时 (21:00-23:00)</option>
                            </select>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="submit-section">
                            <button type="submit" class="submit-btn">
                                <span class="btn-icon">✨</span>
                                <span class="btn-text">开始八字精批</span>
        
                            </button>
                            <p class="submit-note">
                                🔮 专业大师解读，洞察命运奥秘
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 八字合婚模态框 -->
    <div id="marriageModal" class="modal-overlay">
        <div class="modal-container marriage-modal">
            <div class="modal-header marriage-header">
                <h2>八字合婚</h2>
                <span class="close-btn" onclick="closeMarriageModal()">&times;</span>
            </div>
            
            <div class="modal-content">
                <!-- Canvas动画背景 -->
                <canvas id="marriageCanvas" class="marriage-canvas"></canvas>
                
                <div class="marriage-form-container">
                    <div class="form-title">
                        <h3>💕 请输入双方生辰信息</h3>
                        <p>测算两人姻缘，洞察爱情奥秘</p>
                    </div>
                    
                    <form class="marriage-form" id="marriageForm">
                        <!-- 男方信息 -->
                        <div class="person-section male-section">
                            <div class="person-header">
                                <span class="person-icon">♂</span>
                                <h4>男方信息</h4>
                            </div>
                            
                            <div class="input-group">
                                <label for="maleName">
                                    <span class="label-icon">👤</span>
                                    <span class="label-text">姓名</span>
                                </label>
                                <input type="text" id="maleName" name="maleName" placeholder="请输入男方姓名" required>
                            </div>
                            
                            <div class="input-group">
                                <label for="maleBirthDate">
                                    <span class="label-icon">📅</span>
                                    <span class="label-text">出生年月日</span>
                                </label>
                                <input type="date" 
                                       id="maleBirthDate" 
                                       name="maleBirthDate" 
                                       required
                                       min="1950-01-01"
                                       max="2030-12-31">
                            </div>
                            
                            <div class="input-group">
                                <label for="maleBirthHour">
                                    <span class="label-icon">⏰</span>
                                    <span class="label-text">出生时辰</span>
                                </label>
                                <select id="maleBirthHour" name="maleBirthHour" required>
                                    <option value="">选择时辰</option>
                                    <option value="zi">子时 (23:00-01:00)</option>
                                    <option value="chou">丑时 (01:00-03:00)</option>
                                    <option value="yin">寅时 (03:00-05:00)</option>
                                    <option value="mao">卯时 (05:00-07:00)</option>
                                    <option value="chen">辰时 (07:00-09:00)</option>
                                    <option value="si">巳时 (09:00-11:00)</option>
                                    <option value="wu">午时 (11:00-13:00)</option>
                                    <option value="wei">未时 (13:00-15:00)</option>
                                    <option value="shen">申时 (15:00-17:00)</option>
                                    <option value="you">酉时 (17:00-19:00)</option>
                                    <option value="xu">戌时 (19:00-21:00)</option>
                                    <option value="hai">亥时 (21:00-23:00)</option>
                                </select>
                            </div>
                        </div>

                        <!-- 分隔线 -->
                        <div class="divider">
                            <span class="divider-icon">💕</span>
                        </div>

                        <!-- 女方信息 -->
                        <div class="person-section female-section">
                            <div class="person-header">
                                <span class="person-icon">♀</span>
                                <h4>女方信息</h4>
                            </div>
                            
                            <div class="input-group">
                                <label for="femaleName">
                                    <span class="label-icon">👤</span>
                                    <span class="label-text">姓名</span>
                                </label>
                                <input type="text" id="femaleName" name="femaleName" placeholder="请输入女方姓名" required>
                            </div>
                            
                            <div class="input-group">
                                <label for="femaleBirthDate">
                                    <span class="label-icon">📅</span>
                                    <span class="label-text">出生年月日</span>
                                </label>
                                <input type="date" 
                                       id="femaleBirthDate" 
                                       name="femaleBirthDate" 
                                       required
                                       min="1950-01-01"
                                       max="2030-12-31">
                            </div>
                            
                            <div class="input-group">
                                <label for="femaleBirthHour">
                                    <span class="label-icon">⏰</span>
                                    <span class="label-text">出生时辰</span>
                                </label>
                                <select id="femaleBirthHour" name="femaleBirthHour" required>
                                    <option value="">选择时辰</option>
                                    <option value="zi">子时 (23:00-01:00)</option>
                                    <option value="chou">丑时 (01:00-03:00)</option>
                                    <option value="yin">寅时 (03:00-05:00)</option>
                                    <option value="mao">卯时 (05:00-07:00)</option>
                                    <option value="chen">辰时 (07:00-09:00)</option>
                                    <option value="si">巳时 (09:00-11:00)</option>
                                    <option value="wu">午时 (11:00-13:00)</option>
                                    <option value="wei">未时 (13:00-15:00)</option>
                                    <option value="shen">申时 (15:00-17:00)</option>
                                    <option value="you">酉时 (17:00-19:00)</option>
                                    <option value="xu">戌时 (19:00-21:00)</option>
                                    <option value="hai">亥时 (21:00-23:00)</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="submit-section">
                            <button type="submit" class="submit-btn marriage-submit">
                                <span class="btn-icon">💕</span>
                                <span class="btn-text">开始合婚测算</span>
        
                            </button>
                            <p class="submit-note">
                                💖 专业合婚分析，解读姻缘奥秘
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/modules/auth-service.js"></script>
    <script src="js/main.js"></script>
    <script src="js/modules/carousel.js"></script>
    <!-- 认证服务 -->
    <script src="js/modules/unified-auth-service.js"></script>
    <script src="js/modules/member-service.js"></script>
    <script src="js/modules/auth-service.js"></script>
    <!-- 订单和支付服务 -->
    <script src="js/modules/api-order.js"></script>
    <script src="js/modules/order-payment.js"></script>
    <!-- 业务模块 -->
    <script src="js/modules/services.js"></script>
    <script src="js/modules/bazi.js"></script>
    <script src="js/modules/marriage.js"></script>
    <script src="js/modules/baby-name.js"></script>
    <script src="js/modules/yearly-fortune-canvas.js"></script>
    <script src="components/bottom-nav/script.js"></script>
    <script>
        // 加载底部导航
        fetch('components/bottom-nav/index.html')
            .then(response => response.text())
            .then(html => {
                document.getElementById('bottom-nav-container').innerHTML = html;
            });
        
        // 初始化年度运势Canvas
        document.addEventListener('DOMContentLoaded', function() {
            // 创建Canvas实例并保存到全局变量
            window.yearlyFortuneCanvas = new YearlyFortuneCanvas('yearlyFortuneCanvas');
            
            // 更新页面上的年份和标题
            const currentYear = new Date().getFullYear();
            const zodiacAnimals = [
                { name: '鼠', years: [2020, 2032, 2044] },
                { name: '牛', years: [2021, 2033, 2045] },
                { name: '虎', years: [2022, 2034, 2046] },
                { name: '兔', years: [2023, 2035, 2047] },
                { name: '龙', years: [2024, 2036, 2048] },
                { name: '蛇', years: [2025, 2037, 2049] },
                { name: '马', years: [2026, 2038, 2050] },
                { name: '羊', years: [2027, 2039, 2051] },
                { name: '猴', years: [2028, 2040, 2052] },
                { name: '鸡', years: [2029, 2041, 2053] },
                { name: '狗', years: [2030, 2042, 2054] },
                { name: '猪', years: [2031, 2043, 2055] }
            ];
            
            const currentZodiac = zodiacAnimals.find(animal => 
                animal.years.includes(currentYear)
            ) || zodiacAnimals[0];
            
            // 更新徽章和标题
            const yearBadge = document.getElementById('yearBadge');
            const fortuneTitle = document.getElementById('fortuneTitle');
            
            if (yearBadge) {
                yearBadge.textContent = `${currentYear}${currentZodiac.name}年开运版`;
            }
            
            if (fortuneTitle) {
                fortuneTitle.textContent = `${currentYear}${currentZodiac.name}年运势`;
            }
            });
    </script>
</body>
</html> 