/* 轮播图组件样式 */
.banner-carousel {
    margin: 20px 15px 20px;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    position: relative;
}

.carousel-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.carousel-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-item {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-item.active {
    opacity: 1;
}

.banner-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

/* 轮播内容样式 */
.banner-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    z-index: 2;
}

.banner-text {
    position: relative;
    z-index: 3;
    text-align: center;
    color: #8B4513;
}

.banner-text h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(255,255,255,0.8);
    animation: slideInLeft 0.8s ease-out;
}

.banner-text p {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    animation: slideInLeft 0.8s ease-out 0.2s both;
}

.banner-btn {
    display: inline-block;
    padding: 12px 24px;
    background: rgba(139, 69, 19, 0.8);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    animation: slideInLeft 0.8s ease-out 0.4s both;
}

.banner-btn:hover {
    background: rgba(139, 69, 19, 1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
}

.banner-decoration {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    opacity: 0.7;
    animation: float 3s ease-in-out infinite;
}

.banner-decoration img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

/* 圆点指示器 */
.carousel-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 10px;
    background: var(--bg-light);
    position: relative;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #DDD;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.dot::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-pink);
    transition: left 0.3s ease;
}

.dot.active::before {
    left: 0;
}

.dot.active {
    background: var(--primary-pink);
    transform: scale(1.2);
}

.dot:hover {
    transform: scale(1.1);
    background: rgba(255, 105, 180, 0.6);
}

/* 轮播控制按钮 */
.carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 45px;
    height: 45px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #8B4513;
    opacity: 0.8;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.banner-carousel:hover .carousel-control {
    opacity: 1;
}

.carousel-control.prev {
    left: 15px;
}

.carousel-control.next {
    right: 15px;
}

.carousel-control:hover {
    background: rgba(255,255,255,1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

/* 进度条 */
.carousel-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-pink);
    transition: width 3s linear;
    width: 0;
}

.carousel-item.active .carousel-progress {
    width: 100%;
}

/* 动画效果 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(-50%) translateX(0);
    }
    50% {
        transform: translateY(-50%) translateX(5px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 触摸滑动指示 */
.carousel-container::after {
    content: '← 滑动查看更多 →';
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: rgba(139,69,19,0.6);
    pointer-events: none;
    opacity: 0;
    animation: fadeInOut 3s ease-in-out infinite;
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

/* 响应式适配 */
@media (max-width: 480px) {
    .banner-carousel {
        margin: 20px 10px 15px;
    }

    .carousel-container {
        height: 160px;
    }

    .banner-content {
        padding: 15px;
    }

    .banner-text h2 {
        font-size: 22px;
    }

    .banner-text p {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .banner-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .carousel-control {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .carousel-control.prev {
        left: 10px;
    }

    .carousel-control.next {
        right: 10px;
    }
}

@media (max-width: 360px) {
    .carousel-container {
        height: 140px;
    }

    .banner-text h2 {
        font-size: 20px;
    }

    .banner-text p {
        font-size: 14px;
    }

    .banner-btn {
        padding: 8px 16px;
        font-size: 13px;
    }
}