<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生肖运势JSON格式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .json-output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐍 生肖运势JSON格式测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 测试AI模块初始化</div>
            <button class="test-btn" onclick="testAIInitialization()">测试初始化</button>
            <div id="init-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 测试本地分析JSON格式</div>
            <button class="test-btn" onclick="testLocalAnalysis()">测试本地分析</button>
            <div id="local-status" class="status" style="display: none;"></div>
            <div id="local-output" class="json-output" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 测试AI分析JSON格式</div>
            <button class="test-btn" onclick="testAIAnalysis()">测试AI分析</button>
            <div id="ai-status" class="status" style="display: none;"></div>
            <div id="ai-output" class="json-output" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 测试JSON解析</div>
            <button class="test-btn" onclick="testJSONParsing()">测试解析</button>
            <div id="parse-status" class="status" style="display: none;"></div>
            <div id="parse-output" class="json-output" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入AI模块 -->
    <script src="js/modules/ai-service.js?v=1.2"></script>
    <script src="js/modules/zodiac-ai.js?v=1.2"></script>
    
    <script>
        // AI配置
        window.AI_CONFIG = {
            SERVICE_TYPE: 'deepseek',
            DEEPSEEK_API_KEY: '***********************************',
            DEEPSEEK_BASE_URL: 'https://api.deepseek.com/v1',
            DEBUG: true
        };

        let aiService = null;
        let zodiacFortuneAI = null;

        // 测试数据
        const testUserData = {
            zodiac: 'snake',
            birthYear: 2001,
            birthMonth: 6,
            gender: 'female'
        };

        const testBasicAnalysis = {
            overall: { score: 85 },
            career: { score: 88 },
            wealth: { score: 82 },
            love: { score: 90 },
            health: { score: 78 }
        };

        // 测试AI模块初始化
        function testAIInitialization() {
            const statusDiv = document.getElementById('init-status');
            statusDiv.style.display = 'block';
            
            try {
                // 初始化AI服务
                if (typeof AIService !== 'undefined') {
                    aiService = new AIService(window.AI_CONFIG);
                    window.aiService = aiService;
                }
                
                // 初始化生肖AI
                if (typeof ZodiacFortuneAI !== 'undefined') {
                    zodiacFortuneAI = new ZodiacFortuneAI();
                    window.zodiacFortuneAI = zodiacFortuneAI;
                }
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ AI模块初始化成功';
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ AI模块初始化失败: ' + error.message;
            }
        }

        // 测试本地分析
        function testLocalAnalysis() {
            const statusDiv = document.getElementById('local-status');
            const outputDiv = document.getElementById('local-output');
            
            statusDiv.style.display = 'block';
            outputDiv.style.display = 'block';
            
            try {
                if (!zodiacFortuneAI) {
                    throw new Error('生肖AI模块未初始化');
                }
                
                const result = zodiacFortuneAI.generateLocalFortune(testUserData, testBasicAnalysis);
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 本地分析成功，返回JSON格式';
                
                outputDiv.textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 本地分析失败: ' + error.message;
                outputDiv.textContent = error.stack;
            }
        }

        // 测试AI分析
        async function testAIAnalysis() {
            const statusDiv = document.getElementById('ai-status');
            const outputDiv = document.getElementById('ai-output');
            
            statusDiv.style.display = 'block';
            outputDiv.style.display = 'block';
            
            try {
                if (!zodiacFortuneAI) {
                    throw new Error('生肖AI模块未初始化');
                }
                
                statusDiv.className = 'status';
                statusDiv.textContent = '🔄 正在进行AI分析...';
                
                const result = await zodiacFortuneAI.analyzeZodiacFortuneWithAI(testUserData, testBasicAnalysis);
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ AI分析成功，返回JSON格式';
                
                outputDiv.textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ AI分析失败: ' + error.message;
                outputDiv.textContent = error.stack;
            }
        }

        // 测试JSON解析
        function testJSONParsing() {
            const statusDiv = document.getElementById('parse-status');
            const outputDiv = document.getElementById('parse-output');
            
            statusDiv.style.display = 'block';
            outputDiv.style.display = 'block';
            
            try {
                // 模拟AI返回的JSON响应
                const mockAIResponse = `{
                    "basic": {
                        "zodiac": "snake",
                        "zodiacName": "蛇",
                        "gender": "female",
                        "birthYear": 2001,
                        "birthMonth": 6,
                        "element": "火",
                        "score": 85
                    },
                    "sections": {
                        "overall": "2025年对于生肖蛇的朋友来说是一个充满机遇的年份...",
                        "career": "事业方面将迎来重要的转机和发展机会...",
                        "wealth": "财运方面表现稳定，有不错的收入增长...",
                        "love": "感情运势温和，单身者有望遇到心仪对象...",
                        "health": "健康状况良好，但需要注意劳逸结合..."
                    },
                    "scores": {
                        "overall": 85,
                        "career": 88,
                        "wealth": 82,
                        "love": 90,
                        "health": 78
                    },
                    "recommendations": [
                        "佩戴红色饰品增强运势",
                        "多朝南方发展事业",
                        "与生肖鸡、牛的人多合作"
                    ],
                    "luckyElements": {
                        "colors": ["红色", "黄色"],
                        "numbers": [2, 8, 9],
                        "directions": ["南方", "西南方"],
                        "months": [3, 6, 9]
                    }
                }`;
                
                const parsed = JSON.parse(mockAIResponse);
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ JSON解析成功';
                
                outputDiv.textContent = JSON.stringify(parsed, null, 2);
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ JSON解析失败: ' + error.message;
                outputDiv.textContent = error.stack;
            }
        }

        // 页面加载时自动测试初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testAIInitialization, 500);
        });
    </script>
</body>
</html>
