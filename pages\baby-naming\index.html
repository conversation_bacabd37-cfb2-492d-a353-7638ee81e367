<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>👶 宝宝起名 - 易海堂算命网</title>
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../js/modules/ai-config-manager.js"></script>
    <!-- 认证服务 -->
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script src="../../js/modules/auth-service.js"></script>
    <!-- 认证初始化 -->
    <script src="../../js/modules/auth-init.js"></script>
    <!-- 订单和支付服务 -->
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <!-- AI服务 -->
    <script src="../../js/modules/ai-service.js"></script>
    <!-- 业务模块 -->
    <script src="../../js/modules/baby-name.js"></script>
    <style>
        :root {
            /* 颜色变量 */
            --primary-color: #FF69B4;
            --secondary-color: #FF1493;
            --primary-gradient: linear-gradient(135deg, #FFB6C1, #FFC0CB);
            --header-gradient: linear-gradient(135deg, #FF69B4, #FF1493, #DC143C);
            --pink-gradient: linear-gradient(135deg, #FFB6C1, #FFC0CB, #FFE4E1);
            
            /* 阴影效果 */
            --common-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
            --light-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            
            /* 动画过渡 */
            --transition-default: all 0.3s ease;
            
            /* 圆角 */
            --border-radius-sm: 6px;
            --border-radius-md: 12px;
            --border-radius-lg: 15px;
            --border-radius-xl: 25px;
            
            /* 间距 */
            --spacing-xs: 5px;
            --spacing-sm: 8px;
            --spacing-md: 15px;
            --spacing-lg: 20px;
            --spacing-xl: 25px;
        }

        /* 通用卡片样式 */
        .common-card {
            background: var(--primary-gradient);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            margin: var(--spacing-md) 0;
            position: relative;
            overflow: hidden;
        }

        /* 通用按钮样式 */
        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-sm);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-default);
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: #007AFF;
        }

        /* 通用输入框样式 */
        .input-common {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 105, 180, 0.3);
            border-radius: var(--border-radius-md);
            padding: 12px 15px;
            font-size: 14px;
            color: #2C1810;
            transition: var(--transition-default);
            width: 100%;
            box-sizing: border-box;
        }

        .input-common:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--common-shadow);
        }

        /* 通用标题样式 */
        .section-title {
            color: var(--secondary-color);
            font-size: 20px;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
        }

        /* 通用flex布局 */
        .flex-center {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flex-between {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .flex-column {
            display: flex;
            flex-direction: column;
        }

        /* 通用间距类 */
        .gap-sm { gap: var(--spacing-sm); }
        .gap-md { gap: var(--spacing-md); }
        .gap-lg { gap: var(--spacing-lg); }

        .mt-sm { margin-top: var(--spacing-sm); }
        .mt-md { margin-top: var(--spacing-md); }
        .mt-lg { margin-top: var(--spacing-lg); }

        .mb-sm { margin-bottom: var(--spacing-sm); }
        .mb-md { margin-bottom: var(--spacing-md); }
        .mb-lg { margin-bottom: var(--spacing-lg); }

        /* 通用动画类 */
        .hover-scale {
            transition: var(--transition-default);
        }

        .hover-scale:hover {
            transform: scale(1.05);
        }

        /* 通用阴影类 */
        .shadow-sm { box-shadow: var(--light-shadow); }
        .shadow-md { box-shadow: var(--common-shadow); }

        body {
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB, #FFE4E1);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .baby-naming-container {
            position: relative;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .baby-naming-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .baby-naming-content {
            position: relative;
            z-index: 2;
            padding: 0;
        }
        
        .baby-header {
            background: linear-gradient(135deg, #FF69B4, #FF1493, #DC143C);
            color: white;
            text-align: center;
            padding: 20px;
            position: relative;
        }
        
        .baby-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 40%),
                radial-gradient(circle at 80% 80%, rgba(255,192,203,0.2) 0%, transparent 40%);
        }
        
        .baby-header h1 {
            margin: 0;
            font-size: 24px;
            position: relative;
            z-index: 1;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            z-index: 2;
        }
        
        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .form-container {
            padding: 25px 20px;
            max-width: 500px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin-top: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 182, 193, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(255, 105, 180, 0.1);
        }
        
        .form-title {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-title h3 {
            color: #FF1493;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 0 10px rgba(255, 20, 147, 0.3);
        }
        
        .form-title p {
            color: #FF69B4;
            font-size: 14px;
            margin: 0;
        }
        
        .baby-name-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .input-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #FF1493;
            font-size: 14px;
            font-weight: 600;
        }
        
        .label-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }
        
        .input-group input,
        .input-group select {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 105, 180, 0.3);
            border-radius: 12px;
            padding: 12px 15px;
            font-size: 14px;
            color: #2C1810;
            transition: all 0.3s ease;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
            width: 100%;
            box-sizing: border-box;
        }
        
        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: #FF69B4;
            box-shadow: 
                inset 0 2px 5px rgba(0, 0, 0, 0.1),
                0 0 15px rgba(255, 105, 180, 0.3);
            transform: translateY(-1px);
        }
        
        .gender-options {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .gender-option {
            flex: 1;
        }
        
        .gender-option input[type="radio"] {
            display: none;
        }
        
        .gender-btn {
            min-height: 60px;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 105, 180, 0.3);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 5px;
            width: 100%;
            padding: 15px 10px;
            box-sizing: border-box;
        }
        
        .gender-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
        }
        
        .gender-btn.unknown {
            border-left: 4px solid #FFB6C1;
        }
        
        .gender-option input[type="radio"]:checked + .gender-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border-color: #FF1493;
            box-shadow: 0 5px 20px rgba(255, 105, 180, 0.4);
        }
        
        .gender-option input[type="radio"]:checked + .gender-btn.unknown {
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
            color: #333;
        }
        
        .gender-icon {
            font-size: 24px;
            font-weight: 700;
        }
        
        .name-preferences {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 8px;
        }
        
        .preference-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .preference-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #FF69B4;
            border-radius: 3px;
        }
        
        .preference-label {
            font-size: 13px;
            color: #2C1810;
            cursor: pointer;
            user-select: none;
        }
        
        .preference-label:hover {
            color: #FF1493;
        }
        
        .submit-section {
            margin-top: 20px;
            text-align: center;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1, #FFC0CB);
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            color: white;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }
        
        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }
        
        .submit-btn:hover::before {
            left: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 105, 180, 0.6);
        }
        
        .btn-icon {
            font-size: 18px;
            animation: babySparkle 2s ease-in-out infinite;
        }
        
        @keyframes babySparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.2) rotate(-5deg); }
            50% { transform: scale(1) rotate(0deg); }
            75% { transform: scale(1.1) rotate(5deg); }
        }
        
        .btn-price {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 700;
        }
        
        .submit-note {
            color: #FF1493;
            font-size: 12px;
            margin-top: 15px;
            opacity: 0.9;
        }
        
        /* 宝宝日期选择器样式 */
        .baby-date-picker {
            cursor: pointer;
            border: 2px solid #FF69B4;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            min-height: 50px;
        }

        .baby-date-picker:hover {
            border-color: #FF1493;
            box-shadow: 0 0 0 3px rgba(255, 105, 180, 0.1);
        }

        .date-input-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
        }

        .date-placeholder {
            color: #999;
            font-size: 15px;
        }

        .date-placeholder.selected {
            color: #333;
            font-weight: 500;
        }

        .date-picker-btn {
            width: 32px;
            height: 24px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .date-picker-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
        }

        .picker-icon {
            font-size: 14px;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 宝宝日期选择器模态框样式 */
        .baby-date-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 10001;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .baby-modal-container {
            background: white;
            border-radius: 15px;
            width: 100%;
            max-width: 380px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideUp 0.3s ease-out;
        }

        @keyframes modalSlideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 粉色头部 */
        .baby-modal-header {
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-icon {
            font-size: 24px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .baby-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 日期输入框 */
        .date-input-container {
            padding: 20px;
            background: white;
        }

        .date-input-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px 15px;
        }

        .date-value {
            color: #666;
            font-size: 16px;
            font-weight: 500;
        }

        .date-value.selected {
            color: #333;
        }

        .date-confirm-btn {
            width: 36px;
            height: 28px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .date-confirm-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
        }

        .confirm-icon {
            color: white;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 月份导航 */
        .month-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #f0f0f0;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: #666;
        }

        .nav-btn:hover {
            background: #f8f9fa;
            border-color: #bbb;
            color: #333;
        }

        .current-month {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .current-month:hover {
            background: #f8f9fa;
        }

        .month-dropdown {
            font-size: 12px;
            color: #999;
            transition: transform 0.3s ease;
        }

        .current-month:hover .month-dropdown {
            transform: rotate(180deg);
        }

        /* 年份选择器 */
        .year-selector {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            z-index: 10;
            border-radius: 15px;
            overflow: hidden;
        }

        .year-selector-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
            border-bottom: 1px solid #f0f0f0;
        }

        .year-nav-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: white;
            font-weight: bold;
        }

        .year-nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        #yearRangeDisplay {
            font-size: 16px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .year-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            padding: 20px;
            max-height: 280px;
            overflow-y: auto;
        }

        .year-item {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: #333;
            background: #f8f9fa;
            border: 1px solid transparent;
        }

        .year-item:hover {
            background: #FFB6C1;
            color: white;
            transform: scale(1.05);
        }

        .year-item.selected {
            background: #FF69B4;
            color: white;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255, 105, 180, 0.3);
        }

        .year-item.current {
            border-color: #FFB6C1;
            background: rgba(255, 182, 193, 0.1);
            color: #FF69B4;
            font-weight: 600;
        }

        .year-selector-footer {
            display: flex;
            justify-content: center;
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .year-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #FFB6C1;
            color: white;
        }

        .year-btn:hover {
            background: #FF69B4;
            transform: translateY(-1px);
        }

        /* 日历容器 */
        .calendar-container {
            padding: 0 20px 20px;
            background: white;
        }

        /* 星期标题 */
        .week-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 0;
            margin-bottom: 10px;
        }

        .week-day {
            padding: 8px 0;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #666;
        }

        /* 日历网格 */
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #333;
            background: white;
            border: 1px solid transparent;
        }

        .calendar-day:hover {
            background: #f8f9fa;
            border-color: #e0e0e0;
        }

        .calendar-day.other-month {
            color: #ccc;
        }

        .calendar-day.selected {
            background: #FFB6C1;
            color: white;
            font-weight: 600;
        }

        .calendar-day.today {
            border-color: #FFB6C1;
            background: rgba(255, 182, 193, 0.1);
            color: #FF69B4;
            font-weight: 600;
        }

        /* 底部按钮 */
        .calendar-footer {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .footer-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clear-btn {
            background: transparent;
            color: #007AFF;
        }

        .clear-btn:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        .today-btn {
            background: transparent;
            color: #007AFF;
        }

        .today-btn:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        /* 收起箭头 */
        .collapse-arrow {
            display: flex;
            justify-content: center;
            padding: 10px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .collapse-arrow:hover {
            background: #f8f9fa;
        }

        .collapse-arrow span {
            color: #999;
            font-size: 16px;
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .result-content {
                margin: 0;
                border-radius: 0;
            }
            
            .result-header {
                margin: 0;
                border-radius: 0;
            }
            
            .result-actions {
                flex-direction: column;
                gap: 10px;
            }
            
            .action-btn {
                width: 100%;
            }
            
            .name-header {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="baby-naming-container">
        <!-- Canvas动画背景 -->
        <canvas id="babyNamingCanvas" class="baby-naming-canvas"></canvas>
        
        <div class="baby-naming-content">
            <!-- 页面头部 -->
            <header class="baby-header">
                <button class="back-btn" onclick="history.back()">← 返回</button>
                <h1>👶 宝宝起名</h1>
            </header>
            
            <!-- 表单内容 -->
            <div class="form-container">
                <div class="form-title">
                    <h3>🍼 为宝贝取个好名字</h3>
                    <p>美好寓意，伴随一生</p>
                </div>
                
                <form class="baby-name-form" id="babyNameForm">
                    <!-- 父母姓氏 -->
                    <div class="input-group">
                        <label for="parentSurname">
                            <span class="label-icon">👨‍👩‍👧‍👦</span>
                            <span class="label-text">父母姓氏</span>
                        </label>
                        <input type="text" id="parentSurname" name="parentSurname" placeholder="请输入宝宝的姓氏" required>
                    </div>
                    
                    <!-- 宝宝性别 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">👶</span>
                            <span class="label-text">宝宝性别</span>
                        </label>
                        <div class="gender-options baby-gender">
                            <label class="gender-option">
                                <input type="radio" name="babyGender" value="male">
                                <span class="gender-btn male">
                                    <span class="gender-icon">👦</span>
                                    <span>男宝</span>
                                </span>
                            </label>
                            <label class="gender-option">
                                <input type="radio" name="babyGender" value="female">
                                <span class="gender-btn female">
                                    <span class="gender-icon">👧</span>
                                    <span>女宝</span>
                                </span>
                            </label>
                            <label class="gender-option">
                                <input type="radio" name="babyGender" value="unknown">
                                <span class="gender-btn unknown">
                                    <span class="gender-icon">🤱</span>
                                    <span>未知</span>
                                </span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 宝宝出生日期 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">📅</span>
                            <span class="label-text">请选择您的农历出生日期</span>
                        </label>
                        <div class="baby-date-picker" id="babyDatePicker" onclick="openBabyDatePicker()">
                            <div class="date-input-display">
                                <span class="date-placeholder" id="dateDisplayText">请选择日期</span>
                                <div class="date-picker-btn">
                                    <span class="picker-icon">📅</span>
                                </div>
                            </div>
                        </div>
                        <!-- 隐藏的输入字段存储选中的值 -->
                        <input type="hidden" id="babyBirthDate" name="babyBirthDate" required>
                        <input type="hidden" id="selectedYear" name="selectedYear">
                        <input type="hidden" id="selectedMonth" name="selectedMonth">
                        <input type="hidden" id="selectedDay" name="selectedDay">
                    </div>
                    
                    <!-- 出生时辰 -->
                    <div class="input-group">
                        <label for="babyBirthHour">
                            <span class="label-icon">⏰</span>
                            <span class="label-text">出生时辰</span>
                        </label>
                        <select id="babyBirthHour" name="babyBirthHour" required>
                            <option value="">选择时辰</option>
                            <option value="zi">子时 (23:00-01:00)</option>
                            <option value="chou">丑时 (01:00-03:00)</option>
                            <option value="yin">寅时 (03:00-05:00)</option>
                            <option value="mao">卯时 (05:00-07:00)</option>
                            <option value="chen">辰时 (07:00-09:00)</option>
                            <option value="si">巳时 (09:00-11:00)</option>
                            <option value="wu">午时 (11:00-13:00)</option>
                            <option value="wei">未时 (13:00-15:00)</option>
                            <option value="shen">申时 (15:00-17:00)</option>
                            <option value="you">酉时 (17:00-19:00)</option>
                            <option value="xu">戌时 (19:00-21:00)</option>
                            <option value="hai">亥时 (21:00-23:00)</option>
                        </select>
                    </div>
                    
                    <!-- 起名要求 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">✨</span>
                            <span class="label-text">起名要求（可选）</span>
                        </label>
                        <div class="name-preferences">
                            <div class="preference-item">
                                <input type="checkbox" id="classic" name="nameStyle" value="classic">
                                <label for="classic" class="preference-label">古典雅致</label>
                            </div>
                            <div class="preference-item">
                                <input type="checkbox" id="modern" name="nameStyle" value="modern">
                                <label for="modern" class="preference-label">现代时尚</label>
                            </div>
                            <div class="preference-item">
                                <input type="checkbox" id="literary" name="nameStyle" value="literary">
                                <label for="literary" class="preference-label">文艺书香</label>
                            </div>
                            <div class="preference-item">
                                <input type="checkbox" id="auspicious" name="nameStyle" value="auspicious">
                                <label for="auspicious" class="preference-label">吉祥如意</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 避免用字 -->
                    <div class="input-group">
                        <label for="avoidChars">
                            <span class="label-icon">🚫</span>
                            <span class="label-text">避免用字（可选）</span>
                        </label>
                        <input type="text" id="avoidChars" name="avoidChars" placeholder="请输入不希望使用的字，如：明、华、丽">
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="submit-section">
                        <button type="submit" class="submit-btn baby-submit">
                            <span class="btn-icon">🎁</span>
                            <span>开始起名</span>
                        </button>
                        <p class="submit-note">💝 为宝宝起一个美好的名字，陪伴成长每一天</p>
                    </div>
                </form>
            </div>
            
            <!-- 加载动画 -->
            <div class="loading-container" id="loadingContainer" style="display: none;">
                <div class="loading-content">
                    <div class="loading-spinner">🍼</div>
                    <h3>正在为宝宝精心起名...</h3>
                    <p id="loadingStatus">正在分析生辰八字...</p>
                    <div class="loading-steps">
                        <div class="step" id="step1">🔮 分析生辰八字</div>
                        <div class="step" id="step2">⚖️ 计算五行平衡</div>
                        <div class="step" id="step3">🎨 智能起名</div>
                        <div class="step" id="step4">📊 生成详细报告</div>
                    </div>
                </div>
            </div>
            
            <!-- 起名结果显示 -->
            <div class="result-container" id="resultContainer" style="display: none;">
                <div class="result-header">
                    <h2>🎉 宝宝起名结果</h2>
                    <button class="close-result" onclick="closeResult()">×</button>
                </div>
                
                <div class="result-content">
                    <!-- 宝宝信息卡片 -->
                    <div class="baby-info-card">
                        <h3>👶 宝宝信息</h3>
                        <div id="babyInfoDisplay"></div>
                    </div>
                    
                    <!-- 八字分析卡片 -->
                    <div class="bazi-analysis-card">
                        <h3>🔮 生辰八字分析</h3>
                        <div id="baziAnalysisDisplay"></div>
                    </div>
                    
                    <!-- 推荐名字列表 -->
                    <div class="names-list-card">
                        <h3>✨ 精选好名推荐</h3>
                        <div id="namesListDisplay"></div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="result-actions">
                        <button class="action-btn regenerate" onclick="regenerateNames()">
                            🔄 重新起名
                        </button>
                        <button class="action-btn save" onclick="saveReport()">
                            💾 保存报告
                        </button>
                        <button class="action-btn share" onclick="shareReport()">
                            📤 分享结果
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入AI模块 -->
    <script src="../../js/modules/baby-name.js?v=1.2"></script>
    <script src="../../js/modules/ai-service.js?v=1.2"></script>

    <style>
        /* 加载动画样式 */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-content {
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            color: white;
            max-width: 400px;
            width: 90%;
        }
        
        .loading-spinner {
            font-size: 48px;
            animation: spin 2s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-steps {
            margin-top: 25px;
            text-align: left;
        }
        
        .step {
            padding: 8px 0;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }
        
        .step.active {
            opacity: 1;
            color: #FFD700;
        }
        
        .step.completed {
            opacity: 1;
            color: #90EE90;
        }
        
        /* 结果显示样式 */
        .result-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1001;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .result-header {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 auto;
            max-width: 800px;
        }
        
        .close-result {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .close-result:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .result-content {
            background: rgba(255, 255, 255, 0.95);
            max-width: 800px;
            margin: 0 auto;
            border-radius: 0 0 15px 15px;
            padding: 0;
        }
        
        .baby-info-card,
        .bazi-analysis-card,
        .names-list-card,
        .detailed-report-card {
            margin: 0;
            padding: 25px;
            border-bottom: 1px solid rgba(255, 182, 193, 0.3);
        }
        
        .baby-info-card h3,
        .bazi-analysis-card h3,
        .names-list-card h3,
        .detailed-report-card h3 {
            color: #FF1493;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .name-item {
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
            margin: 15px 0;
            padding: 20px;
            border-radius: 12px;
            color: #2C1810;
            position: relative;
            overflow: hidden;
        }
        
        .name-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at top right, rgba(255,255,255,0.3) 0%, transparent 50%);
        }
        
        .name-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }
        
        .name-title {
            font-size: 22px;
            font-weight: bold;
            color: #FF1493;
        }
        
        .name-score {
            background: rgba(255, 20, 147, 0.8);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .name-details {
            position: relative;
            z-index: 1;
        }
        
        .name-detail-row {
            margin: 8px 0;
            display: flex;
            gap: 10px;
        }
        
        .detail-label {
            font-weight: bold;
            color: #FF69B4;
            min-width: 60px;
        }
        
        .detail-value {
            color: #2C1810;
            flex: 1;
        }
        
        .result-actions {
            padding: 25px;
            display: flex;
            gap: 15px;
            justify-content: center;
            background: #f8f9fa;
        }
        
        .action-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.3s ease;
            min-width: 120px;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .action-btn.regenerate {
            background: linear-gradient(135deg, #FF69B4, #FF1493);
            color: white;
        }
        
        .action-btn.save {
            background: linear-gradient(135deg, #32CD32, #228B22);
            color: white;
        }
        
        .action-btn.share {
            background: linear-gradient(135deg, #1E90FF, #0066CC);
            color: white;
        }
        
        /* 移动端适配 */
        @media (max-width: 480px) {
            .result-content {
                margin: 0;
                border-radius: 0;
            }
            
            .result-header {
                margin: 0;
                border-radius: 0;
            }
            
            .result-actions {
                flex-direction: column;
                gap: 10px;
            }
            
            .action-btn {
                width: 100%;
            }
            
            .name-header {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }
    </style>

    <script>
        // 表单管理类
        class BabyNameForm {
            constructor() {
                this.form = document.getElementById('babyNameForm');
                this.loadingContainer = document.getElementById('loadingContainer');
                this.resultContainer = document.getElementById('resultContainer');
                this.currentFormData = null;
                this.currentResult = null;
                
                this.init();
            }
            
            init() {
                if (this.form) {
                    this.form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleSubmit();
                    });
                }
            }
            
            async handleSubmit() {
                const formData = new FormData(this.form);
                
                // 获取必填字段
                const surname = formData.get('parentSurname');
                const gender = formData.get('babyGender');
                const birthDate = formData.get('babyBirthDate');
                const birthHour = formData.get('babyBirthHour');
                
                // 验证必填字段
                if (!this.validateRequiredFields(surname, gender, birthDate, birthHour)) {
                    return;
                }
                
                // 构建请求数据
                this.currentFormData = {
                    surname: surname.trim(),
                    gender: gender,
                    birthDate: birthDate,
                    birthHour: birthHour,
                    nameStyles: formData.getAll('nameStyle'),
                    avoidChars: formData.get('avoidChars')?.trim() || ''
                };
                
                console.log('开始宝宝起名分析:', this.currentFormData);
                
                // 服务配置
                const serviceConfig = {
                    type: 'baby-name',
                    name: '宝宝起名',
                    price: 9.9,
                    description: '根据生辰八字起吉祥好名'
                };

                // 创建订单并支付
                try {
                    await window.orderPaymentManager.createOrderAndPay(
                        serviceConfig,
                        this.currentFormData,
                        // 支付成功回调
                        async (order, paymentResult) => {
                            console.log('支付成功，开始宝宝起名');
                            await this.performBabyNaming();
                        },
                        // 取消支付回调
                        (order) => {
                            console.log('用户取消支付');
                        }
                    );
                } catch (error) {
                    console.error('订单创建失败:', error);
                    alert('创建订单失败，请稍后重试');
                }
            }

            // 执行宝宝起名
            async performBabyNaming() {
                try {
                    this.showLoading();
                    
                    // 检查AI服务状态
                    console.log('检查AI服务状态:', {
                        hasAiService: !!window.aIService,
                        hasBabyNameAI: !!window.babyNameAI,
                        aiConfig: window.AI_CONFIG
                    });
                    
                    if (!window.babyNameAI) {
                        throw new Error('宝宝起名AI服务未初始化');
                    }
                    
                    const result = await babyNameAI.generateNames(this.currentFormData);
                    console.log('起名结果:', result);
                    
                    this.currentResult = result;
                    this.hideLoading();
                    this.showResult(result);
                    
                } catch (error) {
                    console.error('起名失败:', error);
                    this.hideLoading();
                    
                    // 更友好的错误提示
                    let errorMessage = '起名过程中出现错误，请稍后重试';
                    if (error.message.includes('API密钥未设置')) {
                        errorMessage = 'AI服务配置错误，正在使用本地算法为您起名...';
                        // 尝试使用本地算法
                        try {
                            console.log('尝试使用本地算法起名...');
                            const localResult = await babyNameAI.generateLocalNames(this.currentFormData, null, 10);
                            this.currentResult = {
                                names: localResult,
                                report: '使用本地算法生成的名字',
                                generatedAt: new Date().toISOString()
                            };
                            this.showResult(this.currentResult);
                            return;
                        } catch (localError) {
                            console.error('本地算法也失败:', localError);
                            errorMessage = '起名服务暂时不可用，请稍后重试';
                        }
                    }
                    alert(errorMessage);
                }
            }
            
            validateRequiredFields(surname, gender, birthDate, birthHour) {
                if (!surname || !gender || !birthDate || !birthHour) {
                    alert('请填写宝宝的基本信息（姓氏、性别、出生日期和时辰）');
                    return false;
                }
                return true;
            }
            
            showLoading() {
                if (this.loadingContainer) {
                    this.loadingContainer.style.display = 'flex';
                    this.simulateLoadingSteps();
                }
            }
            
            hideLoading() {
                if (this.loadingContainer) {
                    this.loadingContainer.style.display = 'none';
                }
            }
            
            simulateLoadingSteps() {
                const steps = ['step1', 'step2', 'step3', 'step4'];
                const statusTexts = [
                    '正在分析生辰八字...',
                    '正在计算五行平衡...',
                    '正在智能起名...',
                    '正在生成详细报告...'
                ];
                
                let currentStep = 0;
                const updateStep = () => {
                    steps.forEach((stepId, index) => {
                        const stepEl = document.getElementById(stepId);
                        if (stepEl) {
                            stepEl.classList.remove('active', 'completed');
                            if (index < currentStep) {
                                stepEl.classList.add('completed');
                            } else if (index === currentStep) {
                                stepEl.classList.add('active');
                            }
                        }
                    });
                    
                    const statusEl = document.getElementById('loadingStatus');
                    if (statusEl && currentStep < statusTexts.length) {
                        statusEl.textContent = statusTexts[currentStep];
                    }
                    
                    currentStep++;
                    if (currentStep <= steps.length) {
                        setTimeout(updateStep, 1500);
                    }
                };
                
                updateStep();
            }
            
            showResult(result) {
                if (!this.resultContainer) return;
                
                this.displayBabyInfo(result.babyInfo, result.baziAnalysis);
                this.displayBaziAnalysis(result.baziAnalysis);
                this.displayNamesList(result.names);
                
                this.resultContainer.style.display = 'block';
            }
            
            displayBabyInfo(babyInfo, baziAnalysis) {
                const display = document.getElementById('babyInfoDisplay');
                if (!display) return;
                
                const genderText = {
                    male: '男宝宝',
                    female: '女宝宝',
                    unknown: '宝宝'
                }[babyInfo.gender] || '宝宝';
                
                const styleText = babyInfo.nameStyles?.length > 0 ? 
                    babyInfo.nameStyles.map(s => ({
                        classic: '古典雅致',
                        modern: '现代时尚',
                        literary: '文艺书香',
                        auspicious: '吉祥如意'
                    }[s] || s)).join('、') : '无特殊要求';
                
                display.innerHTML = `
                    <div class="flex-between gap-md mb-md">
                        <div><strong>姓氏：</strong>${babyInfo.surname}</div>
                        <div><strong>性别：</strong>${genderText}</div>
                        <div><strong>生辰：</strong>${babyInfo.birthDate}</div>
                        <div><strong>时辰：</strong>${baziAnalysis.hour.name}</div>
                    </div>
                    <div class="mb-sm"><strong>起名风格：</strong>${styleText}</div>
                    ${babyInfo.avoidChars ? `<div><strong>避免用字：</strong>${babyInfo.avoidChars}</div>` : ''}
                `;
            }
            
            displayBaziAnalysis(baziAnalysis) {
                const display = document.getElementById('baziAnalysisDisplay');
                if (display) {
                    display.innerHTML = `
                        <div style="white-space: pre-line; line-height: 1.6;">
                            ${baziAnalysis.analysis}
                        </div>
                    `;
                }
            }
            
            displayNamesList(names) {
                const display = document.getElementById('namesListDisplay');
                if (!display) return;
                
                const nameItems = names.slice(0, 8).map(name => `
                    <div class="common-card">
                        <div class="flex-between mb-sm">
                            <div class="name-title">${name.fullName}</div>
                            <div class="name-score">${name.score}分</div>
                        </div>
                        <div class="flex-column gap-sm">
                            <div class="flex-between">
                                <span class="detail-label">五行:</span>
                                <span class="detail-value">${name.wuxing}</span>
                            </div>
                            <div class="flex-between">
                                <span class="detail-label">寓意:</span>
                                <span class="detail-value">${name.meaning}</span>
                            </div>
                            ${name.reason ? `
                            <div class="flex-between">
                                <span class="detail-label">推荐理由:</span>
                                <span class="detail-value">${name.reason}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('');
                
                display.innerHTML = nameItems;
            }
            
            closeResult() {
                if (this.resultContainer) {
                    this.resultContainer.style.display = 'none';
                }
            }
            
            async regenerateNames() {
                if (!this.currentFormData) {
                    alert('请先填写起名信息');
                    return;
                }
                
                this.closeResult();
                await this.handleSubmit();
            }
            
            saveReport() {
                if (!this.currentResult) {
                    alert('没有可保存的报告');
                    return;
                }
                
                try {
                    const reportText = this.currentResult.report;
                    const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${this.currentResult.babyInfo.surname}宝宝起名报告.txt`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    alert('✅ 报告已保存到下载文件夹');
                } catch (error) {
                    console.error('保存失败:', error);
                    alert('保存失败，请重试');
                }
            }
            
            shareReport() {
                if (!this.currentResult) {
                    alert('没有可分享的报告');
                    return;
                }
                
                const shareText = `🎉 ${this.currentResult.babyInfo.surname}宝宝起名报告\n\n` +
                    `✨ 精选推荐名字：\n${this.currentResult.names.slice(0, 3)
                        .map(name => `• ${name.fullName} (${name.score}分)`)
                        .join('\n')}\n\n` +
                    `💝 更多详细分析，请使用易海堂算命网宝宝起名功能！`;
                
                if (navigator.share) {
                    navigator.share({
                        title: '宝宝起名报告',
                        text: shareText
                    }).catch(console.error);
                } else {
                    navigator.clipboard.writeText(shareText)
                        .then(() => alert('✅ 报告内容已复制到剪贴板，您可以粘贴分享给朋友！'))
                        .catch(() => alert('请手动复制以下内容：\n\n' + shareText));
                }
            }
        }

        // 宝宝起名页面动画管理类
        class BabyNamingPage {
            constructor() {
                this.canvas = null;
                this.ctx = null;
                this.particles = [];
                this.animationId = null;
                
                this.init();
            }
            
            init() {
                try {
                    this.initCanvas();
                    this.generateParticles();
                    this.animateCanvas();
                    console.log('✅ 宝宝起名页面动画初始化成功');
                } catch (error) {
                    console.error('❌ 页面动画初始化失败:', error);
                }
            }
            
            initCanvas() {
                this.canvas = document.getElementById('babyNamingCanvas');
                if (!this.canvas) {
                    console.warn('⚠️ 找不到Canvas元素，跳过动画初始化');
                    return;
                }
                
                this.ctx = this.canvas.getContext('2d');
                
                // 设置canvas尺寸
                const resizeCanvas = () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                };
                
                resizeCanvas();
                window.addEventListener('resize', resizeCanvas);
            }
            
            generateParticles() {
                if (!this.canvas) return;
                
                this.particles = [];
                const particleCount = 30;
                
                for (let i = 0; i < particleCount; i++) {
                    this.particles.push({
                        x: Math.random() * this.canvas.width,
                        y: Math.random() * this.canvas.height,
                        size: Math.random() * 3 + 1,
                        speedX: (Math.random() - 0.5) * 0.5,
                        speedY: (Math.random() - 0.5) * 0.5,
                        opacity: Math.random() * 0.4 + 0.2,
                        color: this.getRandomPinkColor(),
                        symbol: this.getRandomBabySymbol()
                    });
                }
            }
            
            getRandomPinkColor() {
                const colors = [
                    'rgba(255, 182, 193, ',
                    'rgba(255, 192, 203, ',
                    'rgba(255, 105, 180, ',
                    'rgba(255, 20, 147, '
                ];
                return colors[Math.floor(Math.random() * colors.length)];
            }
            
            getRandomBabySymbol() {
                const symbols = ['👶', '🍼', '🎀', '🌸', '💕', '🌟', '✨', '🎈'];
                return symbols[Math.floor(Math.random() * symbols.length)];
            }
            
            animateCanvas() {
                if (!this.canvas || !this.ctx) return;
                
                const animate = () => {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    this.particles.forEach(particle => {
                        // 更新位置
                        particle.x += particle.speedX;
                        particle.y += particle.speedY;
                        
                        // 边界检查
                        if (particle.x < 0 || particle.x > this.canvas.width) {
                            particle.speedX *= -1;
                        }
                        if (particle.y < 0 || particle.y > this.canvas.height) {
                            particle.speedY *= -1;
                        }
                        
                        // 绘制粒子
                        this.ctx.save();
                        this.ctx.globalAlpha = particle.opacity;
                        this.ctx.font = `${particle.size * 8}px Arial`;
                        this.ctx.fillStyle = particle.color + particle.opacity + ')';
                        this.ctx.fillText(particle.symbol, particle.x, particle.y);
                        this.ctx.restore();
                    });
                    
                    this.animationId = requestAnimationFrame(animate);
                };
                
                animate();
            }
            
            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
            }
        }

        // 系统初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 开始初始化宝宝起名系统...');
            
            try {
                // 初始化起名 AI
                window.babyNameAI = new BabyNameAI();
                console.log('✅ 宝宝起名AI初始化成功');
                
                // 初始化认证服务
                window.unifiedAuthService = new UnifiedAuthService();
                window.memberService = new MemberService();
                window.orderPaymentManager = new OrderPaymentManager();
                console.log('✅ 认证服务初始化成功');
                
                // 初始化表单管理
                window.babyNameForm = new BabyNameForm();
                
                // 初始化页面动画
                window.babyNamingPage = new BabyNamingPage();
                
                console.log('🎉 宝宝起名系统初始化完成');
                
            } catch (error) {
                console.error('❌ 系统初始化失败:', error);
                initFallbackMode();
            }
        });

        // 降级模式初始化
        function initFallbackMode() {
            console.log('启动降级模式，使用基础起名功能');
            
            const form = document.getElementById('babyNameForm');
            if (form) {
                form.addEventListener('submit', handleFallbackSubmit);
            }
        }
        
        // 降级模式表单处理
        function handleFallbackSubmit(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            const surname = formData.get('parentSurname');
            const gender = formData.get('babyGender');
            const birthDate = formData.get('babyBirthDate');
            const birthHour = formData.get('babyBirthHour');
            
            if (!surname || !gender || !birthDate || !birthHour) {
                alert('请填写宝宝的基本信息（姓氏、性别、出生日期和时辰）');
                return;
            }
            
            alert('⚠️ 系统升级中，暂时使用简化版功能。完整AI起名功能请稍后重试。');
        }

        // 公共接口函数
        function closeResult() {
            window.babyNameForm?.closeResult();
        }

        function regenerateNames() {
            window.babyNameForm?.regenerateNames();
        }

        function saveReport() {
            window.babyNameForm?.saveReport();
        }

        function shareReport() {
            window.babyNameForm?.shareReport();
        }

        // ====== 宝宝日期选择器功能 ======
        
        // 宝宝日期选择器变量
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth() + 1;
        let selectedDate = null;
        let selectedHour = null;
        // 年份选择器变量
        let yearRangeStart = Math.floor(currentYear / 10) * 10;

        // 初始化宝宝日期选择器
        function initBabyDatePicker() {
            try {
                // 初始化当前年月
                const today = new Date();
                currentYear = today.getFullYear();
                currentMonth = today.getMonth() + 1;
                
                // 检查必要的DOM元素是否存在
                const babyDatePicker = document.getElementById('babyDatePicker');
                const babyDateModal = document.getElementById('babyDateModal');
                const calendarGrid = document.getElementById('calendarGrid');
                
                console.log('🗓️ 宝宝日期选择器初始化检查:');
                console.log('- babyDatePicker:', !!babyDatePicker);
                console.log('- babyDateModal:', !!babyDateModal);
                console.log('- calendarGrid:', !!calendarGrid);
                console.log('- currentYear:', currentYear);
                console.log('- currentMonth:', currentMonth);
                
                if (!babyDatePicker) {
                    console.error('❌ 找不到宝宝日期选择器元素');
                }
                if (!babyDateModal) {
                    console.error('❌ 找不到宝宝日期选择器模态框');
                }
                if (!calendarGrid) {
                    console.error('❌ 找不到日历网格元素');
                }
                
                console.log('✅ 宝宝日期选择器初始化完成');
            } catch (error) {
                console.error('❌ 宝宝日期选择器初始化失败:', error);
            }
        }

        // 打开宝宝日期选择器
        function openBabyDatePicker() {
            console.log('🔍 尝试打开宝宝日期选择器...');
            
            const modal = document.getElementById('babyDateModal');
            const babyDatePicker = document.getElementById('babyDatePicker');
            
            console.log('- modal元素:', !!modal);
            console.log('- babyDatePicker元素:', !!babyDatePicker);
            
            if (modal) {
                modal.style.display = 'flex';
                
                // 生成日历
                generateCalendar();
                updateMonthDisplay();
                
                console.log('📅 宝宝日期选择器已打开');
            } else {
                console.error('❌ 找不到宝宝日期选择器模态框');
                alert('日期选择器初始化失败，请刷新页面重试');
            }
        }

        // 关闭宝宝日期选择器
        function closeBabyDatePicker() {
            const modal = document.getElementById('babyDateModal');
            
            if (modal) {
                modal.style.display = 'none';
                console.log('📅 宝宝日期选择器已关闭');
            }
        }

        // 上一个月
        function prevMonth() {
            currentMonth--;
            if (currentMonth < 1) {
                currentMonth = 12;
                currentYear--;
            }
            generateCalendar();
            updateMonthDisplay();
            console.log('📅 切换到上月:', currentYear + '年' + currentMonth + '月');
        }

        // 下一个月
        function nextMonth() {
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
            generateCalendar();
            updateMonthDisplay();
            console.log('📅 切换到下月:', currentYear + '年' + currentMonth + '月');
        }

        // 生成日历
        function generateCalendar() {
            const calendarGrid = document.getElementById('calendarGrid');
            if (!calendarGrid) return;
            
            calendarGrid.innerHTML = '';
            
            // 获取当前月份第一天
            const firstDay = new Date(currentYear, currentMonth - 1, 1);
            // 获取当前月份最后一天
            const lastDay = new Date(currentYear, currentMonth, 0);
            // 获取上个月最后一天
            const prevLastDay = new Date(currentYear, currentMonth - 1, 0);
            
            // 计算第一天是星期几（0=周日，1=周一...）
            let firstDayOfWeek = firstDay.getDay();
            if (firstDayOfWeek === 0) firstDayOfWeek = 7; // 调整为周一开始
            
            // 添加上个月的日期
            for (let i = firstDayOfWeek - 1; i > 0; i--) {
                const day = prevLastDay.getDate() - i + 1;
                const dayElement = createDayElement(day, true, currentMonth === 1 ? currentYear - 1 : currentYear, currentMonth === 1 ? 12 : currentMonth - 1);
                calendarGrid.appendChild(dayElement);
            }
            
            // 添加当前月份的日期
            for (let day = 1; day <= lastDay.getDate(); day++) {
                const dayElement = createDayElement(day, false, currentYear, currentMonth);
                calendarGrid.appendChild(dayElement);
            }
            
            // 添加下个月的日期（填满42格）
            const totalCells = 42;
            const filledCells = calendarGrid.children.length;
            const remainingCells = totalCells - filledCells;
            
            for (let day = 1; day <= remainingCells; day++) {
                const dayElement = createDayElement(day, true, currentMonth === 12 ? currentYear + 1 : currentYear, currentMonth === 12 ? 1 : currentMonth + 1);
                calendarGrid.appendChild(dayElement);
            }
        }

        // 创建日期元素
        function createDayElement(day, isOtherMonth, year, month) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;
            dayElement.dataset.year = year;
            dayElement.dataset.month = month;
            dayElement.dataset.day = day;
            
            if (isOtherMonth) {
                dayElement.classList.add('other-month');
            }
            
            // 检查是否为今天
            const today = new Date();
            if (year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate()) {
                dayElement.classList.add('today');
            }
            
            // 检查是否为选中日期
            if (selectedDate && selectedDate.year === year && selectedDate.month === month && selectedDate.day === day) {
                dayElement.classList.add('selected');
            }
            
            // 添加点击事件
            dayElement.addEventListener('click', () => {
                selectDate(year, month, day);
            });
            
            return dayElement;
        }

        // 更新月份显示
        function updateMonthDisplay() {
            const monthDisplay = document.getElementById('currentMonthDisplay');
            if (monthDisplay) {
                monthDisplay.textContent = `${currentYear}年${currentMonth.toString().padStart(2, '0')}月`;
            }
        }

        // 选择日期
        function selectDate(year, month, day) {
            // 更新选中的日期
            selectedDate = { year, month, day };
            
            // 更新隐藏字段
            const birthDate = document.getElementById('babyBirthDate');
            const selectedYear = document.getElementById('selectedYear');
            const selectedMonth = document.getElementById('selectedMonth');
            const selectedDay = document.getElementById('selectedDay');
            
            if (birthDate) {
                birthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
            }
            if (selectedYear) selectedYear.value = year;
            if (selectedMonth) selectedMonth.value = month;
            if (selectedDay) selectedDay.value = day;
            
            // 更新显示
            updateDateDisplay();
            updateModalDateDisplay();
            
            // 重新生成日历以更新选中状态
            generateCalendar();
            
            console.log('📅 选择日期:', year + '年' + month + '月' + day + '日');
            
            // 延迟关闭模态框，让用户看到选中效果
            setTimeout(() => {
                closeBabyDatePicker();
            }, 300);
        }

        // 清除日期
        function clearDate() {
            selectedDate = null;
            
            // 清除隐藏字段
            const birthDate = document.getElementById('babyBirthDate');
            const selectedYear = document.getElementById('selectedYear');
            const selectedMonth = document.getElementById('selectedMonth');
            const selectedDay = document.getElementById('selectedDay');
            
            if (birthDate) birthDate.value = '';
            if (selectedYear) selectedYear.value = '';
            if (selectedMonth) selectedMonth.value = '';
            if (selectedDay) selectedDay.value = '';
            
            // 更新显示
            updateDateDisplay();
            updateModalDateDisplay();
            
            // 重新生成日历
            generateCalendar();
            
            console.log('🗑️ 日期已清除');
            
            // 延迟关闭模态框
            setTimeout(() => {
                closeBabyDatePicker();
            }, 300);
        }

        // 选择今天
        function selectToday() {
            const today = new Date();
            const year = today.getFullYear();
            const month = today.getMonth() + 1;
            const day = today.getDate();
            
            // 跳转到今天所在的月份
            currentYear = year;
            currentMonth = month;
            
            // 更新选中的日期（不要调用selectDate，避免双重关闭）
            selectedDate = { year, month, day };
            
            // 更新隐藏字段
            const birthDate = document.getElementById('babyBirthDate');
            const selectedYear = document.getElementById('selectedYear');
            const selectedMonth = document.getElementById('selectedMonth');
            const selectedDay = document.getElementById('selectedDay');
            
            if (birthDate) {
                birthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
            }
            if (selectedYear) selectedYear.value = year;
            if (selectedMonth) selectedMonth.value = month;
            if (selectedDay) selectedDay.value = day;
            
            // 更新显示
            updateDateDisplay();
            updateModalDateDisplay();
            updateMonthDisplay();
            generateCalendar();
            
            console.log('📅 选择今天:', year + '年' + month + '月' + day + '日');
            
            // 延迟关闭模态框
            setTimeout(() => {
                closeBabyDatePicker();
            }, 500);
        }

        // 确认日期选择
        function confirmDate() {
            if (selectedDate) {
                // 更新时辰选择
                const hourSelect = document.getElementById('babyBirthHour');
                if (hourSelect && selectedHour) {
                    hourSelect.value = selectedHour;
                }
                
                // 关闭模态框
                closeBabyDatePicker();
                
                console.log('✅ 确认选择:', selectedDate.year + '年' + selectedDate.month + '月' + selectedDate.day + '日');
            } else {
                alert('请先选择一个日期');
            }
        }

        // 更新日期显示（主界面）
        function updateDateDisplay() {
            const dateDisplayText = document.getElementById('dateDisplayText');
            if (dateDisplayText) {
                if (selectedDate) {
                    const { year, month, day } = selectedDate;
                    dateDisplayText.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
                    dateDisplayText.classList.add('selected');
                } else {
                    dateDisplayText.textContent = '请选择日期';
                    dateDisplayText.classList.remove('selected');
                }
            }
        }

        // 更新模态框日期显示
        function updateModalDateDisplay() {
            const modalDateDisplay = document.getElementById('modalDateDisplay');
            if (modalDateDisplay) {
                if (selectedDate) {
                    const { year, month, day } = selectedDate;
                    modalDateDisplay.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
                    modalDateDisplay.classList.add('selected');
                } else {
                    modalDateDisplay.textContent = '请选择日期';
                    modalDateDisplay.classList.remove('selected');
                }
            }
        }

        // 显示年份选择器
        function showYearSelector() {
            const yearSelector = document.getElementById('yearSelector');
            const calendarContainer = document.querySelector('.calendar-container');
            const calendarFooter = document.querySelector('.calendar-footer');
            const monthNavigation = document.querySelector('.month-navigation');
            
            if (yearSelector) {
                yearSelector.style.display = 'block';
                if (calendarContainer) calendarContainer.style.display = 'none';
                if (calendarFooter) calendarFooter.style.display = 'none';
                if (monthNavigation) monthNavigation.style.display = 'none';
                
                generateYearGrid();
                updateYearRangeDisplay();
            }
        }

        // 隐藏年份选择器
        function hideYearSelector() {
            const yearSelector = document.getElementById('yearSelector');
            const calendarContainer = document.querySelector('.calendar-container');
            const calendarFooter = document.querySelector('.calendar-footer');
            const monthNavigation = document.querySelector('.month-navigation');
            
            if (yearSelector) {
                yearSelector.style.display = 'none';
                if (calendarContainer) calendarContainer.style.display = 'block';
                if (calendarFooter) calendarFooter.style.display = 'flex';
                if (monthNavigation) monthNavigation.style.display = 'flex';
            }
        }

        // 改变年份范围
        function changeYearRange(delta) {
            yearRangeStart += delta;
            if (yearRangeStart < 1900) yearRangeStart = 1900;
            if (yearRangeStart > 2100) yearRangeStart = 2100;
            
            generateYearGrid();
            updateYearRangeDisplay();
        }

        // 更新年份范围显示
        function updateYearRangeDisplay() {
            const yearRangeDisplay = document.getElementById('yearRangeDisplay');
            if (yearRangeDisplay) {
                yearRangeDisplay.textContent = `${yearRangeStart}-${yearRangeStart + 9}`;
            }
        }

        // 生成年份网格
        function generateYearGrid() {
            const yearGrid = document.getElementById('yearGrid');
            if (!yearGrid) return;
            
            yearGrid.innerHTML = '';
            const currentYearValue = new Date().getFullYear();
            
            for (let i = 0; i < 10; i++) {
                const year = yearRangeStart + i;
                const yearItem = document.createElement('div');
                yearItem.className = 'year-item';
                yearItem.textContent = year;
                yearItem.dataset.year = year;
                
                // 添加当前年份标记
                if (year === currentYearValue) {
                    yearItem.classList.add('current');
                }
                
                // 添加选中年份标记
                if (year === currentYear) {
                    yearItem.classList.add('selected');
                }
                
                // 添加点击事件
                yearItem.addEventListener('click', () => {
                    selectYear(year);
                });
                
                yearGrid.appendChild(yearItem);
            }
        }

        // 选择年份
        function selectYear(year) {
            currentYear = year;
            
            // 更新月份显示
            updateMonthDisplay();
            
            // 重新生成日历
            generateCalendar();
            
            // 隐藏年份选择器
            hideYearSelector();
        }

        // 在系统初始化时调用日期选择器初始化
        document.addEventListener('DOMContentLoaded', function() {
            initBabyDatePicker();
            window.AI_CONFIG.SERVICE_TYPE='deepseek'
        });
    </script>

    <!-- 宝宝日期选择器模态框 -->
    <div id="babyDateModal" class="baby-date-modal">
        <div class="baby-modal-container">
            <!-- 粉色头部 -->
            <div class="baby-modal-header">
                <div class="header-icon">📅</div>
                <h3>请选择农历出生日期</h3>
            </div>
            
            <!-- 日期输入框 -->
            <div class="date-input-container">
                <div class="date-input-box">
                    <span class="date-value" id="modalDateDisplay">请选择日期</span>
                    <div class="date-confirm-btn" onclick="confirmDate()">
                        <span class="confirm-icon">✓</span>
                    </div>
                </div>
            </div>
            
            <!-- 月份导航 -->
            <div class="month-navigation">
                <button class="nav-btn prev-month" onclick="prevMonth()">
                    <span>↑</span>
                </button>
                <div class="current-month" onclick="showYearSelector()">
                    <span id="currentMonthDisplay">2025年07月</span>
                    <span class="month-dropdown">▼</span>
                </div>
                <button class="nav-btn next-month" onclick="nextMonth()">
                    <span>↓</span>
                </button>
            </div>

            <!-- 年份选择器 -->
            <div id="yearSelector" class="year-selector" style="display: none;">
                <div class="year-selector-header">
                    <button class="year-nav-btn" onclick="changeYearRange(-10)">‹‹</button>
                    <span id="yearRangeDisplay">2020-2029</span>
                    <button class="year-nav-btn" onclick="changeYearRange(10)">››</button>
                </div>
                <div class="year-grid" id="yearGrid">
                    <!-- 年份选项将通过JavaScript生成 -->
                </div>
                <div class="year-selector-footer">
                    <button class="year-btn" onclick="hideYearSelector()">返回</button>
                </div>
            </div>
            
            <!-- 日历网格 -->
            <div class="calendar-container">
                <!-- 星期标题 -->
                <div class="week-header">
                    <div class="week-day">一</div>
                    <div class="week-day">二</div>
                    <div class="week-day">三</div>
                    <div class="week-day">四</div>
                    <div class="week-day">五</div>
                    <div class="week-day">六</div>
                    <div class="week-day">日</div>
                </div>
                
                <!-- 日历日期网格 -->
                <div class="calendar-grid" id="calendarGrid">
                    <!-- 日期将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 底部按钮 -->
            <div class="calendar-footer">
                <button class="footer-btn clear-btn" onclick="clearDate()">清除</button>
                <button class="footer-btn today-btn" onclick="selectToday()">今天</button>
            </div>
            
            <!-- 收起箭头 -->
            <div class="collapse-arrow" onclick="closeBabyDatePicker()">
                <span>▼</span>
            </div>
        </div>
    </div>
</body>
</html> 