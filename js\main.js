// 全局应用对象
const App = {
    currentPage: 'home',
    
    // 初始化应用
    init() {
        this.setupNavigation();
        this.setupFunctionButtons();
        this.setupServiceCards();
        this.setupBottomNav();
        this.setupCarousel();
        this.setupCalculationItems();
        this.addTouchSupport();
        this.initializeNavState();
    },

    // 初始化导航状态
    initializeNavState() {
        // 确保首页导航在初始加载时是激活的
        const homeNavItem = document.querySelector('.header .nav-item[data-target="home"]');
        if (homeNavItem) {
            homeNavItem.classList.add('active');
        }
    },

    // 设置顶部导航
    setupNavigation() {
        const navItems = document.querySelectorAll('.header .nav-item[data-target]');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 移除所有active类
                navItems.forEach(nav => nav.classList.remove('active'));
                // 添加active类到当前项
                item.classList.add('active');
                
                // 根据data-target属性跳转到对应区域
                const target = item.dataset.target;
                this.handleNavigation(target);
            });
        });

        // 监听滚动事件，更新导航高亮
        this.setupScrollSpy();
    },

    // 处理导航点击
    handleNavigation(target) {
        const targetElement = document.getElementById(target);
        if (targetElement) {
            // 计算滚动位置（考虑固定顶部导航的高度）
            const headerHeight = document.querySelector('.header').offsetHeight;
            const elementPosition = targetElement.offsetTop - headerHeight - 10;
            
            window.scrollTo({
                top: elementPosition,
                behavior: 'smooth'
            });
            
            this.currentPage = target;
        }
    },

    // 滚动监听 - 更新导航高亮
    setupScrollSpy() {
        const sections = document.querySelectorAll('[id]');
        const navItems = document.querySelectorAll('.header .nav-item[data-target]');
        
        const observerOptions = {
            root: null,
            rootMargin: '-20% 0px -60% 0px',
            threshold: 0
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.id;
                    
                    // 更新导航高亮
                    navItems.forEach(item => {
                        item.classList.remove('active');
                        if (item.dataset.target === id) {
                            item.classList.add('active');
                        }
                    });
                }
            });
        }, observerOptions);

        sections.forEach(section => {
            observer.observe(section);
        });
    },

    // 设置功能按钮
    setupFunctionButtons() {
        const funcButtons = document.querySelectorAll('.func-btn');
        funcButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = btn.dataset.page;
                this.handleFunctionClick(page, btn);
            });
        });
    },

    // 处理功能按钮点击
    handleFunctionClick(page, element) {
        // 添加点击动画
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);

        // 直接跳转
        switch(page) {
            case 'bazi':
                window.location.href = 'pages/bazi/index.html';
                break;
            case 'career':
                window.location.href = 'pages/wealth/index.html';
                break;
            case 'marriage':
                window.location.href = 'pages/marriage/index.html';
                break;
            case 'baby-name':
                this.scrollToSection('.naming-section');
                break;
            default:
                this.showComingSoon(page);
        }
    },

    // 设置服务卡片
    setupServiceCards() {
        const serviceCards = document.querySelectorAll('.service-item, .service-card, .service-circle');
        serviceCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const service = card.dataset.service || 'default';
                this.handleServiceClick(service, card);
            });
        });
    },

    // 处理服务卡片点击
    handleServiceClick(service, element) {
        // 添加点击反馈
        this.addClickFeedback(element);
        // 直接跳转
        const serviceRoutes = {
            'bazi': 'pages/bazi/index.html',
            'marriage': 'pages/marriage/index.html',
            'name-analysis': 'pages/naming/index.html',
            'name-detail': 'pages/naming/index.html',
            'portrait': 'pages/tarot/index.html',
            'tarot': 'pages/tarot/index.html',
            'name-match': 'pages/naming/index.html',
            'fortune': 'pages/wealth/index.html',
            'baby-name': 'pages/baby-naming/index.html',
            'zodiac': 'pages/zodiac/index.html',
            'phone': 'pages/phone/index.html'
        };
        const route = serviceRoutes[service];
        if (route) {
            window.location.href = route;
        } else {
            this.showComingSoon('该功能');
        }
    },

    // 设置底部导航
    setupBottomNav() {
        const bottomNavItems = document.querySelectorAll('.bottom-nav .nav-item');
        bottomNavItems.forEach(item => {
            item.addEventListener('click', (e) => {
                bottomNavItems.forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
                
                const navText = item.querySelector('.nav-text').textContent;
                this.handleBottomNavigation(navText);
            });
        });
    },

    // 处理底部导航
    handleBottomNavigation(navText) {
        switch(navText) {
            case '首页':
                window.scrollTo({ top: 0, behavior: 'smooth' });
                break;
            case '订单查询':
                window.location.href = 'pages/order/index.html';
                break;
            case '个人中心':
                window.location.href = 'pages/profile/index.html';
                break;
        }
    },

    // 设置轮播图
    setupCarousel() {
        // 使用轮播图模块
        if (typeof Carousel !== 'undefined') {
            Carousel.init();
            console.log('✅ 轮播图模块初始化成功');
        } else {
            console.warn('⚠️ 轮播图模块未找到，使用简单轮播');
            this.setupSimpleCarousel();
        }
    },

    // 简单轮播图（备用方案）
    setupSimpleCarousel() {
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-item');
        const dots = document.querySelectorAll('.dot');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (slides.length === 0) return;

        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            if (slides[index]) slides[index].classList.add('active');
            if (dots[index]) dots[index].classList.add('active');

            currentSlide = index;
        }

        function nextSlide() {
            const next = (currentSlide + 1) % slides.length;
            showSlide(next);
        }

        function prevSlide() {
            const prev = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(prev);
        }

        // 自动轮播
        let autoPlayInterval = setInterval(nextSlide, 5000);

        // 控制按钮事件
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                clearInterval(autoPlayInterval);
                nextSlide();
                autoPlayInterval = setInterval(nextSlide, 5000);
            });
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                clearInterval(autoPlayInterval);
                prevSlide();
                autoPlayInterval = setInterval(nextSlide, 5000);
            });
        }

        // 点击圆点切换
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                clearInterval(autoPlayInterval);
                showSlide(index);
                autoPlayInterval = setInterval(nextSlide, 5000);
            });
        });
    },

    // 设置测算项目
    setupCalculationItems() {
        const calcItems = document.querySelectorAll('.calc-item');
        calcItems.forEach(item => {
            const btn = item.querySelector('.calc-action');
            if (btn) {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const title = item.querySelector('h4').textContent;
                    // 直接跳转
                    const routes = {
                        '你能和前任复合吗？': 'pages/fortune/index.html',
                        '婚前合婚，选对伴侣': 'pages/marriage/index.html',
                        '好意、好记的吉祥美名！': 'pages/naming/index.html',
                        '事业前程': 'pages/wealth/index.html',
                    };
                    window.location.href = routes[title] || 'pages/fortune/index.html';
                });
            }
        });
        // 特殊计算按钮
        const specialBtns = document.querySelectorAll('.calc-btn');
        specialBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const btnText = btn.textContent.trim();
                if (btnText === '求姻缘') {
                    window.location.href = 'pages/marriage/index.html';
                } else {
                    window.location.href = 'pages/fortune/index.html';
                }
            });
        });
    },

    // 移除 createModal/showCalculationModal 相关方法

    // 显示塔罗牌占卜
    showTarotReading() {
        const cards = ['愚者', '魔术师', '女祭司', '皇后', '皇帝', '教皇', '恋人', '战车', '力量', '隐者'];
        const randomCard = cards[Math.floor(Math.random() * cards.length)];
        
        this.showNotification(`您抽到了：${randomCard}`, '这张牌预示着新的开始和机遇！');
    },

    // 显示通知
    showNotification(title, message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 1001;
            text-align: center;
            max-width: 300px;
            width: 90%;
        `;

        notification.innerHTML = `
            <h3 style="color: #FF69B4; margin-bottom: 10px;">${title}</h3>
            <p style="color: #666; margin-bottom: 15px; line-height: 1.5;">${message}</p>
            <button style="background: linear-gradient(135deg, #FF69B4, #FF1493); color: white; border: none; border-radius: 20px; padding: 10px 20px; cursor: pointer;" onclick="this.parentNode.remove()">确定</button>
        `;

        document.body.appendChild(notification);

        // 3秒后自动关闭
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    },

    // 显示即将上线
    showComingSoon(feature) {
        this.showNotification('即将上线', `${feature}正在开发中，敬请期待！`);
    },

    // 滚动到指定区域
    scrollToSection(selector) {
        const section = document.querySelector(selector);
        if (section) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const elementPosition = section.offsetTop - headerHeight - 10;
            
            window.scrollTo({
                top: elementPosition,
                behavior: 'smooth'
            });
        }
    },

    // 添加点击反馈
    addClickFeedback(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    },

    // 添加触摸支持
    addTouchSupport() {
        // 防止双击缩放
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });

        // 添加触摸反馈
        const interactiveElements = document.querySelectorAll('button, .service-item, .service-card, .service-circle, .nav-item, .calc-item');
        interactiveElements.forEach(element => {
            element.addEventListener('touchstart', () => {
                element.style.opacity = '0.7';
            });
            
            element.addEventListener('touchend', () => {
                element.style.opacity = '';
            });
        });
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    App.init();
    
    // 添加页面加载动画
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease';
        document.body.style.opacity = '1';
    }, 100);
});

// 起名测名功能
function openBoyNaming() {
    console.log('👦 男孩名字按钮被点击了！');
    // 跳转到起名页面，并设置为男孩模式
    window.location.href = 'pages/naming/index.html?type=boy';
}

function openGirlNaming() {
    console.log('👧 女孩名字按钮被点击了！');
    // 跳转到起名页面，并设置为女孩模式
    window.location.href = 'pages/naming/index.html?type=girl';
}

function openNameAnalysis() {
    console.log('📝 姓名详批按钮被点击了！');
    // 跳转到姓名详批页面
    window.location.href = 'pages/naming/index.html?type=analysis';
}

function openNameMatching() {
    console.log('💕 姓名配对按钮被点击了！');
    // 跳转到姓名配对页面
    window.location.href = 'pages/naming/index.html?type=matching';
}

// 导出供其他模块使用
window.App = App;

// 滚动到顶部函数
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
} 