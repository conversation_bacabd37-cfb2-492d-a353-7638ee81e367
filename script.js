// 全局变量
let currentService = '';
let tarotCards = [];

// 算命数据库
const fortuneData = {
    bazi: {
        title: '八字精批',
        price: '¥29.9',
        fields: ['name', 'birthDate', 'birthTime', 'gender'],
        results: [
            '命主五行属火，性格热情开朗，富有创造力',
            '今年运势较好，事业上会有新的突破',
            '感情方面需要多沟通，避免误会',
            '财运稳定，适合稳健投资',
            '健康状况良好，注意劳逸结合'
        ]
    },
    marriage: {
        title: '八字合婚',
        price: '¥19.9',
        fields: ['name1', 'birthDate1', 'name2', 'birthDate2'],
        results: [
            '两人八字匹配度较高，属于良配',
            '性格互补，能够相互扶持',
            '需要注意沟通方式，避免冲突',
            '婚后财运会有所提升',
            '建议在春季或秋季举办婚礼'
        ]
    },
    name: {
        title: '姓名详批',
        price: '¥9.9',
        fields: ['name', 'birthDate'],
        results: [
            '姓名总格数理吉祥，有助于事业发展',
            '人格笔画暗示性格温和，人缘较好',
            '地格数理显示早年运势平稳',
            '外格暗示社交能力强，易得贵人相助',
            '天格五行与八字相配，整体运势向好'
        ]
    },
    career: {
        title: '事业财运',
        price: '¥39.9',
        fields: ['name', 'birthDate', 'profession'],
        results: [
            '事业发展前景良好，有升职加薪机会',
            '适合从事创造性或领导性工作',
            '财运稳中有升，投资需谨慎',
            '人际关系处理得当，有助事业发展',
            '建议在下半年开始新的项目或投资'
        ]
    }
};

// 塔罗牌数据
const tarotCardData = [
    { name: '愚者', meaning: '新的开始，冒险精神，保持童心', emoji: '🎭' },
    { name: '魔术师', meaning: '意志力，创造力，新的机会', emoji: '🎩' },
    { name: '女祭司', meaning: '直觉，潜意识，神秘知识', emoji: '🔮' },
    { name: '皇后', meaning: '丰收，创造力，母性力量', emoji: '👑' },
    { name: '皇帝', meaning: '权威，稳定，领导力', emoji: '⚜️' },
    { name: '教皇', meaning: '传统，精神指导，智慧', emoji: '📿' },
    { name: '恋人', meaning: '爱情，选择，人际关系', emoji: '💕' },
    { name: '战车', meaning: '胜利，意志力，自控', emoji: '🏆' },
    { name: '力量', meaning: '内在力量，勇气，自信', emoji: '💪' },
    { name: '隐者', meaning: '内省，寻找真理，独立', emoji: '🕯️' }
];



// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    setupNavigation();
    setupServiceCards();
    setupFortuneButtons();
    setupTarot();

    setupModal();
    setupNamingConsultation();
}

// 设置导航
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 更新活跃状态
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // 滚动到对应部分
            const target = this.getAttribute('href');
            if (target) {
                const section = document.querySelector(target);
                if (section) {
                    section.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    });
}

// 设置服务卡片
function setupServiceCards() {
    const serviceItems = document.querySelectorAll('.service-item');
    serviceItems.forEach(item => {
        item.addEventListener('click', function() {
            const service = this.dataset.service;
            
            // 定义服务页面映射
            const servicePageMap = {
                'bazi': () => openFortuneModal('bazi'),
                'marriage': () => openFortuneModal('marriage'),
                'baby-name': () => window.location.href = 'pages/baby-naming/index.html',
                'portrait': () => window.location.href = 'pages/tarot/index.html',
                'fortune': () => window.location.href = 'pages/wealth/index.html',
                'name-analysis': () => window.location.href = 'pages/naming/index.html',
                'zodiac': () => window.location.href = 'pages/zodiac/index.html',
                'phone': () => window.location.href = 'pages/phone/index.html'
            };
            
            // 执行对应的服务处理
            if (servicePageMap[service]) {
                servicePageMap[service]();
            } else if (fortuneData[service]) {
                openFortuneModal(service);
            } else {
                console.warn('未找到服务处理逻辑:', service);
            }
        });
    });
}

// 设置算命按钮
function setupFortuneButtons() {
    const fortuneButtons = document.querySelectorAll('.fortune-btn');
    fortuneButtons.forEach(button => {
        button.addEventListener('click', function() {
            const type = this.dataset.type;
            const serviceMap = {
                'bazi-detail': 'bazi',
                'bazi-marriage': 'marriage',
                'name-detail': 'name',
                'career-fortune': 'career'
            };
            
            const service = serviceMap[type] || 'bazi';
            openFortuneModal(service);
        });
    });
}

// 设置塔罗功能
function setupTarot() {
    const shuffleBtn = document.getElementById('shuffleCards');
    const tarotCardsContainer = document.getElementById('tarotCards');
    
    shuffleBtn.addEventListener('click', function() {
        generateTarotCards();
    });
    
    generateTarotCards();
}

// 生成塔罗牌
function generateTarotCards() {
    const container = document.getElementById('tarotCards');
    container.innerHTML = '';
    
    // 随机选择5张牌
    const shuffled = [...tarotCardData].sort(() => Math.random() - 0.5);
    tarotCards = shuffled.slice(0, 5);
    
    tarotCards.forEach((card, index) => {
        const cardElement = document.createElement('div');
        cardElement.className = 'tarot-card';
        cardElement.dataset.index = index;
        
        cardElement.addEventListener('click', function() {
            if (!this.classList.contains('flipped')) {
                this.classList.add('flipped');
                this.innerHTML = card.emoji;
                showTarotResult(card);
            }
        });
        
        container.appendChild(cardElement);
    });
}

// 显示塔罗结果
function showTarotResult(card) {
    const resultDiv = document.getElementById('tarotResult');
    const resultContent = resultDiv.querySelector('.card-result');
    
    resultContent.innerHTML = `
        <div class="tarot-card-info">
            <div class="card-symbol">${card.emoji}</div>
            <h5>${card.name}</h5>
            <p>${card.meaning}</p>
        </div>
    `;
    
    resultDiv.style.display = 'block';
    resultDiv.scrollIntoView({ behavior: 'smooth' });
}



// 设置模态框
function setupModal() {
    const modal = document.getElementById('fortuneModal');
    const closeBtn = document.getElementById('closeModal');
    
    closeBtn.addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// 打开算命模态框
function openFortuneModal(service) {
    const modal = document.getElementById('fortuneModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    
    const serviceData = fortuneData[service];
    if (!serviceData) return;
    
    currentService = service;
    modalTitle.textContent = serviceData.title;
    
    // 创建表单
    const form = createFortuneForm(serviceData);
    modalBody.innerHTML = '';
    modalBody.appendChild(form);
    
    modal.style.display = 'block';
}

// 创建算命表单
function createFortuneForm(serviceData) {
    const form = document.createElement('form');
    form.className = 'fortune-form';
    
    const fieldLabels = {
        name: '姓名',
        name1: '男方姓名',
        name2: '女方姓名',
        birthDate: '出生日期',
        birthDate1: '男方生日',
        birthDate2: '女方生日',
        birthTime: '出生时辰',
        gender: '性别',
        profession: '职业'
    };
    
    serviceData.fields.forEach(field => {
        const formGroup = document.createElement('div');
        formGroup.className = 'form-group';
        
        const label = document.createElement('label');
        label.textContent = fieldLabels[field] || field;
        
        let input;
        if (field === 'gender') {
            input = document.createElement('select');
            input.innerHTML = `
                <option value="男">男</option>
                <option value="女">女</option>
            `;
        } else if (field.includes('Date')) {
            input = document.createElement('input');
            input.type = 'date';
        } else if (field === 'birthTime') {
            input = document.createElement('select');
            input.innerHTML = `
                <option value="子时">子时 (23:00-01:00)</option>
                <option value="丑时">丑时 (01:00-03:00)</option>
                <option value="寅时">寅时 (03:00-05:00)</option>
                <option value="卯时">卯时 (05:00-07:00)</option>
                <option value="辰时">辰时 (07:00-09:00)</option>
                <option value="巳时">巳时 (09:00-11:00)</option>
                <option value="午时">午时 (11:00-13:00)</option>
                <option value="未时">未时 (13:00-15:00)</option>
                <option value="申时">申时 (15:00-17:00)</option>
                <option value="酉时">酉时 (17:00-19:00)</option>
                <option value="戌时">戌时 (19:00-21:00)</option>
                <option value="亥时">亥时 (21:00-23:00)</option>
            `;
        } else {
            input = document.createElement('input');
            input.type = 'text';
        }
        
        input.name = field;
        input.required = true;
        
        formGroup.appendChild(label);
        formGroup.appendChild(input);
        form.appendChild(formGroup);
    });
    
    // 价格显示
    const priceDiv = document.createElement('div');
    priceDiv.className = 'price-info';
    priceDiv.innerHTML = `<h4>测算费用：${serviceData.price}</h4>`;
    form.appendChild(priceDiv);
    
    // 提交按钮
    const submitBtn = document.createElement('button');
    submitBtn.type = 'submit';
    submitBtn.className = 'submit-btn';
    submitBtn.textContent = '开始测算';
    
    submitBtn.addEventListener('click', function(e) {
        e.preventDefault();
        processFortuneForm(form);
    });
    
    form.appendChild(submitBtn);
    
    return form;
}

// 处理算命表单
function processFortuneForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 显示加载状态
    const submitBtn = form.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> 测算中...';
    submitBtn.disabled = true;
    
    // 模拟处理时间
    setTimeout(() => {
        showFortuneResult(data);
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// 显示算命结果
function showFortuneResult(data) {
    const modalBody = document.getElementById('modalBody');
    const serviceData = fortuneData[currentService];
    
    if (!serviceData) return;
    
    const resultHTML = `
        <div class="fortune-result">
            <div class="alert alert-success">
                测算完成！以下是您的详细分析：
            </div>
            <div class="result-content">
                ${serviceData.results.map((result, index) => `
                    <div class="result-item">
                        <div class="result-label">分析 ${index + 1}：</div>
                        <div class="result-value">${result}</div>
                    </div>
                `).join('')}
            </div>
            <div class="result-footer">
                <p><small>* 本分析由易海堂提供，仅供参考。建议结合个人实际情况综合考虑。</small></p>
                <button class="submit-btn" onclick="location.reload()">再次测算</button>
            </div>
        </div>
    `;
    
    modalBody.innerHTML = resultHTML;
}

// 添加页面滚动效果
window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(15, 15, 35, 0.98)';
    } else {
        header.style.background = 'rgba(15, 15, 35, 0.95)';
    }
});

// 添加动画效果
function addAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // 观察所有需要动画的元素
    const animatedElements = document.querySelectorAll('.service-card, .category');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// 页面加载完成后添加动画
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addAnimations, 500);
});



// 设置专业起名咨询按钮
function setupNamingConsultation() {
    console.log('正在设置专业起名咨询按钮...');
    
    // 使用更多选择器尝试找到按钮
    const consultationBtn = document.querySelector('.name-consultation-btn');
    
    if (consultationBtn) {
        console.log('找到专业起名咨询按钮，正在绑定点击事件...');
        
        consultationBtn.addEventListener('click', function(event) {
            event.preventDefault();
            console.log('专业起名咨询按钮被点击');
            
            // 跳转到宝宝起名页面
            try {
                window.location.href = 'pages/baby-naming/index.html';
                console.log('正在跳转到宝宝起名页面...');
            } catch (error) {
                console.error('跳转失败:', error);
                // 备用跳转方式
                window.open('pages/baby-naming/index.html', '_self');
            }
        });
        
        console.log('专业起名咨询按钮绑定成功');
    } else {
        console.error('未找到专业起名咨询按钮 (.name-consultation-btn)');
        
        // 尝试延迟查找
        setTimeout(() => {
            const delayedBtn = document.querySelector('.name-consultation-btn');
            if (delayedBtn) {
                console.log('延迟找到专业起名咨询按钮');
                delayedBtn.addEventListener('click', function(event) {
                    event.preventDefault();
                    console.log('延迟绑定的按钮被点击');
                    window.location.href = 'pages/baby-naming/index.html';
                });
            } else {
                console.error('延迟查找仍未找到按钮');
            }
        }, 1000);
    }
}

// 导出函数供全局使用
window.openFortuneModal = openFortuneModal; 