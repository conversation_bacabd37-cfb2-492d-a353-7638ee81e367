<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财运预测 - 易海堂算命网</title>
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="./style.css">
    <script src="../../js/modules/ai-config-manager.js"></script>
    <!-- 认证服务 -->
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script src="../../js/modules/auth-service.js"></script>
    <!-- 认证初始化 -->
    <script src="../../js/modules/auth-init.js"></script>
    <!-- 订单和支付服务 -->
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <!-- 业务模块 -->
    <script src="../../js/modules/wealth-ai.js"></script>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="header-container">
            <div class="back-btn" onclick="history.back()">
                <span class="back-icon">←</span>
            </div>
            <h1 class="header-title">💰 财运预测</h1>
            <div class="header-right"></div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- Canvas动画背景 -->
        <canvas id="wealthCanvas" class="wealth-canvas"></canvas>
        
        <!-- 页面头部介绍 -->
        <section class="wealth-intro">
            <div class="intro-container">
                <div class="wealth-icon">💰</div>
                <h2 class="intro-title">八字财运预测</h2>
                <p class="intro-desc">通过您的生辰八字，精准分析您的财运走势</p>
            </div>
        </section>

        <!-- 财运测算表单 -->
        <section class="wealth-form-section">
            <div class="form-container">
                <form class="wealth-form" id="wealthForm">
                    <!-- 姓名输入 -->
                    <div class="input-group">
                        <label for="userName">
                            <span class="label-icon">👤</span>
                            <span class="label-text">您的姓名</span>
                        </label>
                        <input type="text" 
                               id="userName" 
                               name="userName" 
                               placeholder="请输入您的真实姓名" 
                               required>
                    </div>
                    
                    <!-- 性别选择 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">👥</span>
                            <span class="label-text">性别</span>
                        </label>
                        <div class="gender-options">
                            <label class="gender-option">
                                <input type="radio" name="gender" value="male" checked>
                                <span class="gender-btn male">
                                    <span class="gender-icon">♂</span>
                                    <span>男</span>
                                </span>
                            </label>
                            <label class="gender-option">
                                <input type="radio" name="gender" value="female">
                                <span class="gender-btn female">
                                    <span class="gender-icon">♀</span>
                                    <span>女</span>
                                </span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 出生日期 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">📅</span>
                            <span class="label-text">请选择您的农历出生日期</span>
                        </label>
                        <div class="wealth-date-picker" id="wealthDatePicker" onclick="openWealthDatePicker()">
                            <div class="date-input-display">
                                <span class="date-placeholder" id="dateDisplayText">请选择日期</span>
                                <div class="date-picker-btn">
                                    <span class="picker-icon">📅</span>
                                </div>
                            </div>
                        </div>
                        <!-- 隐藏的输入字段存储选中的值 -->
                        <input type="hidden" id="birthDate" name="birthDate" required>
                        <input type="hidden" id="selectedYear" name="selectedYear">
                        <input type="hidden" id="selectedMonth" name="selectedMonth">
                        <input type="hidden" id="selectedDay" name="selectedDay">
                    </div>
                    
                    <!-- 出生时辰 -->
                    <div class="input-group">
                        <label for="birthHour">
                            <span class="label-icon">⏰</span>
                            <span class="label-text">出生时辰</span>
                        </label>
                        <select id="birthHour" name="birthHour" required>
                            <option value="">请选择时辰</option>
                            <option value="zi">子时 (23:00-01:00)</option>
                            <option value="chou">丑时 (01:00-03:00)</option>
                            <option value="yin">寅时 (03:00-05:00)</option>
                            <option value="mao">卯时 (05:00-07:00)</option>
                            <option value="chen">辰时 (07:00-09:00)</option>
                            <option value="si">巳时 (09:00-11:00)</option>
                            <option value="wu">午时 (11:00-13:00)</option>
                            <option value="wei">未时 (13:00-15:00)</option>
                            <option value="shen">申时 (15:00-17:00)</option>
                            <option value="you">酉时 (17:00-19:00)</option>
                            <option value="xu">戌时 (19:00-21:00)</option>
                            <option value="hai">亥时 (21:00-23:00)</option>
                        </select>
                    </div>
                    
                    <!-- 关注重点 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">🎯</span>
                            <span class="label-text">关注重点（可选）</span>
                        </label>
                        <div class="focus-options">
                            <div class="focus-item">
                                <input type="checkbox" id="career" name="focus" value="career">
                                <label for="career" class="focus-label">事业财运</label>
                            </div>
                            <div class="focus-item">
                                <input type="checkbox" id="investment" name="focus" value="investment">
                                <label for="investment" class="focus-label">投资理财</label>
                            </div>
                            <div class="focus-item">
                                <input type="checkbox" id="business" name="focus" value="business">
                                <label for="business" class="focus-label">生意经营</label>
                            </div>
                            <div class="focus-item">
                                <input type="checkbox" id="windfall" name="focus" value="windfall">
                                <label for="windfall" class="focus-label">偏财横财</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="submit-section">
                        <button type="submit" class="submit-btn wealth-submit">
                            <span class="btn-icon">💰</span>
                            <span class="btn-text">开始财运预测</span>
                        </button>
                        <p class="submit-note">🔮 专业分析您的财运走势，发现致富机遇</p>
                    </div>
                </form>
            </div>
        </section>

        <!-- 财运知识卡片 -->
        <section class="wealth-tips">
            <div class="tips-container">
                <h3 class="tips-title">💡 财运小贴士</h3>
                <div class="tips-grid">
                    <div class="tip-card">
                        <div class="tip-icon">🌟</div>
                        <h4>五行财星</h4>
                        <p>根据八字五行分析财星强弱，判断财运基础</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">📊</div>
                        <h4>大运流年</h4>
                        <p>结合大运和流年变化，预测财运起伏周期</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">🎯</div>
                        <h4>求财方位</h4>
                        <p>分析有利的求财方向和行业选择</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">⚡</div>
                        <h4>财运时机</h4>
                        <p>把握最佳的投资和创业时机</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载动画 -->
    <div id="loadingOverlay" class="loading-overlay-wealth">
        <div class="loading-container">
            <div class="loading-spinner">
                <span class="spinner-icon">💰</span>
            </div>
            <p class="loading-text">正在分析您的财运...</p>
            <div class="loading-progress">
                <div class="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- AI深度分析区域 -->
    <div id="aiAnalysisSection" class="ai-analysis-section" style="display: none;">
        <div class="ai-container">
            <div class="ai-header">
                <h2>深度财运分析</h2>
                <p class="ai-subtitle">由 <span id="aiServiceType">易海堂</span> 提供专业分析</p>
            </div>
            
            <!-- AI加载动画 -->
            <div id="aiLoading" class="ai-loading">
                <div class="ai-loading-steps">
                    <div class="ai-step" id="aiStep1">
                        <div class="step-icon">📊</div>
                        <div class="step-text">分析财运格局</div>
                    </div>
                    <div class="ai-step" id="aiStep2">
                        <div class="step-icon">💎</div>
                        <div class="step-text">计算投资潜力</div>
                    </div>
                    <div class="ai-step" id="aiStep3">
                        <div class="step-icon">🎯</div>
                        <div class="step-text">预测财运趋势</div>
                    </div>
                    <div class="ai-step" id="aiStep4">
                        <div class="step-icon">🔮</div>
                        <div class="step-text">生成开运建议</div>
                    </div>
                </div>
                <div class="ai-loading-text">正在启动财运分析...</div>
                <div class="ai-progress-bar">
                    <div class="ai-progress-fill"></div>
                </div>
            </div>
            
            <!-- AI分析结果 -->
            <div id="aiContent" class="ai-content" style="display: none;">
                <!-- AI分析内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 结果展示模态框 -->
    <div id="resultModal" class="result-modal">
        <div class="result-container">
            <div class="result-header">
                <h2>💰 您的财运预测结果</h2>
                <span class="close-result" onclick="closeResult()">&times;</span>
            </div>
            <div class="result-content" id="resultContent">
                <!-- 结果内容将通过JavaScript动态生成 -->
            </div>
            <div class="result-actions">
                <button class="action-btn save-btn" onclick="saveResult()">
                    <span class="btn-icon">💾</span>
                    <span>保存结果</span>
                </button>
                <button class="action-btn share-btn" onclick="shareResult()">
                    <span class="btn-icon">📤</span>
                    <span>分享结果</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 财运日期选择器模态框 -->
    <div id="wealthDateModal" class="wealth-date-modal">
        <div class="wealth-modal-container">
            <!-- 金色头部 -->
            <div class="wealth-modal-header">
                <div class="header-icon">💰</div>
                <h3>请选择农历出生日期</h3>
            </div>
            
            <!-- 日期输入框 -->
            <div class="date-input-container">
                <div class="date-input-box">
                    <span class="date-value" id="modalDateDisplay">请选择日期</span>
                    <div class="date-confirm-btn" onclick="confirmDate()">
                        <span class="confirm-icon">✓</span>
                    </div>
                </div>
            </div>
            
            <!-- 月份导航 -->
            <div class="month-navigation">
                <button class="nav-btn prev-month" onclick="prevMonth()">
                    <span>↑</span>
                </button>
                <div class="current-month" onclick="showYearSelector()">
                    <span id="currentMonthDisplay">2025年01月</span>
                    <span class="month-dropdown">▼</span>
                </div>
                <button class="nav-btn next-month" onclick="nextMonth()">
                    <span>↓</span>
                </button>
            </div>

            <!-- 年份选择器 -->
            <div id="yearSelector" class="year-selector" style="display: none;">
                <div class="year-selector-header">
                    <button class="year-nav-btn" onclick="changeYearRange(-10)">‹‹</button>
                    <span id="yearRangeDisplay">2020-2029</span>
                    <button class="year-nav-btn" onclick="changeYearRange(10)">››</button>
                </div>
                <div class="year-grid" id="yearGrid">
                    <!-- 年份选项将通过JavaScript生成 -->
                </div>
                <div class="year-selector-footer">
                    <button class="year-btn" onclick="hideYearSelector()">返回</button>
                </div>
            </div>
            
            <!-- 日历网格 -->
            <div class="calendar-container">
                <!-- 星期标题 -->
                <div class="week-header">
                    <div class="week-day">一</div>
                    <div class="week-day">二</div>
                    <div class="week-day">三</div>
                    <div class="week-day">四</div>
                    <div class="week-day">五</div>
                    <div class="week-day">六</div>
                    <div class="week-day">日</div>
                </div>
                
                <!-- 日历日期网格 -->
                <div class="calendar-grid" id="calendarGrid">
                    <!-- 日期将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 底部按钮 -->
            <div class="calendar-footer">
                <button class="footer-btn clear-btn" onclick="clearDate()">清除</button>
                <button class="footer-btn today-btn" onclick="selectToday()">今天</button>
            </div>
            
            <!-- 收起箭头 -->
            <div class="collapse-arrow" onclick="closeWealthDatePicker()">
                <span>▼</span>
            </div>
        </div>
    </div>

    <!-- 引入AI模块 -->
    <script src="../../js/modules/ai-service.js"></script>
    <script src="../../js/modules/wealth-ai.js"></script>
    <script src="./script.js"></script>
</body>
</html> 