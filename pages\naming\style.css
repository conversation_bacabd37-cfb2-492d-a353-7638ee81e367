/* 姓名详批页面专用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* 顶部导航样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(10px);
    padding: 10px 0;
    box-shadow: 0 2px 20px rgba(102, 126, 234, 0.3);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    max-width: 400px;
    margin: 0 auto;
}

.back-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.back-icon {
    font-size: 20px;
    color: white;
    font-weight: bold;
}

.header-title {
    font-size: 18px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-right {
    width: 40px;
}

/* 主要内容区域 */
.main-content {
    padding-top: 70px;
    min-height: 100vh;
    position: relative;
}

/* Canvas背景动画 */
.naming-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

/* 页面介绍区域 */
.naming-intro {
    padding: 20px 20px 15px;
    text-align: center;
}

.intro-container {
    max-width: 400px;
    margin: 0 auto;
}

.naming-icon {
    font-size: 50px;
    margin-bottom: 15px;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.intro-title {
    font-size: 24px;
    color: white;
    margin-bottom: 10px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.intro-desc {
    font-size: 15px;
    color: white;
    margin-bottom: 15px;
    line-height: 1.5;
    opacity: 0.9;
}

/* 表单区域 */
.naming-form-section {
    padding: 0 20px 30px;
}

.form-container {
    max-width: 400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(10px);
}

.naming-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #4a5568;
    font-size: 15px;
}

.label-icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
    color: #667eea;
}

.input-group input[type="text"],
.input-group input[type="date"],
.input-group select {
    padding: 12px;
    border: 2px solid #667eea;
    border-radius: 12px;
    font-size: 15px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    color: #333;
}

.input-group input[type="text"]:focus,
.input-group input[type="date"]:focus,
.input-group select:focus {
    outline: none;
    border-color: #764ba2;
    box-shadow: 0 0 0 3px rgba(118, 75, 162, 0.1);
    background: white;
}

/* 性别选择样式 */
.gender-options {
    display: flex;
    gap: 12px;
}

.gender-option {
    flex: 1;
}

.gender-option input[type="radio"] {
    display: none;
}

.gender-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 12px;
    border: 2px solid #667eea;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #666;
}

.gender-option input[type="radio"]:checked + .gender-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #764ba2;
    box-shadow: 0 4px 15px rgba(118, 75, 162, 0.3);
}

.gender-btn.male {
    color: #4A90E2;
}

.gender-btn.female {
    color: #E74C3C;
}

.gender-option input[type="radio"]:checked + .gender-btn.male,
.gender-option input[type="radio"]:checked + .gender-btn.female {
    color: white;
}

.gender-icon {
    font-size: 18px;
}

/* 关注重点选择 */
.focus-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.focus-item {
    display: flex;
    align-items: center;
}

.focus-item input[type="checkbox"] {
    display: none;
}

.focus-label {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #667eea;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #666;
}

.focus-item input[type="checkbox"]:checked + .focus-label {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #764ba2;
    box-shadow: 0 2px 10px rgba(118, 75, 162, 0.3);
}

/* 提交按钮 */
.submit-section {
    margin-top: 25px;
    text-align: center;
}

.submit-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 15px;
    color: white;
    font-size: 17px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(118, 75, 162, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(118, 75, 162, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.btn-icon {
    font-size: 20px;
}

.submit-note {
    margin-top: 15px;
    font-size: 14px;
    color: #4a5568;
    opacity: 0.8;
}

/* 姓名知识小贴士 */
.naming-tips {
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.1);
}

.tips-container {
    max-width: 400px;
    margin: 0 auto;
}

.tips-title {
    text-align: center;
    font-size: 20px;
    color: white;
    margin-bottom: 20px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tips-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.tip-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
}

.tip-icon {
    font-size: 30px;
    margin-bottom: 10px;
}

.tip-card h4 {
    font-size: 16px;
    color: #4a5568;
    margin-bottom: 8px;
    font-weight: bold;
}

.tip-card p {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 加载动画 */
.loading-overlay-name {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
}

.loading-container {
    text-align: center;
    color: white;
}

.loading-spinner {
    font-size: 50px;
    margin-bottom: 20px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-text {
    font-size: 18px;
    margin-bottom: 20px;
}

.loading-progress {
    width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    animation: progress 3s ease-in-out;
}

@keyframes progress {
    from { width: 0%; }
    to { width: 100%; }
}

/* 结果模态框 */
.result-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.result-container {
    background: white;
    border-radius: 20px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.result-header {
    padding: 25px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 20px 20px 0 0;
}

.result-header h2 {
    font-size: 20px;
    font-weight: bold;
}

.close-result {
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.close-result:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.result-actions {
    padding: 20px 25px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 15px;
}

.action-btn {
    flex: 1;
    padding: 12px;
    border: 2px solid #667eea;
    border-radius: 10px;
    background: white;
    color: #667eea;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(118, 75, 162, 0.3);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .form-container {
        margin: 0 10px;
        padding: 18px;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .focus-options {
        grid-template-columns: 1fr;
    }
    
    .naming-intro {
        padding: 15px 20px 10px;
    }
    
    .naming-tips {
        padding: 25px 20px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.naming-intro,
.naming-form-section,
.naming-tips {
    animation: fadeInUp 0.8s ease-out;
}

.naming-form-section {
    animation-delay: 0.2s;
}

.naming-tips {
    animation-delay: 0.4s;
}

/* 姓名配对模式特殊样式 */
.matching-mode .naming-intro {
    background: linear-gradient(135deg, #FF69B4, #FF1493);
}

.matching-mode .intro-title {
    color: white;
    text-shadow: 0 2px 4px rgba(255, 20, 147, 0.3);
}

.matching-mode .naming-icon {
    animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.05);
    }
}

.matching-mode .submit-btn {
    background: linear-gradient(135deg, #FF69B4, #FF1493);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3);
}

.matching-mode .submit-btn:hover {
    box-shadow: 0 12px 35px rgba(255, 105, 180, 0.4);
}

.matching-mode .gender-option input[type="radio"]:checked + .gender-btn {
    background: linear-gradient(135deg, #FF69B4, #FF1493);
    border-color: #FF1493;
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
}

.matching-mode .focus-item input[type="checkbox"]:checked + .focus-label {
    background: linear-gradient(135deg, #FF69B4, #FF1493);
    border-color: #FF1493;
    box-shadow: 0 2px 10px rgba(255, 105, 180, 0.3);
}

.matching-mode .result-header {
    background: linear-gradient(135deg, #FF69B4, #FF1493);
}

.matching-mode .action-btn {
    border-color: #FF69B4;
    color: #FF69B4;
}

.matching-mode .action-btn:hover {
    background: linear-gradient(135deg, #FF69B4, #FF1493);
    box-shadow: 0 8px 20px rgba(255, 105, 180, 0.3);
}

/* 性别选择区域隐藏样式 */
.input-group.gender-hidden {
    display: none !important;
    visibility: hidden;
    opacity: 0;
    height: 0;
    margin: 0;
    padding: 0;
    transition: all 0.3s ease;
}

/* 宝宝日期选择器样式 */
.baby-date-picker {
    cursor: pointer;
    border: 2px solid #667eea;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    min-height: 50px;
}

.baby-date-picker:hover {
    border-color: #764ba2;
    box-shadow: 0 0 0 3px rgba(118, 75, 162, 0.1);
}

.date-input-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
}

.date-placeholder {
    color: #999;
    font-size: 15px;
}

.date-placeholder.selected {
    color: #333;
    font-weight: 500;
}

.date-picker-btn {
    width: 32px;
    height: 24px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.date-picker-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
}

.picker-icon {
    font-size: 14px;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 宝宝日期选择器模态框样式 */
.baby-date-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10001;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.baby-modal-container {
    background: white;
    border-radius: 15px;
    width: 100%;
    max-width: 380px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideUp 0.3s ease-out;
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 粉色头部 */
.baby-modal-header {
    background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    font-size: 24px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.baby-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 日期输入框 */
.date-input-container {
    padding: 20px;
    background: white;
}

.date-input-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 15px;
}

.date-value {
    color: #666;
    font-size: 16px;
    font-weight: 500;
}

.date-value.selected {
    color: #333;
}

.date-confirm-btn {
    width: 36px;
    height: 28px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.date-confirm-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
}

.confirm-icon {
    color: white;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 月份导航 */
.month-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
}

.nav-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: #666;
}

.nav-btn:hover {
    background: #f8f9fa;
    border-color: #bbb;
    color: #333;
}

.current-month {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.current-month:hover {
    background: #f8f9fa;
}

.month-dropdown {
    font-size: 12px;
    color: #999;
    transition: transform 0.3s ease;
}

.current-month:hover .month-dropdown {
    transform: rotate(180deg);
}

/* 年份选择器 */
.year-selector {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 10;
    border-radius: 15px;
    overflow: hidden;
}

.year-selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
    border-bottom: 1px solid #f0f0f0;
}

.year-nav-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: white;
    font-weight: bold;
}

.year-nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

#yearRangeDisplay {
    font-size: 16px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.year-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    padding: 20px;
    max-height: 280px;
    overflow-y: auto;
}

.year-item {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: #333;
    background: #f8f9fa;
    border: 1px solid transparent;
}

.year-item:hover {
    background: #FFB6C1;
    color: white;
    transform: scale(1.05);
}

.year-item.selected {
    background: #FF69B4;
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 105, 180, 0.3);
}

.year-item.current {
    border-color: #FFB6C1;
    background: rgba(255, 182, 193, 0.1);
    color: #FF69B4;
    font-weight: 600;
}

.year-selector-footer {
    display: flex;
    justify-content: center;
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #f0f0f0;
}

.year-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #FFB6C1;
    color: white;
}

.year-btn:hover {
    background: #FF69B4;
    transform: translateY(-1px);
}

/* 日历容器 */
.calendar-container {
    padding: 0 20px 20px;
    background: white;
}

/* 星期标题 */
.week-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0;
    margin-bottom: 10px;
}

.week-day {
    padding: 8px 0;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #666;
}

/* 日历网格 */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    color: #333;
    background: white;
    border: 1px solid transparent;
}

.calendar-day:hover {
    background: #f8f9fa;
    border-color: #e0e0e0;
}

.calendar-day.other-month {
    color: #ccc;
}

.calendar-day.selected {
    background: #FFB6C1;
    color: white;
    font-weight: 600;
}

.calendar-day.today {
    border-color: #FFB6C1;
    background: rgba(255, 182, 193, 0.1);
    color: #FF69B4;
    font-weight: 600;
}

/* 底部按钮 */
.calendar-footer {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #f0f0f0;
}

.footer-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-btn {
    background: transparent;
    color: #007AFF;
}

.clear-btn:hover {
    background: rgba(0, 122, 255, 0.1);
}

.today-btn {
    background: transparent;
    color: #007AFF;
}

.today-btn:hover {
    background: rgba(0, 122, 255, 0.1);
}

/* 收起箭头 */
.collapse-arrow {
    display: flex;
    justify-content: center;
    padding: 10px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.collapse-arrow:hover {
    background: #f8f9fa;
}

.collapse-arrow span {
    color: #999;
    font-size: 16px;
}

/* 移动端适配 */
@media (max-width: 480px) {
    .lunar-modal-container {
        margin: 10px;
        max-width: none;
    }
    
    .lunar-modal-header {
        padding: 15px;
    }
    
    .lunar-modal-header h3 {
        font-size: 14px;
    }
    
    .lunar-btn {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .calendar-tab {
        padding: 12px;
        font-size: 14px;
    }
    
    .lunar-picker-container {
        height: 200px;
        padding: 15px 0;
    }
    
    .picker-item {
        height: 35px;
        font-size: 14px;
    }
    
    .picker-item.selected {
        font-size: 16px;
    }
    
    .picker-indicator {
        height: 35px;
    }
    
    .picker-mask-top,
    .picker-mask-bottom {
        height: 82px;
    }
}