/**
 * 生肖运势AI分析模块
 * 集成DeepSeek API进行深度生肖运势分析
 */

class ZodiacFortuneAI {
    constructor() {
        this.isAnalyzing = false;
        this.lastAnalysis = null;
        this.initializeKnowledgeBase();
        // 初始化认证服务
        if (window.initializeAuthServices) {
            window.initializeAuthServices();
        }
    }

    // 初始化生肖学知识库
    initializeKnowledgeBase() {
        this.knowledgeBase = {
            // 十二生肖基础属性
            zodiacAttributes: {
                rat: {
                    name: '鼠', element: '水', personality: ['聪明机敏', '灵活变通', '善于理财', '社交能力强'],
                    strengths: ['适应力强', '学习能力强', '商业头脑', '人际关系好'],
                    weaknesses: ['有时过于精明', '缺乏耐心', '容易急躁', '偶尔小心眼'],
                    compatibleZodiacs: ['龙', '猴', '牛'], conflictZodiacs: ['马', '羊', '鸡'],
                    luckyColors: ['蓝色', '黑色', '白色'], luckyNumbers: [2, 3, 6, 8],
                    luckyDirections: ['北方', '东南方'], bestMonths: [2, 5, 9, 11]
                },
                ox: {
                    name: '牛', element: '土', personality: ['踏实稳重', '勤奋务实', '诚实可靠', '有责任心'],
                    strengths: ['工作能力强', '意志坚定', '值得信赖', '理财谨慎'],
                    weaknesses: ['有时固执', '不够灵活', '社交略弱', '创新不足'],
                    compatibleZodiacs: ['鼠', '蛇', '鸡'], conflictZodiacs: ['羊', '马', '狗'],
                    luckyColors: ['黄色', '橙色', '红色'], luckyNumbers: [1, 4, 5, 9],
                    luckyDirections: ['东北方', '南方'], bestMonths: [3, 7, 10, 12]
                },
                tiger: {
                    name: '虎', element: '木', personality: ['勇敢果断', '领导力强', '独立自主', '富有正义感'],
                    strengths: ['决策能力强', '执行力强', '有魄力', '保护欲强'],
                    weaknesses: ['有时冲动', '过于自信', '缺乏耐心', '听不进建议'],
                    compatibleZodiacs: ['马', '狗', '猴'], conflictZodiacs: ['猴', '蛇', '猪'],
                    luckyColors: ['绿色', '蓝色', '红色'], luckyNumbers: [1, 3, 4, 7],
                    luckyDirections: ['东方', '南方'], bestMonths: [1, 4, 8, 10]
                },
                rabbit: {
                    name: '兔', element: '木', personality: ['温和善良', '心思细腻', '艺术天赋', '追求和谐'],
                    strengths: ['人缘好', '有品味', '善解人意', '做事细致'],
                    weaknesses: ['有时优柔寡断', '缺乏主见', '容易受影响', '避免冲突'],
                    compatibleZodiacs: ['羊', '猪', '狗'], conflictZodiacs: ['鸡', '龙', '鼠'],
                    luckyColors: ['绿色', '红色', '粉色'], luckyNumbers: [3, 4, 6, 9],
                    luckyDirections: ['东方', '东南方'], bestMonths: [2, 5, 8, 11]
                },
                dragon: {
                    name: '龙', element: '土', personality: ['雄心壮志', '领导天赋', '创新能力', '富有远见'],
                    strengths: ['天生领袖', '创造力强', '眼光独到', '影响力大'],
                    weaknesses: ['有时傲慢', '脾气急躁', '完美主义', '压力过大'],
                    compatibleZodiacs: ['鼠', '猴', '鸡'], conflictZodiacs: ['狗', '兔', '龙'],
                    luckyColors: ['金色', '银色', '白色'], luckyNumbers: [1, 6, 7, 8],
                    luckyDirections: ['西北方', '北方'], bestMonths: [1, 6, 7, 12]
                },
                snake: {
                    name: '蛇', element: '火', personality: ['智慧深邃', '直觉敏锐', '神秘魅力', '思考深入'],
                    strengths: ['洞察力强', '判断准确', '有魅力', '理财能力强'],
                    weaknesses: ['有时冷漠', '多疑猜忌', '报复心强', '不善表达'],
                    compatibleZodiacs: ['牛', '鸡', '龙'], conflictZodiacs: ['猪', '虎', '猴'],
                    luckyColors: ['红色', '黄色', '黑色'], luckyNumbers: [2, 8, 9, 0],
                    luckyDirections: ['西南方', '南方'], bestMonths: [4, 6, 9, 11]
                },
                horse: {
                    name: '马', element: '火', personality: ['热情奔放', '自由独立', '积极向上', '善于交际'],
                    strengths: ['行动力强', '乐观开朗', '适应力强', '口才好'],
                    weaknesses: ['有时急躁', '缺乏耐心', '不够专注', '喜欢变化'],
                    compatibleZodiacs: ['虎', '羊', '狗'], conflictZodiacs: ['鼠', '牛', '兔'],
                    luckyColors: ['红色', '紫色', '橙色'], luckyNumbers: [2, 3, 7, 8],
                    luckyDirections: ['南方', '西南方'], bestMonths: [3, 6, 9, 12]
                },
                goat: {
                    name: '羊', element: '土', personality: ['温和体贴', '艺术天赋', '善良仁慈', '追求美好'],
                    strengths: ['有同情心', '创造力强', '审美能力强', '团队合作好'],
                    weaknesses: ['有时软弱', '缺乏自信', '依赖性强', '决断力不足'],
                    compatibleZodiacs: ['兔', '马', '猪'], conflictZodiacs: ['牛', '狗', '鼠'],
                    luckyColors: ['绿色', '红色', '紫色'], luckyNumbers: [3, 4, 5, 9],
                    luckyDirections: ['南方', '东南方'], bestMonths: [5, 7, 10, 12]
                },
                monkey: {
                    name: '猴', element: '金', personality: ['聪明伶俐', '幽默风趣', '多才多艺', '善于变通'],
                    strengths: ['学习能力强', '适应力强', '创新能力强', '社交能力强'],
                    weaknesses: ['有时浮躁', '不够专一', '喜欢炫耀', '缺乏耐心'],
                    compatibleZodiacs: ['鼠', '龙', '蛇'], conflictZodiacs: ['虎', '猪', '猴'],
                    luckyColors: ['白色', '金色', '蓝色'], luckyNumbers: [4, 9, 1, 7],
                    luckyDirections: ['西北方', '北方'], bestMonths: [1, 4, 7, 10]
                },
                rooster: {
                    name: '鸡', element: '金', personality: ['勤奋认真', '完美主义', '组织能力强', '注重细节'],
                    strengths: ['工作能力强', '责任心强', '条理清晰', '执行力强'],
                    weaknesses: ['有时挑剔', '过于严格', '喜欢批评', '固执己见'],
                    compatibleZodiacs: ['牛', '龙', '蛇'], conflictZodiacs: ['兔', '鸡', '狗'],
                    luckyColors: ['金色', '棕色', '黄色'], luckyNumbers: [5, 7, 8, 0],
                    luckyDirections: ['西方', '西南方'], bestMonths: [2, 5, 8, 11]
                },
                dog: {
                    name: '狗', element: '土', personality: ['忠诚可靠', '正直诚实', '有责任心', '善于保护'],
                    strengths: ['值得信赖', '正义感强', '团队精神好', '执行力强'],
                    weaknesses: ['有时悲观', '过于担心', '固执保守', '不善变通'],
                    compatibleZodiacs: ['虎', '兔', '马'], conflictZodiacs: ['龙', '羊', '鸡'],
                    luckyColors: ['红色', '绿色', '紫色'], luckyNumbers: [3, 4, 9, 0],
                    luckyDirections: ['东方', '南方'], bestMonths: [1, 6, 9, 12]
                },
                pig: {
                    name: '猪', element: '水', personality: ['善良纯真', '乐观开朗', '大方慷慨', '享受生活'],
                    strengths: ['人缘好', '心胸宽广', '有福气', '生活品质高'],
                    weaknesses: ['有时懒散', '缺乏目标', '容易满足', '理财不佳'],
                    compatibleZodiacs: ['兔', '羊', '虎'], conflictZodiacs: ['蛇', '猴', '猪'],
                    luckyColors: ['黄色', '灰色', '棕色'], luckyNumbers: [2, 5, 8, 9],
                    luckyDirections: ['东南方', '南方'], bestMonths: [4, 6, 10, 11]
                }
            },

            // 2025年流年分析
            yearAnalysis2025: {
                yearElement: '乙巳火', 
                yearCharacter: '蛇年', 
                generalTrend: '智慧与变革之年',
                keyEvents: ['经济转型', '科技突破', '人际关系调整', '健康养生重视'],
                luckyMonths: [2, 5, 8, 11], 
                challengeMonths: [3, 6, 9, 12],
                overallThemes: ['变革创新', '深度思考', '精神成长', '财富积累']
            },

            // 运势分析维度
            fortuneAspects: {
                overall: { name: '整体运势', weight: 0.25 },
                career: { name: '事业工作', weight: 0.2 },
                wealth: { name: '财运理财', weight: 0.2 },
                love: { name: '感情婚姻', weight: 0.15 },
                health: { name: '健康运势', weight: 0.1 },
                study: { name: '学业发展', weight: 0.1 }
            }
        };
    }

    // 生成AI生肖运势分析提示词
    generateZodiacFortunePrompt(userData, basicAnalysis) {
        const { zodiac, birthYear, birthMonth, gender } = userData;
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[zodiac];
        const yearAnalysis = this.knowledgeBase.yearAnalysis2025;

        const genderText = gender === 'male' ? '男性' : '女性';

        const prompt = `请对以下信息进行深度的2025年生肖运势详批：

个人信息：
- 生肖：${zodiacAttr.name}（${zodiac}）
- 性别：${genderText}
- 出生年份：${birthYear}年
- 出生月份：${birthMonth}月
- 五行属性：${zodiacAttr.element}


请严格按照以下JSON格式返回分析结果，不要包含任何JSON之外的文本：

{
  "basic": {
    "zodiac": "${zodiac}",
    "zodiacName": "${zodiacAttr.name}",
    "gender": "${gender}",
    "birthYear": ${birthYear},
    "birthMonth": ${birthMonth},
    "element": "${zodiacAttr.element}",
    "score": ${basicAnalysis.overall.score}
  },
  "attributes": {
    "personality": ${JSON.stringify(zodiacAttr.personality)},
    "strengths": ${JSON.stringify(zodiacAttr.strengths)},
    "weaknesses": ${JSON.stringify(zodiacAttr.weaknesses)},
    "compatibleZodiacs": ${JSON.stringify(zodiacAttr.compatibleZodiacs)},
    "conflictZodiacs": ${JSON.stringify(zodiacAttr.conflictZodiacs)}
  },
  "sections": {
    "overall": "2025年整体运势评估：结合生肖特质与流年特点，深度分析整体运势走向，要求200字以上",
    "career": "事业工作运势详批：职场发展、事业机遇、工作变动、晋升可能性分析，要求200字以上",
    "wealth": "财运理财运势指导：正财偏财、投资理财、财运时机、理财建议，要求200字以上",
    "love": "感情婚姻运势解析：桃花运、恋爱运、婚姻运、家庭关系发展，要求200字以上",
    "health": "健康运势与养生：身体健康状况、易患疾病、养生重点、运动建议，要求200字以上",
    "relationship": "人际关系与贵人：人际运势、贵人相助、小人防范、社交建议，要求200字以上",
    "study": "学习成长与发展：学业运势、技能提升、个人成长、发展方向，要求200字以上",
    "enhancement": "开运化解建议：具体的开运方法、化解不利、增强运势的实用建议，要求200字以上"
  },
  "scores": {
    "overall": ${basicAnalysis.overall.score},
    "career": ${basicAnalysis.career.score},
    "wealth": ${basicAnalysis.wealth.score},
    "love": ${basicAnalysis.love.score},
    "health": ${basicAnalysis.health.score}
  },
  "recommendations": [
    "具体建议1",
    "具体建议2",
    "具体建议3"
  ],
  "luckyElements": {
    "colors": ${JSON.stringify(zodiacAttr.luckyColors)},
    "numbers": ${JSON.stringify(zodiacAttr.luckyNumbers)},
    "directions": ${JSON.stringify(zodiacAttr.luckyDirections)},
    "months": ${JSON.stringify(zodiacAttr.bestMonths)}
  },
  "timestamp": "${new Date().toISOString()}"
}

请用专业的生肖学术语，语言要生动有趣，富有感染力。`;

        return prompt;
    }

    // 调用AI接口进行深度分析
    async generateAIFortune(userData, basicAnalysis) {
        if (!window.aIService) {
            throw new Error('AI服务未初始化，请检查配置');
        }

        console.log('开始AI生肖运势分析:', userData.zodiac);

        try {
            const prompt = this.generateZodiacFortunePrompt(userData, basicAnalysis);
            console.log('AI分析提示词:', prompt);

            // 使用统一AI调用接口
            const systemPrompt = "你是一位精通中华传统生肖学和未来运势的专业大师，能够进行准确的生肖运势分析和预测。请严格按照用户要求的JSON格式返回分析结果，不要包含任何JSON之外的文本。确保返回的是有效的JSON格式，每个分析部分都要详细且不少于200字。";
            const aiResponse = await window.aIService.callAI(prompt, systemPrompt, {
                enableFallback: true  // 启用服务降级
            });

            console.log('AI分析响应:', aiResponse);
            return this.parseAIFortuneResponse(aiResponse, userData, basicAnalysis);

        } catch (error) {
            console.error('AI生肖运势分析失败:', error);
            throw error;
        }
    }

    // 解析AI运势分析响应
    parseAIFortuneResponse(response, userData, basicAnalysis) {
        try {
            // 尝试解析JSON格式的响应
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsedResponse = JSON.parse(jsonMatch[0]);
                console.log(parsedResponse)
                // 验证JSON结构是否完整
                if (parsedResponse.basic && parsedResponse.sections) {
                    return {
                        success: true,
                        fortuneAnalysis: {
                            method: 'ai',
                            basic: parsedResponse.basic,
                            attributes: parsedResponse.attributes,
                            sections: parsedResponse.sections,
                            scores: parsedResponse.scores,
                            recommendations: parsedResponse.recommendations || [],
                            luckyElements: parsedResponse.luckyElements || {},
                            timestamp: parsedResponse.timestamp || new Date().toISOString()
                        }
                    };
                }
            }
        } catch (error) {
            console.warn('JSON解析失败，尝试文本解析:', error);
        }

        // 如果JSON解析失败，降级到文本解析
        return this.parseTextFortuneResponse(response, userData, basicAnalysis);
    }

    // 文本响应解析（降级方案）
    parseTextFortuneResponse(response, userData, basicAnalysis) {
        try {
            // 尝试从响应中提取结构化信息
            const sections = {
                overall: this.extractSection(response, ['整体运势评估', '2025年整体运势', '总体运势']),
                career: this.extractSection(response, ['事业工作运势', '事业运势', '工作运势']),
                wealth: this.extractSection(response, ['财运理财运势', '财运运势', '理财运势']),
                love: this.extractSection(response, ['感情婚姻运势', '感情运势', '婚姻运势']),
                health: this.extractSection(response, ['健康运势与养生', '健康运势', '养生建议']),
                relationship: this.extractSection(response, ['人际关系与贵人', '人际关系', '贵人运势']),
                study: this.extractSection(response, ['学习成长与发展', '学习运势', '成长发展']),
                enhancement: this.extractSection(response, ['开运化解建议', '开运方法', '化解建议'])
            };

            const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];

            return {
                success: true,
                fortuneAnalysis: {
                    method: 'ai',
                    basic: {
                        zodiac: userData.zodiac,
                        zodiacName: zodiacAttr.name,
                        gender: userData.gender,
                        birthYear: userData.birthYear,
                        birthMonth: userData.birthMonth,
                        element: zodiacAttr.element,
                        score: this.extractAIScore(response) || this.calculateAIScore(sections)
                    },
                    attributes: {
                        personality: zodiacAttr.personality,
                        strengths: zodiacAttr.strengths,
                        weaknesses: zodiacAttr.weaknesses,
                        compatibleZodiacs: zodiacAttr.compatibleZodiacs,
                        conflictZodiacs: zodiacAttr.conflictZodiacs
                    },
                    sections: sections,
                    scores: {
                        overall: basicAnalysis.overall.score,
                        career: basicAnalysis.career.score,
                        wealth: basicAnalysis.wealth.score,
                        love: basicAnalysis.love.score,
                        health: basicAnalysis.health.score
                    },
                    recommendations: this.extractRecommendations(response),
                    luckyElements: {
                        colors: zodiacAttr.luckyColors,
                        numbers: zodiacAttr.luckyNumbers,
                        directions: zodiacAttr.luckyDirections,
                        months: zodiacAttr.bestMonths
                    },
                    timestamp: new Date().toISOString()
                }
            };

        } catch (error) {
            console.warn('AI响应解析失败，使用原始文本:', error);
            const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];

            return {
                success: true,
                fortuneAnalysis: {
                    method: 'ai',
                    basic: {
                        zodiac: userData.zodiac,
                        zodiacName: zodiacAttr.name,
                        gender: userData.gender,
                        birthYear: userData.birthYear,
                        birthMonth: userData.birthMonth,
                        element: zodiacAttr.element,
                        score: 75
                    },
                    attributes: {
                        personality: zodiacAttr.personality,
                        strengths: zodiacAttr.strengths,
                        weaknesses: zodiacAttr.weaknesses,
                        compatibleZodiacs: zodiacAttr.compatibleZodiacs,
                        conflictZodiacs: zodiacAttr.conflictZodiacs
                    },
                    sections: { overall: response.substring(0, 500) + '...' },
                    scores: {
                        overall: basicAnalysis.overall.score,
                        career: basicAnalysis.career.score,
                        wealth: basicAnalysis.wealth.score,
                        love: basicAnalysis.love.score,
                        health: basicAnalysis.health.score
                    },
                    recommendations: ['建议详细咨询专业生肖运势大师'],
                    luckyElements: {
                        colors: zodiacAttr.luckyColors,
                        numbers: zodiacAttr.luckyNumbers,
                        directions: zodiacAttr.luckyDirections,
                        months: zodiacAttr.bestMonths
                    },
                    timestamp: new Date().toISOString()
                }
            };
        }
    }

    // 提取特定章节内容
    extractSection(text, keywords) {
        for (const keyword of keywords) {
            const regex = new RegExp(`[*]*\\s*\\d*[、.]?\\s*[*]*\\s*${keyword}[*]*[：:]?([\\s\\S]*?)(?=\\n\\n|\\n[*]*\\s*\\d*[、.]?\\s*[*]*\\s*[一-龥]+[*]*[：:]|$)`, 'i');
            const match = text.match(regex);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return '';
    }

    // 生成AI摘要
    generateAISummary(text, userData) {
        const zodiacName = this.knowledgeBase.zodiacAttributes[userData.zodiac].name;
        const sentences = text.split(/[。！？]/);
        let summary = `生肖${zodiacName}的2025年运势：`;
        
        // 提取关键评价句子
        const keyPhrases = ['整体', '运势', '财运', '事业', '感情', '健康', '2025'];
        const relevantSentences = sentences.filter(sentence => 
            keyPhrases.some(phrase => sentence.includes(phrase)) && sentence.length > 15
        );
        
        if (relevantSentences.length > 0) {
            summary += relevantSentences.slice(0, 2).join('。') + '。';
        } else {
            summary += sentences.slice(1, 3).join('。') + '。';
        }
        
        return summary.length > 200 ? summary.substring(0, 200) + '...' : summary;
    }

    // 提取建议
    extractRecommendations(text) {
        const recommendations = [];
        const lines = text.split('\n');
        
        for (const line of lines) {
            if (line.includes('建议') || line.includes('推荐') || line.includes('应该') || 
                line.includes('宜') || line.includes('适合') || line.includes('注意')) {
                const cleanLine = line.trim().replace(/^[•\-\*\d\.]+/, '').trim();
                if (cleanLine.length > 8) {
                    recommendations.push(cleanLine);
                }
            }
        }
        
        return recommendations.length > 0 ? recommendations.slice(0, 8) : 
               ['保持积极乐观的心态', '注意身体健康', '把握事业机遇', '维护人际关系'];
    }

    // 提取AI评分
    extractAIScore(text) {
        const scoreMatch = text.match(/(\d{1,2})[分点]/);
        if (scoreMatch) {
            const score = parseInt(scoreMatch[1]);
            return score >= 30 && score <= 100 ? score : null;
        }
        return null;
    }

    // 计算AI评分
    calculateAIScore(sections) {
        let score = 75;
        
        // 根据分析内容的详细程度和积极性调整分数
        const positiveWords = ['吉', '好', '佳', '优', '利', '旺', '顺', '成功', '发达', '兴旺', '机遇'];
        const negativeWords = ['凶', '差', '不利', '阻碍', '困难', '破财', '病灾', '不顺', '挫折'];
        
        const allContent = Object.values(sections).join(' ');
        
        const positiveCount = positiveWords.reduce((count, word) => 
            count + (allContent.match(new RegExp(word, 'g')) || []).length, 0);
        const negativeCount = negativeWords.reduce((count, word) => 
            count + (allContent.match(new RegExp(word, 'g')) || []).length, 0);
        
        score += positiveCount * 2 - negativeCount * 2;
        
        return Math.min(95, Math.max(45, score));
    }

    // 提取开运要素
    extractLuckyElements(text, zodiac) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[zodiac];
        const elements = [...zodiacAttr.luckyColors];
        
        // 从文本中提取额外的开运要素
        const elementKeywords = ['颜色', '方位', '数字', '饰品', '植物', '食物'];
        elementKeywords.forEach(keyword => {
            const regex = new RegExp(`${keyword}[：:]?([^。！？\\n]*?)`, 'g');
            const matches = text.match(regex);
            if (matches) {
                matches.forEach(match => {
                    const extracted = match.replace(keyword, '').replace(/[：:]/g, '').trim();
                    if (extracted.length > 0 && extracted.length < 20) {
                        elements.push(extracted);
                    }
                });
            }
        });
        
        return [...new Set(elements)].slice(0, 6);
    }

    // 生成本地运势分析（AI失败时的降级方案）
    generateLocalFortune(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];

        // 生成各个分析章节
        const sections = {
            overall: this.generateLocalOverall(userData, basicAnalysis),
            career: this.generateLocalCareer(userData, basicAnalysis),
            wealth: this.generateLocalWealth(userData, basicAnalysis),
            love: this.generateLocalLove(userData, basicAnalysis),
            health: this.generateLocalHealth(userData, basicAnalysis),
            relationship: this.generateLocalRelationship(userData, basicAnalysis),
            study: this.generateLocalStudy(userData, basicAnalysis),
            enhancement: this.generateLocalEnhancement(userData, basicAnalysis)
        };

        return {
            success: true,
            fortuneAnalysis: {
                method: 'local',
                basic: {
                    zodiac: userData.zodiac,
                    zodiacName: zodiacAttr.name,
                    gender: userData.gender,
                    birthYear: userData.birthYear,
                    birthMonth: userData.birthMonth,
                    element: zodiacAttr.element,
                    score: basicAnalysis.overall.score
                },
                attributes: {
                    personality: zodiacAttr.personality,
                    strengths: zodiacAttr.strengths,
                    weaknesses: zodiacAttr.weaknesses,
                    compatibleZodiacs: zodiacAttr.compatibleZodiacs,
                    conflictZodiacs: zodiacAttr.conflictZodiacs
                },
                sections: sections,
                scores: {
                    overall: basicAnalysis.overall.score,
                    career: basicAnalysis.career.score,
                    wealth: basicAnalysis.wealth.score,
                    love: basicAnalysis.love.score,
                    health: basicAnalysis.health.score
                },
                recommendations: this.generateLocalRecommendations(userData, zodiacAttr),
                luckyElements: {
                    colors: zodiacAttr.luckyColors,
                    numbers: zodiacAttr.luckyNumbers,
                    directions: zodiacAttr.luckyDirections,
                    months: zodiacAttr.bestMonths
                },
                timestamp: new Date().toISOString()
            }
        };


    }

    // 生成本地人际关系分析
    generateLocalRelationship(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        return `人际关系方面，生肖${zodiacAttr.name}在2025年贵人运不错。与生肖${zodiacAttr.compatibleZodiacs.join('、')}的人合作会有好结果。要注意避免与生肖${zodiacAttr.conflictZodiacs.join('、')}的人发生冲突，保持和谐的人际关系。`;
    }

    // 生成本地学习发展分析
    generateLocalStudy(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        return `学习发展方面，您的${zodiacAttr.personality[1]}特质有助于知识吸收。建议在${zodiacAttr.bestMonths.join('、')}月加强学习，发挥${zodiacAttr.strengths[1]}的优势。持续提升自己的专业技能，会为未来发展打下良好基础。`;
    }

    // 生成本地开运建议
    generateLocalEnhancement(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        return `开运化解方面，建议佩戴${zodiacAttr.luckyColors[0]}色饰品增强运势，多朝${zodiacAttr.luckyDirections[0]}发展事业。幸运数字${zodiacAttr.luckyNumbers.join('、')}可以多加运用。在${zodiacAttr.bestMonths.join('、')}月把握重要机遇，避免在冲突月份做重大决定。`;
    }

    // 生成本地整体运势
    generateLocalOverall(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        const score = basicAnalysis.overall.score;
        
        let analysis = `2025年是生肖${zodiacAttr.name}的`;
        if (score >= 85) {
            analysis += '大运之年，各方面运势都很不错。';
        } else if (score >= 70) {
            analysis += '平稳发展之年，整体运势良好。';
        } else {
            analysis += '需要谨慎的一年，宜守不宜攻。';
        }
        
        analysis += `您的${zodiacAttr.element}行属性与蛇年相配，`;
        analysis += zodiacAttr.element === '火' ? '运势特别旺盛。' : '需要注意平衡发展。';
        
        return analysis;
    }

    // 生成本地事业运势
    generateLocalCareer(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        return `事业方面，您的${zodiacAttr.strengths[0]}将发挥重要作用。建议发挥${zodiacAttr.personality[0]}的特点，在工作中会有不错的表现。需要注意的是${zodiacAttr.weaknesses[0]}，适度调整会有更好发展。`;
    }

    // 生成本地财运运势
    generateLocalWealth(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        return `财运方面，2025年${zodiacAttr.name}的理财运势相对稳定。建议谨慎投资，选择${zodiacAttr.luckyNumbers[0]}、${zodiacAttr.luckyNumbers[1]}等幸运数字相关的理财产品。避免在${userData.birthMonth}月进行大额投资。`;
    }

    // 生成本地感情运势
    generateLocalLove(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        const genderText = userData.gender === 'male' ? '男性' : '女性';
        return `感情方面，${genderText}生肖${zodiacAttr.name}在2025年感情运势温和。单身者可以关注生肖${zodiacAttr.compatibleZodiacs[0]}、${zodiacAttr.compatibleZodiacs[1]}的异性。已有伴侣者要避免与生肖${zodiacAttr.conflictZodiacs[0]}的人产生误会。`;
    }

    // 生成本地健康运势
    generateLocalHealth(userData, basicAnalysis) {
        const zodiacAttr = this.knowledgeBase.zodiacAttributes[userData.zodiac];
        return `健康方面，${zodiacAttr.element}行属性的您在2025年要特别注意调理身体。建议多穿${zodiacAttr.luckyColors[0]}色衣物，朝${zodiacAttr.luckyDirections[0]}多活动。在${zodiacAttr.bestMonths.join('、')}月要特别注意养生保健。`;
    }

    // 生成本地建议
    generateLocalRecommendations(userData, zodiacAttr) {
        return [
            `佩戴${zodiacAttr.luckyColors[0]}色饰品增强运势`,
            `多朝${zodiacAttr.luckyDirections[0]}发展事业`,
            `与生肖${zodiacAttr.compatibleZodiacs[0]}的人多合作`,
            `在${zodiacAttr.bestMonths[0]}、${zodiacAttr.bestMonths[1]}月把握机遇`,
            '保持积极乐观的心态',
            '注重身体健康和心理平衡'
        ];
    }

    getScoreDescription(score) {
        if (score >= 85) return '运势旺盛';
        if (score >= 70) return '运势良好';
        if (score >= 55) return '运势平稳';
        return '运势一般';
    }

    // 完整的AI生肖运势分析流程
    async analyzeZodiacFortuneWithAI(userData, basicAnalysis) {
        this.isAnalyzing = true;
        
        try {
            console.log('开始AI生肖运势分析:', userData.zodiac);

            // 尝试AI分析
            if (window.AI_CONFIG && window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
                try {
                    const result = await this.generateAIFortune(userData, basicAnalysis);
                    this.lastAnalysis = result;
                    console.log(result)
                    return result;
                } catch (error) {
                    console.warn('AI分析失败，使用本地分析:', error);
                    const result = this.generateLocalFortune(userData, basicAnalysis);
                    this.lastAnalysis = result;
                    return result;
                }
            } else {
                const result = this.generateLocalFortune(userData, basicAnalysis);
                this.lastAnalysis = result;
                return result;
            }
            
        } catch (error) {
            console.error('生肖运势AI分析失败:', error);
            throw error;
        } finally {
            this.isAnalyzing = false;
        }
    }
}

// 导出模块
window.ZodiacFortuneAI = ZodiacFortuneAI; 