// 手机号测吉凶页面JavaScript功能

// 全局变量
let canvas, ctx;
let currentAnalysis = null;
let phoneNumberAI = null;
let aiService = null;

// 数字能量磁场配置
const magneticFields = {
    // 吉祥磁场
    shengqi: { // 生气磁场
        numbers: ['14', '41', '23', '32', '68', '86', '77', '95', '59'],
        name: '生气磁场',
        type: 'positive',
        score: 90,
        energy: '积极向上',
        description: '代表活力、创新、积极进取的能量。有利于事业发展和人际关系。',
        effects: ['提升活力', '增强创新能力', '促进事业发展', '改善人际关系']
    },
    tianyi: { // 天医磁场
        numbers: ['13', '31', '24', '42', '69', '96', '78', '87'],
        name: '天医磁场',
        type: 'positive',
        score: 95,
        energy: '财富健康',
        description: '主财运和健康，带来财富积累和身体康健。是最吉祥的磁场之一。',
        effects: ['增强财运', '促进健康', '带来贵人', '事业顺利']
    },
    yannian: { // 延年磁场
        numbers: ['19', '91', '28', '82', '67', '76', '34', '43'],
        name: '延年磁场',
        type: 'positive',
        score: 85,
        energy: '贵人助力',
        description: '代表贵人运和人际关系，助力事业发展，延年益寿。',
        effects: ['贵人相助', '人际和谐', '事业稳定', '延年益寿']
    },
    fuwei: { // 伏位磁场
        numbers: ['11', '22', '33', '44', '55', '66', '77', '88', '99'],
        name: '伏位磁场',
        type: 'neutral',
        score: 70,
        energy: '稳定平和',
        description: '稳定平和的能量，适合稳健发展，但缺乏突破性。',
        effects: ['保持稳定', '减少变动', '平稳发展', '安分守己']
    },
    
    // 凶煞磁场
    jueming: { // 绝命磁场
        numbers: ['12', '21', '39', '93', '48', '84', '67', '76'],
        name: '绝命磁场',
        type: 'negative',
        score: 30,
        energy: '破坏极强',
        description: '最凶的磁场，容易导致破财、健康问题和人际冲突。',
        effects: ['财运不佳', '健康问题', '人际冲突', '事业阻碍']
    },
    wugui: { // 五鬼磁场
        numbers: ['18', '81', '29', '92', '37', '73', '46', '64'],
        name: '五鬼磁场',
        type: 'negative',
        score: 40,
        energy: '意外频发',
        description: '容易招致意外事件、小人是非和财务损失。',
        effects: ['意外事件', '小人是非', '财务损失', '情绪波动']
    },
    liusha: { // 六煞磁场
        numbers: ['16', '61', '27', '72', '38', '83', '49', '94'],
        name: '六煞磁场',
        type: 'negative',
        score: 45,
        energy: '感情波折',
        description: '主要影响感情和人际关系，容易导致桃花劫和感情纠纷。',
        effects: ['感情波折', '桃花劫', '人际问题', '情绪不稳']
    },
    huohai: { // 祸害磁场
        numbers: ['17', '71', '26', '62', '35', '53', '48', '84'],
        name: '祸害磁场',
        type: 'negative',
        score: 50,
        energy: '口舌是非',
        description: '容易引发口舌是非、官司纠纷和人际矛盾。',
        effects: ['口舌是非', '官司纠纷', '人际矛盾', '工作不顺']
    }
};

// 运营商配置
const carriers = {
    '134': '中国移动', '135': '中国移动', '136': '中国移动', '137': '中国移动',
    '138': '中国移动', '139': '中国移动', '147': '中国移动', '150': '中国移动',
    '151': '中国移动', '152': '中国移动', '157': '中国移动', '158': '中国移动',
    '159': '中国移动', '182': '中国移动', '183': '中国移动', '184': '中国移动',
    '187': '中国移动', '188': '中国移动', '198': '中国移动',
    
    '130': '中国联通', '131': '中国联通', '132': '中国联通', '145': '中国联通',
    '155': '中国联通', '156': '中国联通', '166': '中国联通', '175': '中国联通',
    '176': '中国联通', '185': '中国联通', '186': '中国联通',
    
    '133': '中国电信', '149': '中国电信', '153': '中国电信', '173': '中国电信',
    '177': '中国电信', '180': '中国电信', '181': '中国电信', '189': '中国电信',
    '199': '中国电信'
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化认证服务
    window.unifiedAuthService = new UnifiedAuthService();
    window.memberService = new MemberService();
    window.orderPaymentManager = new OrderPaymentManager();
    initializeCanvas();
    setupEventListeners();
    animateCanvas();
    initializeAIServices();
});

// 初始化AI服务
function initializeAIServices() {
    try {
        console.log('🤖 初始化手机号AI分析服务...');
        
        // 检查必要的类是否存在
        if (typeof PhoneNumberAI === 'undefined') {
            console.error('❌ PhoneNumberAI 类未找到，请检查模块是否正确加载');
            return;
        }
        
        if (typeof AIService === 'undefined' && window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
            console.error('❌ AIService 类未找到，切换到离线模式');
            window.AI_CONFIG.SERVICE_TYPE = 'offline';
        }
        
        // 初始化AI服务
        if (window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
            try {
                aiService = new AIService(window.AI_CONFIG);
                window.aIService = aiService;
                console.log('✅ AI服务初始化成功:', window.AI_CONFIG.SERVICE_TYPE);
            } catch (error) {
                console.warn('⚠️ AI服务初始化失败，切换到离线模式:', error);
                window.AI_CONFIG.SERVICE_TYPE = 'offline';
            }
        }
        
        // 初始化手机号AI
        phoneNumberAI = new PhoneNumberAI();
        console.log('✅ 手机号AI分析模块初始化成功');
        
    } catch (error) {
        console.error('❌ AI服务初始化失败:', error);
        // 继续使用基础功能，不影响主要功能
    }
}


// 初始化Canvas动画
function initializeCanvas() {
    canvas = document.getElementById('phoneCanvas');
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    resizeCanvas();
    
    window.addEventListener('resize', resizeCanvas);
}

// 调整Canvas尺寸
function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
}

// Canvas动画 - 数字粒子效果
function animateCanvas() {
    if (!ctx) return;
    
    const particles = [];
    const numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const symbols = ['📱', '📞', '📲', '💬', '📡', '🔢', '⚡', '💫', '✨', '🌟'];
    
    // 创建粒子
    for (let i = 0; i < 30; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.8,
            vy: (Math.random() - 0.5) * 0.8,
            symbol: Math.random() > 0.7 ? symbols[Math.floor(Math.random() * symbols.length)] : numbers[Math.floor(Math.random() * numbers.length)],
            opacity: Math.random() * 0.5 + 0.3,
            size: Math.random() * 15 + 10,
            rotation: Math.random() * Math.PI * 2,
            rotationSpeed: (Math.random() - 0.5) * 0.02
        });
    }
    
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        particles.forEach(particle => {
            // 更新位置和旋转
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.rotation += particle.rotationSpeed;
            
            // 边界检测
            if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            
            // 绘制粒子
            ctx.save();
            ctx.globalAlpha = particle.opacity;
            ctx.font = `${particle.size}px Arial`;
            ctx.textAlign = 'center';
            ctx.translate(particle.x, particle.y);
            ctx.rotate(particle.rotation);
            
            if (particle.symbol.length === 1 && !isNaN(particle.symbol)) {
                // 数字粒子 - 添加发光效果
                ctx.shadowColor = '#28a745';
                ctx.shadowBlur = 10;
                ctx.fillStyle = '#28a745';
            } else {
                // 符号粒子
                ctx.fillStyle = '#20c997';
            }
            
            ctx.fillText(particle.symbol, 0, 0);
            ctx.restore();
        });
        
        requestAnimationFrame(animate);
    }
    
    animate();
}

// 设置事件监听器
function setupEventListeners() {
    const form = document.getElementById('phoneForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    const phoneInput = document.getElementById('phoneNumber');
    if (phoneInput) {
        phoneInput.addEventListener('input', formatPhoneInput);
        phoneInput.addEventListener('keypress', validateNumberInput);
    }
}

// 处理手机号输入格式
function formatPhoneInput(e) {
    let value = e.target.value.replace(/[^\d]/g, '');
    e.target.value = value;
}

// 验证数字输入
function validateNumberInput(e) {
    const char = String.fromCharCode(e.which);
    if (!/[0-9]/.test(char)) {
        e.preventDefault();
    }
}

// 使用示例号码
function useExample(phoneNumber) {
    const input = document.getElementById('phoneNumber');
    if (input) {
        input.value = phoneNumber;
        input.focus();
    }
}

// 处理表单提交
function handleFormSubmit(e) {
    e.preventDefault();
    
    const phoneNumber = document.getElementById('phoneNumber').value.trim();
    
    if (!validatePhoneNumber(phoneNumber)) {
        alert('请输入正确的11位手机号码');
        return;
    }

    // 服务配置
    const serviceConfig = {
        type: 'phone-analysis',
        name: '手机号测吉凶',
        price: 12.9,
        description: '通过数字能量学分析手机号码吉凶'
    };

    const userData = {
        phoneNumber: phoneNumber
    };

    // 创建订单并支付
    try {
        window.orderPaymentManager.createOrderAndPay(
            serviceConfig,
            userData,
            // 支付成功回调
            async function(order, paymentResult) {
                console.log('支付成功，开始手机号分析');
                await performPhoneAnalysis(phoneNumber);
            },
            // 取消支付回调
            function(order) {
                console.log('用户取消支付');
            }
        );
    } catch (error) {
        console.error('订单创建失败:', error);
        alert('创建订单失败，请稍后重试');
    }
}

// 执行手机号分析
async function performPhoneAnalysis(phoneNumber) {
    try {
        showLoading();
        
        setTimeout(() => {
            const analysis = analyzePhoneNumber(phoneNumber);
            displayAnalysisResult(analysis);
            hideLoading();
        }, 2500);
    } catch (error) {
        console.error('手机号分析失败:', error);
        hideLoading();
        alert('分析失败，请稍后重试');
    }
}

// 验证手机号格式
function validatePhoneNumber(phoneNumber) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
}

// 分析手机号码
function analyzePhoneNumber(phoneNumber) {
    const analysis = {
        phoneNumber: phoneNumber,
        carrier: getCarrier(phoneNumber),
        overallScore: 0,
        magneticAnalysis: [],
        numberBreakdown: [],
        energyBalance: {},
        suggestions: []
    };
    
    const combinations = extractCombinations(phoneNumber);
    let totalScore = 0;
    let scoreCount = 0;
    
    combinations.forEach(combo => {
        const field = findMagneticField(combo);
        if (field) {
            analysis.magneticAnalysis.push({
                combination: combo,
                field: field,
                position: getPositionInPhone(phoneNumber, combo)
            });
            totalScore += field.score;
            scoreCount++;
        }
    });
    
    analysis.overallScore = scoreCount > 0 ? Math.round(totalScore / scoreCount) : 60;
    analysis.numberBreakdown = analyzeIndividualNumbers(phoneNumber);
    analysis.energyBalance = analyzeEnergyBalance(analysis.magneticAnalysis);
    analysis.suggestions = generateSuggestions(analysis);
    
    currentAnalysis = analysis;
    return analysis;
}

// 获取运营商信息
function getCarrier(phoneNumber) {
    const prefix = phoneNumber.substring(0, 3);
    return carriers[prefix] || '未知运营商';
}

// 提取数字组合
function extractCombinations(phoneNumber) {
    const combinations = [];
    for (let i = 0; i < phoneNumber.length - 1; i++) {
        const combo = phoneNumber.substring(i, i + 2);
        combinations.push(combo);
    }
    return combinations;
}

// 查找磁场类型
function findMagneticField(combination) {
    for (const fieldKey in magneticFields) {
        const field = magneticFields[fieldKey];
        if (field.numbers.includes(combination)) {
            return field;
        }
    }
    return null;
}

// 获取组合在手机号中的位置
function getPositionInPhone(phoneNumber, combination) {
    const index = phoneNumber.indexOf(combination);
    if (index <= 2) return '前段';
    if (index <= 6) return '中段';
    return '后段';
}

// 分析单个数字
function analyzeIndividualNumbers(phoneNumber) {
    const breakdown = [];
    const numberMeanings = {
        '0': { meaning: '圆满', energy: 'neutral', description: '代表圆满和完整' },
        '1': { meaning: '独立', energy: 'positive', description: '代表独立和领导力' },
        '2': { meaning: '合作', energy: 'positive', description: '代表合作和平衡' },
        '3': { meaning: '创造', energy: 'positive', description: '代表创造和表达' },
        '4': { meaning: '稳定', energy: 'neutral', description: '代表稳定和务实' },
        '5': { meaning: '自由', energy: 'positive', description: '代表自由和变化' },
        '6': { meaning: '和谐', energy: 'positive', description: '代表和谐和关爱' },
        '7': { meaning: '智慧', energy: 'positive', description: '代表智慧和直觉' },
        '8': { meaning: '成功', energy: 'positive', description: '代表成功和物质' },
        '9': { meaning: '完成', energy: 'positive', description: '代表完成和奉献' }
    };
    
    for (let i = 0; i < phoneNumber.length; i++) {
        const digit = phoneNumber[i];
        const meaning = numberMeanings[digit];
        breakdown.push({
            digit: digit,
            position: i + 1,
            meaning: meaning.meaning,
            energy: meaning.energy,
            description: meaning.description
        });
    }
    
    return breakdown;
}

// 分析能量平衡
function analyzeEnergyBalance(magneticAnalysis) {
    const balance = {
        positive: 0,
        negative: 0,
        neutral: 0,
        dominant: '',
        harmony: 0
    };
    
    magneticAnalysis.forEach(analysis => {
        if (analysis.field.type === 'positive') {
            balance.positive++;
        } else if (analysis.field.type === 'negative') {
            balance.negative++;
        } else {
            balance.neutral++;
        }
    });
    
    const total = balance.positive + balance.negative + balance.neutral;
    if (total > 0) {
        if (balance.positive >= balance.negative && balance.positive >= balance.neutral) {
            balance.dominant = 'positive';
        } else if (balance.negative > balance.positive && balance.negative >= balance.neutral) {
            balance.dominant = 'negative';
        } else {
            balance.dominant = 'neutral';
        }
        
        const ideal = total / 3;
        const variance = Math.abs(balance.positive - ideal) + Math.abs(balance.negative - ideal) + Math.abs(balance.neutral - ideal);
        balance.harmony = Math.max(0, 100 - (variance / total) * 100);
    }
    
    return balance;
}

// 生成改运建议
function generateSuggestions(analysis) {
    const suggestions = [];
    
    if (analysis.overallScore >= 80) {
        suggestions.push('您的手机号码能量很好，建议继续使用');
        suggestions.push('可以在重要场合优先使用此号码');
        suggestions.push('定期清理手机，保持正能量流通');
    } else if (analysis.overallScore >= 60) {
        suggestions.push('号码能量中等，可以通过风水布局来提升');
        suggestions.push('建议搭配吉祥饰品增强正能量');
        suggestions.push('在重要决策时可参考数字能量');
    } else {
        suggestions.push('建议考虑更换为能量更好的号码');
        suggestions.push('可以通过佩戴护身符来化解负面能量');
        suggestions.push('避免在重要时刻使用此号码');
        suggestions.push('建议咨询专业人士进行详细分析');
    }
    
    if (analysis.energyBalance.negative > analysis.energyBalance.positive) {
        suggestions.push('负面磁场较多，建议多行善事化解');
        suggestions.push('可以在手机上贴吉祥符来改善能量');
    }
    
    if (analysis.energyBalance.harmony < 50) {
        suggestions.push('能量不够平衡，建议寻求专业调理');
    }
    
    return suggestions;
}

// 显示分析结果
function displayAnalysisResult(analysis) {
    document.getElementById('phoneInput').style.display = 'none';
    
    const resultSection = document.getElementById('phoneResult');
    resultSection.style.display = 'block';
    
    updatePhoneInfo(analysis);
    updateAnalysisContent(analysis);
    
    setTimeout(() => {
        resultSection.classList.add('show');
        resultSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // 启动AI深度分析
        startAIAnalysis(analysis);
    }, 100);
    
    saveAnalysisToStorage(analysis);
}

// 启动AI深度分析
async function startAIAnalysis(basicAnalysis) {
    console.log('🚀 启动AI深度分析...');
    
    // 显示AI分析区域
    const aiSection = document.getElementById('aiAnalysisSection');
    if (aiSection) {
        aiSection.style.display = 'block';
        
        // 平滑滚动到AI分析区域
        setTimeout(() => {
            aiSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 500);
    }
    
    // 检查AI服务是否可用
    if (!phoneNumberAI) {
        showAIError('AI分析服务未初始化');
        return;
    }
    
    try {
        // 显示AI加载动画
        showAILoading();
        
        // 执行AI分析
        const aiResult = await phoneNumberAI.analyzePhoneNumber(basicAnalysis.phoneNumber, basicAnalysis);

        console.log('🎯 收到AI分析结果:', aiResult);
        console.log('📋 AI分析章节详情:', aiResult.aiAnalysis?.sections);

        // 显示AI分析结果
        displayAIResult(aiResult);
        
        // 更新当前分析结果
        currentAnalysis = { ...basicAnalysis, aiResult: aiResult };
        
    } catch (error) {
        console.error('AI分析失败:', error);
        showAIError(error.message);
    }
}

// 显示AI加载动画
function showAILoading() {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';
    
    // 模拟加载步骤
    const steps = ['aiStep1', 'aiStep2', 'aiStep3'];
    let currentStep = 0;
    
    const updateStep = () => {
        // 清除之前的状态
        steps.forEach(stepId => {
            const stepEl = document.getElementById(stepId);
            if (stepEl) {
                stepEl.classList.remove('active', 'completed');
            }
        });
        
        // 标记完成的步骤
        for (let i = 0; i < currentStep; i++) {
            const stepEl = document.getElementById(steps[i]);
            if (stepEl) {
                stepEl.classList.add('completed');
            }
        }
        
        // 标记当前步骤
        if (currentStep < steps.length) {
            const currentEl = document.getElementById(steps[currentStep]);
            if (currentEl) {
                currentEl.classList.add('active');
            }
        }
        
        currentStep++;
        
        if (currentStep <= steps.length) {
            setTimeout(updateStep, 1500);
        }
    };
    
    updateStep();
}

// 显示AI分析结果
function displayAIResult(aiResult) {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';
        contentDiv.innerHTML = generateAIContentHTML(aiResult.aiAnalysis);
    }
}

// 生成AI内容HTML
function generateAIContentHTML(aiAnalysis) {
    let html = '';
    
    // AI评分显示
    if (aiAnalysis.score) {
        html += `
            <div class="ai-section ai-summary">
                <div class="ai-score-display">
                    <div class="ai-score-circle">${aiAnalysis.score}分</div>
                    <div class="ai-score-info">
                        <div class="ai-score-label">智能评分</div>
                        <div class="ai-score-desc">${getAIScoreDescription(aiAnalysis.score)}</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // AI摘要
    if (aiAnalysis.summary) {
        html += `
            <div class="ai-section ai-summary">
                <h4>📋 智能摘要</h4>
                <p>${aiAnalysis.summary}</p>
            </div>
        `;
    }
    
    // 详细分析章节
    if (aiAnalysis.sections) {
        Object.keys(aiAnalysis.sections).forEach(key => {
            const content = aiAnalysis.sections[key];
            if (content && content.trim()) {
                const sectionTitles = {
                    personality: '👤 个性特征分析',
                    career: '💼 事业财运预测',
                    relationships: '👥 人际关系影响',
                    health: '🏥 健康运势评估',
                    love: '❤️ 感情婚姻指导',
                    suggestions: '💡 专业改运建议',
                    timing: '⏰ 最佳使用时机',
                    general: '🔮 综合分析'
                };
                
                html += `
                    <div class="ai-section">
                        <h4>${sectionTitles[key] || '📊 分析内容'}</h4>
                        <p>${content}</p>
                    </div>
                `;
            }
        });
    }
    
    // AI建议
    if (aiAnalysis.recommendations && aiAnalysis.recommendations.length > 0) {
        html += `
            <div class="ai-section ai-recommendations">
                <h4>🎯 专业建议</h4>
                <ul>
                    ${aiAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    html += `
        <div class="ai-section" style="background: rgba(255, 255, 255, 0.05); font-size: 14px; color: rgba(255, 255, 255, 0.8);">
            <p>本分析由易海堂提供，仅供参考。建议结合个人实际情况综合考虑。</p>
            <p style="margin: 5px 0 0 0;">分析时间：${new Date(aiAnalysis.timestamp).toLocaleString()}</p>
        </div>
    `;
    
    return html;
}

// 获取AI评分描述
function getAIScoreDescription(score) {
    if (score >= 90) return '评估：极佳运势';
    if (score >= 80) return '评估：优秀运势';
    if (score >= 70) return '评估：良好运势';
    if (score >= 60) return '评估：一般运势';
    return '评估：需要改善';
}

// 显示AI错误
function showAIError(errorMessage) {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';
        contentDiv.innerHTML = `
            <div class="ai-section" style="background: rgba(220, 53, 69, 0.2); border-left-color: #dc3545;">
                <h4>⚠️ 分析暂时不可用</h4>
                <p>原因：${errorMessage}</p>
                <p>已为您提供基础的数字能量分析，如需深度分析，请稍后重试或联系客服。</p>
            </div>
        `;
    }
}

// 更新手机号信息
function updatePhoneInfo(analysis) {
    const container = document.getElementById('resultPhoneInfo');
    if (!container) return;
    
    const maskedNumber = maskPhoneNumber(analysis.phoneNumber);
    
    container.innerHTML = `
        <div class="phone-display">${maskedNumber}</div>
        <div class="carrier-info">
            <h3>${analysis.carrier}</h3>
            <p>数字能量分析</p>
        </div>
    `;
}

// 掩码手机号
function maskPhoneNumber(phoneNumber) {
    return phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7);
}

// 更新分析内容
function updateAnalysisContent(analysis) {
    const container = document.getElementById('analysisContent');
    if (!container) return;
    
    const scoreClass = getScoreClass(analysis.overallScore);
    const scoreDesc = getScoreDescription(analysis.overallScore);
    
    container.innerHTML = `
        <div class="analysis-section">
            <h3><span class="section-icon">🔮</span>磁场分析</h3>
            ${generateMagneticFieldHTML(analysis.magneticAnalysis)}
            <div class="suggestions">
                <h4>🎯 磁场建议</h4>
                <p>检测到 ${analysis.magneticAnalysis.length} 个有效磁场组合，${getMagneticFieldAdvice(analysis)}</p>
            </div>
        </div>
        
        <div class="analysis-section">
            <h3><span class="section-icon">🔢</span>数字解析</h3>
            <div class="number-breakdown">
                ${generateNumberBreakdownHTML(analysis.numberBreakdown)}
            </div>
            <p>每个数字都有其独特的能量属性，组合在一起形成您手机号的整体能量场。</p>
        </div>
        
        <div class="analysis-section">
            <h3><span class="section-icon">⚖️</span>能量平衡</h3>
            ${generateEnergyBalanceHTML(analysis.energyBalance)}
            <p>能量平衡度：${Math.round(analysis.energyBalance.harmony)}%，${getBalanceAdvice(analysis.energyBalance)}</p>
        </div>
        
        <div class="analysis-section">
            <h3><span class="section-icon">💡</span>改运建议</h3>
            <div class="suggestions">
                <ul class="suggestion-list">
                    ${analysis.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                </ul>
            </div>
        </div>
    `;
}

// 获取评分等级类名
function getScoreClass(score) {
    if (score >= 85) return 'score-excellent';
    if (score >= 70) return 'score-good';
    if (score >= 55) return 'score-average';
    return 'score-poor';
}

// 获取评分描述
function getScoreDescription(score) {
    if (score >= 85) return '优秀';
    if (score >= 70) return '良好';
    if (score >= 55) return '一般';
    return '需改善';
}

// 获取详细评分描述
function getDetailedScoreDesc(score) {
    if (score >= 85) return '能量非常旺盛，是一个非常吉祥的号码';
    if (score >= 70) return '能量较好，总体比较吉利';
    if (score >= 55) return '能量一般，有改善空间';
    return '能量不佳，建议考虑调整';
}

// 生成磁场分析HTML
function generateMagneticFieldHTML(magneticAnalysis) {
    if (magneticAnalysis.length === 0) {
        return '<p>未检测到明显的磁场组合，号码能量相对平淡。</p>';
    }
    
    return magneticAnalysis.map(analysis => {
        const field = analysis.field;
        const typeClass = field.type === 'positive' ? 'positive' : field.type === 'negative' ? 'negative' : 'neutral';
        
        return `
            <div class="magnetic-field-item ${typeClass}" style="margin: 10px 0; padding: 10px; border-radius: 8px; background: ${field.type === 'positive' ? '#d4edda' : field.type === 'negative' ? '#f8d7da' : '#fff3cd'};">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                    <span style="font-weight: bold; color: #333;">${analysis.combination} - ${field.name}</span>
                    <span style="font-size: 12px; color: #666;">${analysis.position}</span>
                </div>
                <p style="font-size: 13px; color: #555; margin: 0;">${field.description}</p>
            </div>
        `;
    }).join('');
}

// 生成数字分解HTML
function generateNumberBreakdownHTML(numberBreakdown) {
    return numberBreakdown.map(item => {
        const energyClass = item.energy === 'positive' ? 'positive' : item.energy === 'negative' ? 'negative' : 'neutral';
        return `
            <div class="number-item ${energyClass}">
                <div class="number-value">${item.digit}</div>
                <div class="number-energy">${item.meaning}</div>
            </div>
        `;
    }).join('');
}

// 生成能量平衡HTML
function generateEnergyBalanceHTML(energyBalance) {
    const total = energyBalance.positive + energyBalance.negative + energyBalance.neutral;
    if (total === 0) {
        return '<p>未检测到明显的能量磁场。</p>';
    }
    
    const positivePercent = Math.round((energyBalance.positive / total) * 100);
    const negativePercent = Math.round((energyBalance.negative / total) * 100);
    const neutralPercent = Math.round((energyBalance.neutral / total) * 100);
    
    return `
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin: 15px 0;">
            <div style="text-align: center; padding: 10px; background: #d4edda; border-radius: 8px;">
                <div style="font-size: 16px; font-weight: bold; color: #28a745;">${positivePercent}%</div>
                <div style="font-size: 12px; color: #666;">正面能量</div>
            </div>
            <div style="text-align: center; padding: 10px; background: #f8d7da; border-radius: 8px;">
                <div style="font-size: 16px; font-weight: bold; color: #dc3545;">${negativePercent}%</div>
                <div style="font-size: 12px; color: #666;">负面能量</div>
            </div>
            <div style="text-align: center; padding: 10px; background: #fff3cd; border-radius: 8px;">
                <div style="font-size: 16px; font-weight: bold; color: #ffc107;">${neutralPercent}%</div>
                <div style="font-size: 12px; color: #666;">中性能量</div>
            </div>
        </div>
    `;
}

// 获取磁场建议
function getMagneticFieldAdvice(analysis) {
    const positiveFields = analysis.magneticAnalysis.filter(a => a.field.type === 'positive').length;
    const negativeFields = analysis.magneticAnalysis.filter(a => a.field.type === 'negative').length;
    
    if (positiveFields > negativeFields) {
        return '正面磁场占优势，整体能量较好';
    } else if (negativeFields > positiveFields) {
        return '负面磁场较多，建议考虑改善';
    } else {
        return '正负磁场相当，能量较为平衡';
    }
}

// 获取平衡建议
function getBalanceAdvice(energyBalance) {
    if (energyBalance.harmony >= 80) return '能量分布很均衡';
    if (energyBalance.harmony >= 60) return '能量分布较为均衡';
    if (energyBalance.harmony >= 40) return '能量分布不够均衡';
    return '能量分布失衡，建议调整';
}

// 显示加载动画
function showLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'flex';
    }
}

// 隐藏加载动画
function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// 保存分析结果到本地存储
function saveAnalysisToStorage(analysis) {
    const savedResults = JSON.parse(localStorage.getItem('phoneAnalysisResults') || '[]');
    const newResult = {
        ...analysis,
        timestamp: new Date().toISOString(),
        id: Date.now()
    };
    
    savedResults.unshift(newResult);
    if (savedResults.length > 10) {
        savedResults.splice(10);
    }
    
    localStorage.setItem('phoneAnalysisResults', JSON.stringify(savedResults));
}

// 保存分析结果
function saveAnalysis() {
    if (!currentAnalysis) {
        alert('没有可保存的分析结果');
        return;
    }
    
    const blob = new Blob([JSON.stringify(currentAnalysis, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `手机号分析_${currentAnalysis.phoneNumber}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    alert('分析结果已保存到文件');
}

// 分享分析结果
function shareAnalysis() {
    if (!currentAnalysis) {
        alert('没有可分享的分析结果');
        return;
    }
    
    const maskedNumber = maskPhoneNumber(currentAnalysis.phoneNumber);
    const scoreDesc = getScoreDescription(currentAnalysis.overallScore);
    const shareText = `我在易海堂测了手机号码 ${maskedNumber} 的数字能量，综合评分 ${currentAnalysis.overallScore} 分（${scoreDesc}）！检测到 ${currentAnalysis.magneticAnalysis.length} 个磁场组合，能量平衡度 ${Math.round(currentAnalysis.energyBalance.harmony)}%！`;
    
    if (navigator.share) {
        navigator.share({
            title: '手机号测吉凶结果',
            text: shareText,
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(shareText).then(() => {
            alert('分享内容已复制到剪贴板');
        }).catch(() => {
            alert('分享失败，请手动复制分享内容');
        });
    }
}

// 重新分析
function resetAnalysis() {
    currentAnalysis = null;
    
    const form = document.getElementById('phoneForm');
    if (form) {
        form.reset();
    }
    
    document.getElementById('phoneResult').style.display = 'none';
    document.getElementById('phoneInput').style.display = 'block';
    
    // 隐藏AI分析区域
    const aiSection = document.getElementById('aiAnalysisSection');
    if (aiSection) {
        aiSection.style.display = 'none';
    }
    
    window.scrollTo({ top: 0, behavior: 'smooth' });
} 