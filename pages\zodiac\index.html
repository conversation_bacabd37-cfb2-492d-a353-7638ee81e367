<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生肖运势 - 易海堂算命网</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="../../css/main.css">
    <!-- 认证服务 -->
     <script src="../../js/modules/ai-config-manager.js"></script>
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script src="../../js/modules/auth-service.js"></script>
    <!-- 认证初始化 -->
    <script src="../../js/modules/auth-init.js"></script>
    <!-- 订单和支付服务 -->
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <!-- 业务模块 -->
    <script src="../../js/modules/zodiac-ai.js"></script>
    
    <style>
        .year-hint {
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .hint-icon {
            font-size: 14px;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }
        
        .hint-text {
            line-height: 1.3;
        }
        
        /* 当选择了生肖后的状态会通过JavaScript动态设置 */

        /* Banner轮播图样式 */
        .banner-carousel {
            margin: 40px 0;
            padding: 0 20px;
        }

        .carousel-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .carousel-wrapper {
            position: relative;
            width: 100%;
            height: 300px;
            overflow: hidden;
        }

        .carousel-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        }

        .carousel-slide.active {
            opacity: 1;
        }

        .carousel-slide img {
            width: 50%;
            height: 100%;
            object-fit: cover;
            border-radius: 0;
        }

        .slide-content {
            flex: 1;
            padding: 40px;
            color: #8b4513;
            text-align: center;
        }

        .slide-content h2 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .slide-content p {
            font-size: 1.2rem;
            margin-bottom: 25px;
            opacity: 0.9;
        }

        .slide-btn {
            display: inline-block;
            padding: 12px 30px;
            background: rgba(139, 69, 19, 0.8);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .slide-btn:hover {
            background: rgba(139, 69, 19, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
        }

        /* 轮播指示器 */
        .carousel-indicators {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: #ff69b4;
            transform: scale(1.2);
        }

        /* 轮播控制按钮 */
        .carousel-control {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.8);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 24px;
            color: #8b4513;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-control:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-50%) scale(1.1);
        }

        .carousel-control.prev {
            left: 20px;
        }

        .carousel-control.next {
            right: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .carousel-wrapper {
                height: 250px;
            }

            .carousel-slide {
                flex-direction: column;
                text-align: center;
            }

            .carousel-slide img {
                width: 100%;
                height: 60%;
            }

            .slide-content {
                padding: 20px;
                height: 40%;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .slide-content h2 {
                font-size: 1.8rem;
                margin-bottom: 10px;
            }

            .slide-content p {
                font-size: 1rem;
                margin-bottom: 15px;
            }

            .carousel-control {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }
        }

        /* JSON测试按钮样式 */
        .action-btn.info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            border: 2px solid #17a2b8;
        }

        .action-btn.info:hover {
            background: linear-gradient(135deg, #138496, #117a8b);
            border-color: #138496;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
        }
        
        /* AI分析区域样式 */
        .ai-analysis-section {
            margin: 25px 0;
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .ai-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .ai-section-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
            color: white;
        }
        
        .ai-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .ai-icon {
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        /* AI加载动画 */
        .ai-loading {
            text-align: center;
            padding: 40px 20px;
        }
        
        .ai-loading-spinner {
            font-size: 48px;
            animation: zodiacSpin 3s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes zodiacSpin {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }
        
        .ai-loading-text {
            font-size: 18px;
            margin-bottom: 25px;
            font-weight: 500;
        }
        
        .ai-loading-steps {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .ai-loading-steps .step {
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0.5;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-size: 14px;
        }
        
        .ai-loading-steps .step.active {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .ai-loading-steps .step.completed {
            opacity: 1;
            background: rgba(40, 167, 69, 0.8);
            color: white;
        }
        
        /* AI内容样式 */
        .ai-content {
            line-height: 1.8;
        }
        
        .ai-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border-left: 4px solid rgba(255, 255, 255, 0.5);
        }
        
        .ai-section h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: bold;
            color: #FFD700;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .ai-section p {
            margin: 0;
            line-height: 1.7;
            font-size: 14px;
        }
        
        .ai-summary {
            background: rgba(255, 215, 0, 0.2);
            border-left-color: #FFD700;
            font-size: 16px;
            font-weight: 500;
        }
        
        .ai-recommendations {
            background: rgba(40, 167, 69, 0.2);
            border-left-color: #28a745;
        }
        
        .ai-recommendations ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .ai-recommendations li {
            margin: 8px 0;
            font-size: 14px;
        }
        
        .ai-score-display {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 15px 0;
        }
        
        .ai-score-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        .ai-score-info {
            flex: 1;
        }
        
        .ai-score-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 5px;
        }
        
        .ai-score-desc {
            font-size: 16px;
            font-weight: 500;
        }
        
        .lucky-elements {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .lucky-element {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* AI分析结果样式 */
        .ai-section {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .ai-section h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: bold;
            color: white;
        }

        .ai-score-display {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .ai-score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .ai-score-info {
            text-align: left;
        }

        .ai-score-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }

        .ai-score-desc {
            font-size: 16px;
            font-weight: bold;
            color: #FFD700;
        }

        /* 基本信息网格 */
        .basic-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 3px solid #4A90E2;
        }

        .info-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #4A90E2;
            font-weight: bold;
        }

        /* 评分网格 */
        .scores-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .score-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .score-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .score-bar {
            position: relative;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            overflow: hidden;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            transition: width 1s ease;
        }

        .score-text {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: white;
            font-weight: bold;
        }

        /* 章节内容 */
        .section-content {
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        /* 建议列表 */
        .recommendations-list {
            margin: 0;
            padding-left: 20px;
        }

        .recommendations-list li {
            margin-bottom: 8px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }

        /* 开运要素网格 */
        .lucky-elements-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .lucky-element-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .lucky-label {
            font-size: 13px;
            color: #FFD700;
            font-weight: 500;
        }

        .lucky-values {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 400;
        }

        /* AI错误样式 */
        .ai-error {
            text-align: center;
            padding: 40px 20px;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .error-message {
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        }

        .error-detail {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 20px;
        }

        .retry-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .ai-analysis-section {
                margin: 15px 0;
                padding: 20px;
            }
            
            .ai-section-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .ai-badge {
                align-self: flex-end;
            }
            
            .ai-loading-steps {
                grid-template-columns: 1fr;
                max-width: 100%;
            }
            
            .ai-section {
                padding: 15px;
            }
            
            .ai-score-display {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .basic-info-grid {
                grid-template-columns: 1fr;
            }

            .lucky-elements-grid {
                grid-template-columns: 1fr;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .info-value {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <!-- 动画背景 -->
    <canvas id="zodiacCanvas" class="zodiac-canvas"></canvas>
    
    <!-- 返回按钮 -->
    <div class="back-button" onclick="window.history.back()">
        <span class="back-icon">←</span>
        <span class="back-text">返回</span>
    </div>

    <!-- 页面头部 -->
    <header class="zodiac-header">
        <div class="header-content">
            <h1 class="header-title">🐍 2025蛇年生肖运势</h1>
            <p class="header-subtitle">十二生肖运程详批，把握流年机遇</p>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-container">
        <!-- 生肖选择区域 -->
        <section class="zodiac-selection" id="zodiacSelection">
            <div class="section-header">
                <h2>🎯 选择您的生肖</h2>
                <p>请选择您的生肖属相</p>
            </div>
            
            <div class="zodiac-grid">
                <div class="zodiac-card" data-zodiac="rat">
                    <div class="zodiac-icon">🐭</div>
                    <div class="zodiac-name">鼠</div>
                    <div class="zodiac-years">近年：1984, 1996, 2008, 2020</div>
                </div>
                <div class="zodiac-card" data-zodiac="ox">
                    <div class="zodiac-icon">🐮</div>
                    <div class="zodiac-name">牛</div>
                    <div class="zodiac-years">近年：1985, 1997, 2009, 2021</div>
                </div>
                <div class="zodiac-card" data-zodiac="tiger">
                    <div class="zodiac-icon">🐯</div>
                    <div class="zodiac-name">虎</div>
                    <div class="zodiac-years">近年：1986, 1998, 2010, 2022</div>
                </div>
                <div class="zodiac-card" data-zodiac="rabbit">
                    <div class="zodiac-icon">🐰</div>
                    <div class="zodiac-name">兔</div>
                    <div class="zodiac-years">近年：1987, 1999, 2011, 2023</div>
                </div>
                <div class="zodiac-card" data-zodiac="dragon">
                    <div class="zodiac-icon">🐲</div>
                    <div class="zodiac-name">龙</div>
                    <div class="zodiac-years">近年：1988, 2000, 2012, 2024</div>
                </div>
                <div class="zodiac-card" data-zodiac="snake">
                    <div class="zodiac-icon">🐍</div>
                    <div class="zodiac-name">蛇</div>
                    <div class="zodiac-years">近年：1989, 2001, 2013, 2025</div>
                </div>
                <div class="zodiac-card" data-zodiac="horse">
                    <div class="zodiac-icon">🐴</div>
                    <div class="zodiac-name">马</div>
                    <div class="zodiac-years">近年：1990, 2002, 2014, 2026</div>
                </div>
                <div class="zodiac-card" data-zodiac="goat">
                    <div class="zodiac-icon">🐑</div>
                    <div class="zodiac-name">羊</div>
                    <div class="zodiac-years">近年：1991, 2003, 2015, 2027</div>
                </div>
                <div class="zodiac-card" data-zodiac="monkey">
                    <div class="zodiac-icon">🐵</div>
                    <div class="zodiac-name">猴</div>
                    <div class="zodiac-years">近年：1992, 2004, 2016, 2028</div>
                </div>
                <div class="zodiac-card" data-zodiac="rooster">
                    <div class="zodiac-icon">🐔</div>
                    <div class="zodiac-name">鸡</div>
                    <div class="zodiac-years">近年：1993, 2005, 2017, 2029</div>
                </div>
                <div class="zodiac-card" data-zodiac="dog">
                    <div class="zodiac-icon">🐶</div>
                    <div class="zodiac-name">狗</div>
                    <div class="zodiac-years">近年：1994, 2006, 2018, 2030</div>
                </div>
                <div class="zodiac-card" data-zodiac="pig">
                    <div class="zodiac-icon">🐷</div>
                    <div class="zodiac-name">猪</div>
                    <div class="zodiac-years">近年：1995, 2007, 2019, 2031</div>
                </div>
            </div>
        </section>

        <!-- 详细信息输入区域 -->
        <section class="info-input" id="infoInput" style="display: none;">
            <div class="section-header">
                <h2>📝 完善信息</h2>
                <p>请输入您的详细信息，获取更精准的运势</p>
            </div>
            
            <form class="zodiac-form" id="zodiacForm">
                <div class="selected-zodiac" id="selectedZodiac">
                    <!-- 选中的生肖信息会显示在这里 -->
                </div>
                
                <div class="form-group">
                    <label for="birthYear">
                        <span class="label-icon">📅</span>
                        <span class="label-text">出生年份</span>
                    </label>
                    <select id="birthYear" name="birthYear" required>
                        <option value="">请选择出生年份</option>
                        <!-- 年份选项会通过JavaScript动态生成 -->
                    </select>
                    <div class="year-hint" id="yearHint">
                        <span class="hint-icon">💡</span>
                        <span class="hint-text" id="hintText">选择生肖后将只显示对应年份</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="birthMonth">
                        <span class="label-icon">🗓️</span>
                        <span class="label-text">出生月份</span>
                    </label>
                    <select id="birthMonth" name="birthMonth" required>
                        <option value="">请选择出生月份</option>
                        <option value="1">一月</option>
                        <option value="2">二月</option>
                        <option value="3">三月</option>
                        <option value="4">四月</option>
                        <option value="5">五月</option>
                        <option value="6">六月</option>
                        <option value="7">七月</option>
                        <option value="8">八月</option>
                        <option value="9">九月</option>
                        <option value="10">十月</option>
                        <option value="11">十一月</option>
                        <option value="12">十二月</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="gender">
                        <span class="label-icon">👤</span>
                        <span class="label-text">性别</span>
                    </label>
                    <div class="gender-options">
                        <label class="gender-option">
                            <input type="radio" name="gender" value="male">
                            <span class="gender-btn male">
                                <span class="gender-icon">👨</span>
                                <span>男</span>
                            </span>
                        </label>
                        <label class="gender-option">
                            <input type="radio" name="gender" value="female">
                            <span class="gender-btn female">
                                <span class="gender-icon">👩</span>
                                <span>女</span>
                            </span>
                        </label>
                    </div>
                </div>

                <button type="submit" class="submit-btn">
                    <span class="btn-icon">🔮</span>
                    <span class="btn-text">查看2025年运势</span>
                </button>
            </form>
        </section>

        <!-- 运势结果展示区域 -->
        <section class="fortune-result" id="fortuneResult" style="display: none;">
            <div class="result-header">
                <h2>🎊 您的2025年运势详批</h2>
                <div class="result-zodiac-info" id="resultZodiacInfo">
                    <!-- 生肖信息会显示在这里 -->
                </div>
            </div>
            <!-- AI深度分析区域 -->
            <div class="ai-analysis-section" id="aiAnalysisSection" style="display: none;">
                <div class="ai-section-header">
                    <h3>深度运势分析</h3>
                    <div class="ai-badge">
                        <span class="ai-icon">✨</span>
                        <span id="aiServiceType">易海堂</span>
                    </div>
                </div>
                
                <div class="ai-loading" id="aiLoading" style="display: none;">
                    <div class="ai-loading-spinner">🔮</div>
                    <div class="ai-loading-text">正在深度分析您的运势...</div>
                    <div class="ai-loading-steps">
                        <div class="step" id="aiStep1">🐲 分析生肖特质</div>
                        <div class="step" id="aiStep2">📅 结合流年趋势</div>
                        <div class="step" id="aiStep3">🎯 预测运势走向</div>
                        <div class="step" id="aiStep4">💡 生成专业建议</div>
                    </div>
                </div>
                
                <div class="ai-content" id="aiContent" style="display: none;">
                    <!-- AI分析结果会显示在这里 -->
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn primary" onclick="saveResult()">
                    <span class="btn-icon">💾</span>
                    <span>保存结果</span>
                </button>
                <button class="action-btn secondary" onclick="shareResult()">
                    <span class="btn-icon">📤</span>
                    <span>分享运势</span>
                </button>
                <button class="action-btn info" onclick="testJSONOutput()">
                    <span class="btn-icon">🔍</span>
                    <span>查看JSON</span>
                </button>
                <button class="action-btn tertiary" onclick="resetForm()">
                    <span class="btn-icon">🔄</span>
                    <span>重新测算</span>
                </button>
            </div>
        </section>

        <!-- Banner轮播图 -->
        <section class="banner-carousel">
            <div class="carousel-container">
                <div class="carousel-wrapper" id="carouselWrapper">
                    <div class="carousel-slide active">
                        <img src="../../images/banner/banner1.png" alt="姓名详批 - 超吉避凶 好运一整年">
                        <div class="slide-content">
                            <h2>姓名详批</h2>
                            <p>超吉避凶 好运一整年</p>
                            <a href="#" class="slide-btn">查看更多 →</a>
                        </div>
                    </div>
                    <div class="carousel-slide">
                        <img src="../../images/banner/banner2.png" alt="生肖运势 - 2025年运程详解">
                        <div class="slide-content">
                            <h2>生肖运势</h2>
                            <p>2025年运程详解</p>
                            <a href="#" class="slide-btn">立即测算 →</a>
                        </div>
                    </div>
                </div>

                <!-- 轮播指示器 -->
                <div class="carousel-indicators">
                    <span class="indicator active" data-slide="0"></span>
                    <span class="indicator" data-slide="1"></span>
                </div>

                <!-- 轮播控制按钮 -->
                <button class="carousel-control prev" id="prevBtn">‹</button>
                <button class="carousel-control next" id="nextBtn">›</button>
            </div>
        </section>
    </main>

    <!-- 加载动画 -->
    <div class="loading-overlay-zodiac" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在分析您的运势...</div>
        </div>
    </div>

    <!-- 引入AI模块 -->
    <script src="../../js/modules/ai-service.js?v=1.2"></script>
    <script src="../../js/modules/zodiac-ai.js?v=1.2"></script>
    
    <script src="script.js?v=1.3"></script>
</body>
</html> 