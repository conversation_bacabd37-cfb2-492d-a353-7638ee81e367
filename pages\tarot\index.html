<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>💕 智能姻缘画像 - 易海堂算命网</title>
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="../../css/portrait-form.css">
    <!-- 认证服务 -->
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script src="../../js/modules/auth-service.js"></script>
    <!-- 订单和支付服务 -->
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <!-- AI服务模块 -->
    <script src="../../js/modules/ai-config-manager.js"></script>
    <script src="../../js/modules/ai-service.js"></script>
    <script src="../../js/modules/zhipu-ai-service.js"></script>
    <!-- 业务模块 -->
    <script src="../../js/modules/portrait-ai.js"></script>
    <script src="../../js/modules/portrait-form-config.js"></script>
    <style>
        /* 全局样式 */
        body {
            background: linear-gradient(135deg, #FFB6C1, #FF69B4, #DC143C);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            overflow-x: hidden;
        }

        /* 主容器 */
        .wizard-container {
            position: relative;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 背景装饰 */
        .bg-decoration {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 182, 193, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 105, 180, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(220, 20, 60, 0.2) 0%, transparent 50%);
        }

        /* 头部 */
        .wizard-header {
            background: linear-gradient(135deg, #DC143C, #FF1493);
            color: white;
            text-align: center;
            padding: 20px;
            position: relative;
            z-index: 2;
            box-shadow: 0 4px 20px rgba(220, 20, 60, 0.3);
        }

        .wizard-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid white;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.05);
        }

        /* 进度条 */
        .progress-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            position: relative;
            z-index: 2;
        }

        .progress-bar {
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #FF1493, #FFB6C1);
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            color: white;
            font-size: 14px;
            margin-top: 8px;
            font-weight: 600;
        }

        /* 内容区域 */
        .wizard-content {
            flex: 1;
            padding: 20px;
            position: relative;
            z-index: 2;
        }

        .step-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .step-title {
            font-size: 24px;
            font-weight: 700;
            color: #DC143C;
            margin-bottom: 10px;
            text-align: center;
        }

        .step-subtitle {
            font-size: 16px;
            color: #666;
            text-align: center;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        /* 选项卡片 */
        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .option-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(220, 20, 60, 0.2);
            border-color: #FF1493;
        }

        .option-card.selected {
            background: linear-gradient(135deg, #FF1493, #FFB6C1);
            border-color: #FF1493;
            color: white;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(220, 20, 60, 0.3);
        }

        .option-card.selected::before {
            content: '✓';
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            color: #FF1493;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .option-icon {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
        }

        .option-text {
            font-size: 13px;
            font-weight: 600;
            line-height: 1.2;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 16px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #FF1493;
            box-shadow: 0 0 15px rgba(255, 20, 147, 0.2);
        }

        /* 按钮样式 */
        .btn-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            position: relative;
            z-index: 10;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #DC143C, #FF1493);
            color: white;
            box-shadow: 0 5px 20px rgba(220, 20, 60, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 调试样式 */
        .btn {
            position: relative;
            z-index: 100;
        }

        /* 结果展示 */
        .result-container {
            text-align: center;
            padding: 20px;
        }

        .result-title {
            color: #DC143C;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .carousel-container {
            position: relative;
            margin: 20px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            background: #f8f9fa;
            min-height: 420px;
        }

        .carousel-wrapper {
            display: flex;
            transition: transform 0.5s ease;
        }

        .carousel-slide {
            min-width: 100%;
            position: relative;
        }

        .carousel-image {
            width: 100%;
            height: 400px;
            object-fit: contain;
            border-radius: 15px;
            background: #f8f9fa;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .carousel-image.loaded {
            opacity: 1;
        }

        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.8);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            color: #DC143C;
            transition: all 0.3s ease;
        }

        .carousel-nav:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
        }

        .carousel-nav.prev {
            left: 10px;
        }

        .carousel-nav.next {
            right: 10px;
        }

        .carousel-dots {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 15px;
        }

        .carousel-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-dot.active {
            background: #DC143C;
            transform: scale(1.2);
        }

        /* 加载动画 */
        .loading-container {
            text-align: center;
            padding: 40px 20px;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #DC143C;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #666;
            font-size: 16px;
            margin: 0;
        }

        /* 详细参数表单样式 */
        .detailed-form-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .detailed-form-title {
            font-size: 20px;
            font-weight: 700;
            color: #DC143C;
            margin-bottom: 20px;
            text-align: center;
        }

        .detailed-form-subtitle {
            font-size: 14px;
            color: #666;
            text-align: center;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        /* 范围表单样式 */
        .range-form-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .range-input-group {
            text-align: center;
        }

        .range-label {
            display: block;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .range-control {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 15px;
        }

        .range-input {
            width: 200px;
            height: 8px;
            border-radius: 4px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
        }

        .range-input::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #DC143C, #FF1493);
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(220, 20, 60, 0.3);
        }

        .range-input::-moz-range-thumb {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #DC143C, #FF1493);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 8px rgba(220, 20, 60, 0.3);
        }

        .range-value {
            font-size: 24px;
            font-weight: 700;
            color: #DC143C;
            min-width: 60px;
            text-align: center;
        }

        .range-labels {
            display: flex;
            justify-content: space-between;
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .wizard-content {
                padding: 15px;
            }
            
            .step-container {
                padding: 20px;
            }
            
            .options-container {
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
                gap: 10px;
            }
            
            .option-card {
                padding: 15px 10px;
                min-height: 80px;
            }
            
            .option-icon {
                font-size: 24px;
            }
            
            .option-text {
                font-size: 12px;
            }
            
            .btn-container {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }

        @media (max-width: 480px) {
            .wizard-header h1 {
                font-size: 20px;
            }
            
            .step-title {
                font-size: 20px;
            }
            
            .step-subtitle {
                font-size: 14px;
            }
            
            .options-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <div class="bg-decoration"></div>
        
        <!-- 头部 -->
        <div class="wizard-header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>💕 智能姻缘画像</h1>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">步骤 1 / 5</div>
        </div>
        
        <!-- 内容区域 -->
        <div class="wizard-content">
            <div class="step-container">
                <div id="stepContent">
                    <!-- 步骤内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // AI配置
        window.AI_CONFIG = {
            SERVICE_TYPE: 'zhipu', // 'zhipu', 'deepseek', 'dify'

            // 智谱AI配置
            ZHIPU_API_KEY: '38fac9046717433aa99f1b73c97084b1.7ys0b9U1BFfSdGGO', // 请在这里填入您的智谱AI API密钥
            ZHIPU_BASE_URL: 'https://open.bigmodel.cn/api/paas/v4',
            ZHIPU_TEXT_MODEL: 'glm-4-flash',
            ZHIPU_IMAGE_MODEL: 'cogview-3-plus',

            // DeepSeek配置（备用）
            DEEPSEEK_API_KEY: '***********************************',
            DEEPSEEK_BASE_URL: 'https://api.deepseek.com/v1',

            // 调试模式
            DEBUG: true
        };

        // 向导状态管理
        const wizardState = {
            currentStep: 1,
            totalSteps: 18,
            userData: {
                gender: '',
                age: '',
                zodiac: '',
                preferredGender: '',
                preferredAge: '',
                priorities: [],
                // 新增详细参数
                appearance: {},
                personality: {},
                lifestyle: {},
                career: {},
                values: {},
                relationship: {}
            }
        };

        // 步骤配置
        const steps = [
            {
                id: 1,
                title: '您的性别',
                subtitle: '请选择您的性别，系统将自动为您匹配异性对象',
                type: 'choice',
                options: [
                    { value: 'male', text: '男性', icon: '👨' },
                    { value: 'female', text: '女性', icon: '👩' }
                ],
                field: 'gender'
            },
            {
                id: 2,
                title: '您的年龄段',
                subtitle: '选择您的年龄段，系统将为您匹配合适的对象',
                type: 'choice',
                options: [
                    { value: 'young', text: '20-25岁', icon: '🌸' },
                    { value: 'mature', text: '26-35岁', icon: '🌺' },
                    { value: 'middle', text: '36-45岁', icon: '🌹' },
                    { value: 'senior', text: '45岁以上', icon: '🌷' }
                ],
                field: 'age'
            },
            {
                id: 3,
                title: '您的星座',
                subtitle: '选择您的星座，了解星座匹配度',
                type: 'choice',
                options: [
                    { value: 'aries', text: '白羊座', icon: '♈' },
                    { value: 'taurus', text: '金牛座', icon: '♉' },
                    { value: 'gemini', text: '双子座', icon: '♊' },
                    { value: 'cancer', text: '巨蟹座', icon: '♋' },
                    { value: 'leo', text: '狮子座', icon: '♌' },
                    { value: 'virgo', text: '处女座', icon: '♍' },
                    { value: 'libra', text: '天秤座', icon: '♎' },
                    { value: 'scorpio', text: '天蝎座', icon: '♏' },
                    { value: 'sagittarius', text: '射手座', icon: '♐' },
                    { value: 'capricorn', text: '摩羯座', icon: '♑' },
                    { value: 'aquarius', text: '水瓶座', icon: '♒' },
                    { value: 'pisces', text: '双鱼座', icon: '♓' }
                ],
                field: 'zodiac'
            },
            {
                id: 4,
                title: '最看重的特质',
                subtitle: '选择您最看重的特质（建议选择2-5个，可多选）',
                type: 'multiChoice',
                options: [
                    { value: 'appearance', text: '外貌出众', icon: '✨' },
                    { value: 'personality', text: '性格温和', icon: '😊' },
                    { value: 'intelligence', text: '聪明睿智', icon: '🧠' },
                    { value: 'humor', text: '幽默风趣', icon: '😄' },
                    { value: 'career', text: '事业有成', icon: '💼' },
                    { value: 'family', text: '顾家温馨', icon: '🏠' },
                    { value: 'adventure', text: '喜欢冒险', icon: '🗺️' },
                    { value: 'stability', text: '稳重可靠', icon: '🛡️' },
                    { value: 'reading', text: '热爱阅读', icon: '📚' },
                    { value: 'music', text: '音乐天赋', icon: '🎵' },
                    { value: 'sports', text: '运动健身', icon: '🏃‍♀️' },
                    { value: 'travel', text: '旅行探索', icon: '✈️' },
                    { value: 'cooking', text: '烹饪美食', icon: '👨‍🍳' },
                    { value: 'art', text: '艺术创作', icon: '🎨' },
                    { value: 'photography', text: '摄影爱好', icon: '📷' },
                    { value: 'gaming', text: '游戏娱乐', icon: '🎮' },
                    { value: 'nature', text: '亲近自然', icon: '🌿' },
                    { value: 'technology', text: '科技达人', icon: '💻' },
                    { value: 'fashion', text: '时尚品味', icon: '👗' },
                    { value: 'pets', text: '爱心宠物', icon: '🐕' },
                    { value: 'volunteer', text: '热心公益', icon: '🤝' },
                    { value: 'meditation', text: '内心平静', icon: '🧘‍♀️' },
                    { value: 'dancing', text: '舞蹈才艺', icon: '💃' },
                    { value: 'writing', text: '写作才华', icon: '✍️' }
                ],
                field: 'priorities',
                maxSelections: 5
            },
            // 外貌特征细分
            {
                id: 5,
                title: '理想年龄范围',
                subtitle: '选择您理想伴侣的年龄范围',
                type: 'rangeForm',
                field: 'appearance',
                subField: 'age',
                config: { min: 18, max: 50, default: 25, label: '理想年龄' }
            },
            {
                id: 6,
                title: '理想身高范围',
                subtitle: '选择您理想伴侣的身高范围',
                type: 'rangeForm',
                field: 'appearance',
                subField: 'height',
                config: { min: 150, max: 190, default: 165, label: '理想身高' }
            },
            {
                id: 7,
                title: '体型偏好',
                subtitle: '选择您偏好的体型类型',
                type: 'choice',
                options: [
                    { value: '苗条', text: '苗条', icon: '💃' },
                    { value: '运动型', text: '运动型', icon: '🏃‍♀️' },
                    { value: '标准', text: '标准', icon: '👤' },
                    { value: '丰满', text: '丰满', icon: '🌸' }
                ],
                field: 'appearance',
                subField: 'bodyType'
            },
            {
                id: 8,
                title: '风格偏好',
                subtitle: '选择您偏好的风格类型',
                type: 'choice',
                options: [
                    { value: '优雅', text: '优雅', icon: '👑' },
                    { value: '休闲', text: '休闲', icon: '😌' },
                    { value: '运动', text: '运动', icon: '⚽' },
                    { value: '艺术', text: '艺术', icon: '🎨' }
                ],
                field: 'appearance',
                subField: 'style'
            },
            // 性格特征细分
            {
                id: 9,
                title: '性格特征偏好',
                subtitle: '选择您看重的性格特质（可多选）',
                type: 'multiChoice',
                options: [
                    { value: '温柔', text: '温柔', icon: '🌸' },
                    { value: '活泼', text: '活泼', icon: '😊' },
                    { value: '知性', text: '知性', icon: '🧠' },
                    { value: '成熟', text: '成熟', icon: '👔' },
                    { value: '浪漫', text: '浪漫', icon: '💕' }
                ],
                field: 'personality',
                subField: 'primary',
                maxSelections: 3
            },
            {
                id: 10,
                title: '沟通方式偏好',
                subtitle: '选择您偏好的沟通方式',
                type: 'choice',
                options: [
                    { value: '直接', text: '直接', icon: '💬' },
                    { value: '温和', text: '温和', icon: '😌' },
                    { value: '幽默', text: '幽默', icon: '😄' }
                ],
                field: 'personality',
                subField: 'communication'
            },
            // 生活方式细分
            {
                id: 11,
                title: '兴趣爱好',
                subtitle: '选择您希望伴侣拥有的兴趣爱好（可多选）',
                type: 'multiChoice',
                options: [
                    { value: '电影', text: '看电影', icon: '🎬' },
                    { value: '购物', text: '购物', icon: '🛍️' },
                    { value: '咖啡', text: '品咖啡', icon: '☕' },
                    { value: '园艺', text: '园艺', icon: '🌱' },
                    { value: '瑜伽', text: '瑜伽', icon: '🧘‍♀️' },
                    { value: '手工', text: '手工制作', icon: '✂️' },
                    { value: '收藏', text: '收藏', icon: '🏺' },
                    { value: '桌游', text: '桌游', icon: '🎲' }
                ],
                field: 'lifestyle',
                subField: 'activities',
                maxSelections: 3
            },
            {
                id: 12,
                title: '生活风格',
                subtitle: '选择您偏好的生活风格',
                type: 'choice',
                options: [
                    { value: '有条理', text: '有条理', icon: '📋' },
                    { value: '随性', text: '随性', icon: '😌' },
                    { value: '极简', text: '极简', icon: '🎯' }
                ],
                field: 'lifestyle',
                subField: 'livingStyle'
            },
            // 职业发展细分
            {
                id: 13,
                title: '行业领域偏好',
                subtitle: '选择您偏好的行业领域（可多选）',
                type: 'multiChoice',
                options: [
                    { value: '教育', text: '教育', icon: '📖' },
                    { value: '医疗', text: '医疗', icon: '🏥' },
                    { value: '科技', text: '科技', icon: '💻' },
                    { value: '艺术', text: '艺术', icon: '🎨' },
                    { value: '商务', text: '商务', icon: '💼' }
                ],
                field: 'career',
                subField: 'field',
                maxSelections: 3
            },
            {
                id: 14,
                title: '职业阶段偏好',
                subtitle: '选择您偏好的职业发展阶段',
                type: 'choice',
                options: [
                    { value: '入门', text: '入门', icon: '🌱' },
                    { value: '中层', text: '中层', icon: '📈' },
                    { value: '高级', text: '高级', icon: '⭐' },
                    { value: '专家', text: '专家', icon: '🏆' }
                ],
                field: 'career',
                subField: 'level'
            },
            // 价值观细分
            {
                id: 15,
                title: '家庭观念',
                subtitle: '选择您偏好的家庭观念',
                type: 'choice',
                options: [
                    { value: '传统', text: '传统', icon: '🏠' },
                    { value: '现代', text: '现代', icon: '🏢' },
                    { value: '灵活', text: '灵活', icon: '🔄' }
                ],
                field: 'values',
                subField: 'family'
            },
            {
                id: 16,
                title: '理财观念',
                subtitle: '选择您偏好的理财观念',
                type: 'choice',
                options: [
                    { value: '保守', text: '保守', icon: '🛡️' },
                    { value: '稳健', text: '稳健', icon: '📊' },
                    { value: '积极', text: '积极', icon: '📈' }
                ],
                field: 'values',
                subField: 'finance'
            },
            // 关系期望细分
            {
                id: 17,
                title: '承诺程度',
                subtitle: '选择您对关系的承诺程度期望',
                type: 'choice',
                options: [
                    { value: '随缘', text: '随缘', icon: '🍃' },
                    { value: '认真', text: '认真', icon: '💝' },
                    { value: '以结婚为目的', text: '以结婚为目的', icon: '💒' }
                ],
                field: 'relationship',
                subField: 'commitment'
            },
            {
                id: 18,
                title: '时间规划',
                subtitle: '选择您对关系发展的时间规划',
                type: 'choice',
                options: [
                    { value: '立即', text: '立即', icon: '⚡' },
                    { value: '6个月内', text: '6个月内', icon: '📅' },
                    { value: '1-2年', text: '1-2年', icon: '📆' },
                    { value: '随缘', text: '随缘', icon: '🍃' }
                ],
                field: 'relationship',
                subField: 'timeline'
            }
        ];

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('JavaScript错误:', e.error);
        });

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 尝试初始化服务，如果失败则使用默认值
                if (typeof UnifiedAuthService !== 'undefined') {
                    window.unifiedAuthService = new UnifiedAuthService();
                } else {
                    console.warn('UnifiedAuthService 未定义，使用默认值');
                    window.unifiedAuthService = { /* 默认实现 */ };
                }
                
                if (typeof MemberService !== 'undefined') {
                    window.memberService = new MemberService();
                } else {
                    console.warn('MemberService 未定义，使用默认值');
                    window.memberService = { /* 默认实现 */ };
                }
                
                if (typeof OrderPaymentManager !== 'undefined') {
                    window.orderPaymentManager = new OrderPaymentManager();
                } else {
                    console.warn('OrderPaymentManager 未定义，使用默认值');
                    window.orderPaymentManager = { 
                        createOrderAndPay: function(config, data, successCallback, cancelCallback) {
                            console.log('模拟支付流程');
                            // 直接调用成功回调
                            setTimeout(() => successCallback({}, {}), 1000);
                        }
                    };
                }
                
                // 初始化AI服务
                try {
                    // 总是初始化通用AI服务，支持所有AI提供商
                    window.aiService = new AIService({
                        SERVICE_TYPE: window.AI_CONFIG.SERVICE_TYPE,
                        DEEPSEEK_API_KEY: window.AI_CONFIG.DEEPSEEK_API_KEY,
                        DEEPSEEK_BASE_URL: window.AI_CONFIG.DEEPSEEK_BASE_URL,
                        ZHIPU_API_KEY: window.AI_CONFIG.ZHIPU_API_KEY,
                        ZHIPU_BASE_URL: window.AI_CONFIG.ZHIPU_BASE_URL,
                        ZHIPU_MODEL: window.AI_CONFIG.ZHIPU_TEXT_MODEL
                    });
                    console.log('✅ 通用AI服务初始化成功');

                    if (window.AI_CONFIG.SERVICE_TYPE === 'zhipu') {
                        // 同时初始化专门的智谱AI服务（用于图像生成）
                        window.zhipuAIService = new ZhipuAIService({
                            ZHIPU_API_KEY: window.AI_CONFIG.ZHIPU_API_KEY,
                            ZHIPU_BASE_URL: window.AI_CONFIG.ZHIPU_BASE_URL,
                            ZHIPU_TEXT_MODEL: window.AI_CONFIG.ZHIPU_TEXT_MODEL,
                            ZHIPU_IMAGE_MODEL: window.AI_CONFIG.ZHIPU_IMAGE_MODEL,
                            SERVICE_TYPE: 'zhipu'
                        });
                        console.log('✅ 智谱AI专用服务初始化成功');
                    }
                } catch (error) {
                    console.error('❌ AI服务初始化失败:', error);
                }

                // 检查必要的服务是否已加载
                console.log('💕 智能姻缘画像向导页面已加载');
                console.log('🔍 检查服务加载状态:');
                console.log('- PortraitAI:', typeof window.PortraitAI !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
                console.log('- ZhipuAIService:', typeof window.ZhipuAIService !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
                console.log('- AIService:', typeof window.AIService !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
                console.log('- UnifiedAuthService:', typeof UnifiedAuthService !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
                console.log('- MemberService:', typeof MemberService !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
                console.log('- OrderPaymentManager:', typeof OrderPaymentManager !== 'undefined' ? '✅ 已加载' : '❌ 未加载');
                console.log('- 当前AI服务类型:', window.AI_CONFIG.SERVICE_TYPE);

                renderCurrentStep();
                
                // 添加全局测试函数
                window.testNextStep = function() {
                    console.log('测试 nextStep 函数');
                    nextStep();
                };
            } catch (error) {
                console.error('初始化失败:', error);
                // 即使初始化失败，也要渲染页面
                renderCurrentStep();
            }
        });

        // 渲染当前步骤
        function renderCurrentStep() {
            const step = steps.find(s => s.id === wizardState.currentStep);
            if (!step) return;

            // 记录当前步骤
            console.log(`\n🔄 进入步骤${wizardState.currentStep}/${wizardState.totalSteps} - ${step.title}`);
            console.log(`📝 步骤描述: ${step.subtitle}`);

            const stepContent = document.getElementById('stepContent');
            
            if (step.type === 'detailedForm') {
                // 渲染详细参数表单
                renderDetailedForm(stepContent, step);
            } else if (step.type === 'choice') {
                renderChoiceStep(stepContent, step);
            } else if (step.type === 'multiChoice') {
                renderMultiChoiceStep(stepContent, step);
            } else if (step.type === 'rangeForm') {
                renderRangeForm(stepContent, step);
            }

            updateProgress();
            updateButtons();
        }

        // 渲染选择步骤
        function renderChoiceStep(container, step) {
            const currentValue = step.subField ? 
                (wizardState.userData[step.field]?.[step.subField] || '') : 
                (wizardState.userData[step.field] || '');
            
            container.innerHTML = `
                <h2 class="step-title">${step.title}</h2>
                <p class="step-subtitle">${step.subtitle}</p>
                <div class="options-container">
                    ${step.options.map(option => `
                        <div class="option-card ${currentValue === option.value ? 'selected' : ''}" 
                             onclick="selectOption('${option.value}', '${step.field}', '${step.subField || ''}')">
                            <span class="option-icon">${option.icon}</span>
                            <span class="option-text">${option.text}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 渲染多选步骤
        function renderMultiChoiceStep(container, step) {
            const currentSelections = step.subField ? 
                (wizardState.userData[step.field]?.[step.subField] || []) : 
                (wizardState.userData[step.field] || []);
            
            container.innerHTML = `
                <h2 class="step-title">${step.title}</h2>
                <p class="step-subtitle">${step.subtitle}</p>
                <div class="options-container">
                    ${step.options.map(option => `
                        <div class="option-card ${currentSelections.includes(option.value) ? 'selected' : ''}" 
                             onclick="selectMultiOption('${option.value}', '${step.field}', '${step.subField || ''}')">
                            <span class="option-icon">${option.icon}</span>
                            <span class="option-text">${option.text}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 渲染范围表单步骤
        function renderRangeForm(container, step) {
            const currentValue = wizardState.userData[step.field]?.[step.subField] || step.config.default;
            
            // 确保数据被初始化
            if (!wizardState.userData[step.field]) {
                wizardState.userData[step.field] = {};
            }
            if (wizardState.userData[step.field][step.subField] === undefined) {
                wizardState.userData[step.field][step.subField] = step.config.default;
            }
            
            container.innerHTML = `
                <h2 class="step-title">${step.title}</h2>
                <p class="step-subtitle">${step.subtitle}</p>
                <div class="range-form-container">
                    <div class="range-input-group">
                        <label class="range-label">${step.config.label}</label>
                        <div class="range-control">
                            <input type="range" 
                                   id="rangeInput" 
                                   min="${step.config.min}" 
                                   max="${step.config.max}" 
                                   value="${currentValue}" 
                                   class="range-input"
                                   oninput="updateRangeValue(this.value, '${step.field}', '${step.subField}')">
                            <span class="range-value">${currentValue}</span>
                        </div>
                        <div class="range-labels">
                            <span>${step.config.min}</span>
                            <span>${step.config.max}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 渲染详细参数表单
        function renderDetailedForm(container, step) {
            const formConfig = new PortraitFormConfig();
            const formHTML = formConfig.generateFormHTML(step.category);
            
            container.innerHTML = `
                <h2 class="step-title">${step.title}</h2>
                <p class="step-subtitle">${step.subtitle}</p>
                <div class="detailed-form-container">
                    ${formHTML}
                </div>
            `;

            // 添加表单事件监听器
            addFormEventListeners();
        }

        // 添加表单事件监听器
        function addFormEventListeners() {
            // 范围滑块事件
            const rangeInputs = document.querySelectorAll('.range-input');
            rangeInputs.forEach(input => {
                input.addEventListener('input', function() {
                    const valueSpan = this.parentNode.querySelector('.range-value');
                    if (valueSpan) {
                        valueSpan.textContent = this.value;
                    }
                });
            });
        }

        // 选择选项
        function selectOption(value, field, subField = null) {
            const currentStep = steps.find(s => s.id === wizardState.currentStep);

            if (subField) {
                if (!wizardState.userData[field]) {
                    wizardState.userData[field] = {};
                }
                wizardState.userData[field][subField] = value;
                console.log(`🎯 步骤${wizardState.currentStep} - ${currentStep.title}: 选择了 ${field}.${subField} = "${value}"`);
            } else {
                wizardState.userData[field] = value;
                console.log(`🎯 步骤${wizardState.currentStep} - ${currentStep.title}: 选择了 ${field} = "${value}"`);
            }

            // 输出当前完整的用户数据
            console.log('📊 当前用户数据:', JSON.stringify(wizardState.userData, null, 2));
            renderCurrentStep();
        }

        // 选择多选选项
        function selectMultiOption(value, field, subField = null) {
            const currentStep = steps.find(s => s.id === wizardState.currentStep);
            let action = '';

            if (subField) {
                if (!wizardState.userData[field]) {
                    wizardState.userData[field] = {};
                }
                if (!wizardState.userData[field][subField]) {
                    wizardState.userData[field][subField] = [];
                }
                const currentSelections = wizardState.userData[field][subField];
                const step = steps.find(s => s.field === field && s.subField === subField);

                if (currentSelections.includes(value)) {
                    // 取消选择
                    const index = currentSelections.indexOf(value);
                    currentSelections.splice(index, 1);
                    action = '取消选择';
                } else {
                    // 添加选择
                    if (step.maxSelections && currentSelections.length >= step.maxSelections) {
                        alert(`最多只能选择${step.maxSelections}项`);
                        return;
                    }
                    currentSelections.push(value);
                    action = '添加选择';
                }
                console.log(`🎯 步骤${wizardState.currentStep} - ${currentStep.title}: ${action} ${field}.${subField} = "${value}"`);
                console.log(`📝 当前${field}.${subField}选择: [${currentSelections.join(', ')}]`);
            } else {
                if (!wizardState.userData[field]) {
                    wizardState.userData[field] = [];
                }

                const currentSelections = wizardState.userData[field];
                const step = steps.find(s => s.field === field);

                if (currentSelections.includes(value)) {
                    // 取消选择
                    const index = currentSelections.indexOf(value);
                    currentSelections.splice(index, 1);
                    action = '取消选择';
                } else {
                    // 添加选择
                    if (step.maxSelections && currentSelections.length >= step.maxSelections) {
                        alert(`最多只能选择${step.maxSelections}项`);
                        return;
                    }
                    currentSelections.push(value);
                    action = '添加选择';
                }
                console.log(`🎯 步骤${wizardState.currentStep} - ${currentStep.title}: ${action} ${field} = "${value}"`);
                console.log(`📝 当前${field}选择: [${currentSelections.join(', ')}]`);
            }

            // 输出当前完整的用户数据
            console.log('📊 当前用户数据:', JSON.stringify(wizardState.userData, null, 2));
            renderCurrentStep();
        }

        // 更新范围值
        function updateRangeValue(value, field, subField) {
            const currentStep = steps.find(s => s.id === wizardState.currentStep);

            if (!wizardState.userData[field]) {
                wizardState.userData[field] = {};
            }
            wizardState.userData[field][subField] = parseInt(value);

            console.log(`🎯 步骤${wizardState.currentStep} - ${currentStep.title}: 设置 ${field}.${subField} = ${value}`);
            console.log('📊 当前用户数据:', JSON.stringify(wizardState.userData, null, 2));

            // 更新显示的数值
            const valueSpan = document.querySelector('.range-value');
            if (valueSpan) {
                valueSpan.textContent = value;
            }
        }

        // 更新按钮
        function updateButtons() {
            const stepContent = document.getElementById('stepContent');
            const currentStep = steps.find(s => s.id === wizardState.currentStep);
            
            let canProceed = true;
            
            if (currentStep.type === 'choice') {
                const currentValue = currentStep.subField ? 
                    wizardState.userData[currentStep.field]?.[currentStep.subField] : 
                    wizardState.userData[currentStep.field];
                if (!currentValue) {
                    canProceed = false;
                }
            } else if (currentStep.type === 'multiChoice') {
                const currentSelections = currentStep.subField ? 
                    wizardState.userData[currentStep.field]?.[currentStep.subField] : 
                    wizardState.userData[currentStep.field];
                if (!currentSelections || currentSelections.length === 0) {
                    canProceed = false;
                }
                if (currentSelections && currentSelections.length < 2) {
                    canProceed = false;
                }
            } else if (currentStep.type === 'rangeForm') {
                // 范围滑块总是有值的，所以总是可以继续
                canProceed = true;
            } else if (currentStep.type === 'detailedForm') {
                // 检查当前步骤的详细表单是否填写完整
                canProceed = validateDetailedFormStep(currentStep);
            }
            
            // 自动设置异性偏好
            if (currentStep.field === 'gender' && wizardState.userData.gender) {
                wizardState.userData.preferredGender = wizardState.userData.gender === 'male' ? 'female' : 'male';
            }
            
            // 添加调试信息
            console.log('当前步骤:', currentStep);
            console.log('canProceed:', canProceed);
            console.log('当前步骤类型:', currentStep.type);
            
            // 添加按钮
            const btnContainer = document.createElement('div');
            btnContainer.className = 'btn-container';
            
            if (wizardState.currentStep > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.className = 'btn btn-secondary';
                prevBtn.textContent = '上一步';
                prevBtn.addEventListener('click', prevStep);
                btnContainer.appendChild(prevBtn);
            }
            
            const nextBtn = document.createElement('button');
            nextBtn.className = 'btn btn-primary';
            nextBtn.textContent = wizardState.currentStep === wizardState.totalSteps ? '生成画像' : '下一步';
            nextBtn.disabled = !canProceed;
            
            // 添加测试点击事件
            nextBtn.addEventListener('click', function(e) {
                console.log('按钮被点击了！');
                console.log('当前步骤:', wizardState.currentStep);
                console.log('按钮文本:', nextBtn.textContent);
                console.log('按钮是否禁用:', nextBtn.disabled);
                e.preventDefault();
                e.stopPropagation();
            });
            
            // 使用 addEventListener 而不是 onclick
            if (wizardState.currentStep === wizardState.totalSteps) {
                nextBtn.addEventListener('click', generatePortrait);
            } else {
                nextBtn.addEventListener('click', nextStep);
            }
            
            btnContainer.appendChild(nextBtn);
            
            // 移除旧的按钮容器
            const oldBtnContainer = stepContent.querySelector('.btn-container');
            if (oldBtnContainer) {
                oldBtnContainer.remove();
            }
            
            stepContent.appendChild(btnContainer);
        }

        // 验证详细表单步骤
        function validateDetailedFormStep(step) {
            const formConfig = new PortraitFormConfig();
            const config = formConfig.getConfig();
            const category = step.category;
            
            if (!config[category]) return false;
            
            // 检查该分类下的所有字段是否都有值
            for (const field in config[category]) {
                const fieldName = `${category}[${field}]`;
                const input = document.querySelector(`[name="${fieldName}"]`);
                
                if (!input) continue;
                
                if (input.type === 'range') {
                    // 范围滑块总是有默认值，所以总是有效的
                    continue;
                } else if (input.type === 'checkbox') {
                    const checkboxes = document.querySelectorAll(`[name="${fieldName}"]:checked`);
                    if (checkboxes.length === 0) {
                        return false;
                    }
                } else {
                    if (!input.value || input.value.trim() === '') {
                        return false;
                    }
                }
            }
            
            return true;
        }

        // 下一步
        function nextStep() {
            console.log('nextStep 被调用，当前步骤:', wizardState.currentStep);
            
            // 如果是详细表单步骤，先收集当前步骤的数据
            const currentStep = steps.find(s => s.id === wizardState.currentStep);
            if (currentStep.type === 'detailedForm') {
                collectCurrentStepData(currentStep);
            }
            
            if (wizardState.currentStep < wizardState.totalSteps) {
                wizardState.currentStep++;
                renderCurrentStep();
            }
        }

        // 收集当前步骤的数据
        function collectCurrentStepData(step) {
            const formConfig = new PortraitFormConfig();
            const config = formConfig.getConfig();
            const category = step.category;
            
            if (!config[category]) return;
            
            wizardState.userData[category] = {};
            
            for (const field in config[category]) {
                const fieldName = `${category}[${field}]`;
                const input = document.querySelector(`[name="${fieldName}"]`);
                
                if (input) {
                    if (input.type === 'range') {
                        wizardState.userData[category][field] = parseInt(input.value);
                    } else if (input.type === 'checkbox') {
                        const checkboxes = document.querySelectorAll(`[name="${fieldName}"]:checked`);
                        wizardState.userData[category][field] = Array.from(checkboxes).map(cb => cb.value);
                    } else {
                        wizardState.userData[category][field] = input.value;
                    }
                }
            }
        }

        // 上一步
        function prevStep() {
            if (wizardState.currentStep > 1) {
                wizardState.currentStep--;
                renderCurrentStep();
            }
        }

        // 更新进度
        function updateProgress() {
            const progress = (wizardState.currentStep / wizardState.totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `步骤 ${wizardState.currentStep} / ${wizardState.totalSteps}`;
        }

        // 生成画像
        async function generatePortrait() {
            // 收集最后一步的数据（如果有的话）
            const currentStep = steps.find(s => s.id === wizardState.currentStep);
            if (currentStep.type === 'detailedForm') {
                collectCurrentStepData(currentStep);
            }
            // 对于新的步骤类型，数据已经在选择时收集了，不需要额外收集
            
            // 服务配置
            const serviceConfig = {
                type: 'portrait',
                name: '智能姻缘画像',
                price: 19.9,
                description: '根据您的偏好生成专属姻缘画像'
            };

            // 创建订单并支付
            try {
                await window.orderPaymentManager.createOrderAndPay(
                    serviceConfig,
                    wizardState.userData,
                    // 支付成功回调
                    async function(order, paymentResult) {
                        console.log('支付成功，开始生成画像');
                        await performPortraitGeneration();
                    },
                    // 取消支付回调
                    function(order) {
                        console.log('用户取消支付');
                        // 返回上一步
                        wizardState.currentStep--;
                        renderCurrentStep();
                    }
                );
            } catch (error) {
                console.error('订单创建失败:', error);
                alert('创建订单失败，请稍后重试');
                // 返回上一步
                wizardState.currentStep--;
                renderCurrentStep();
            }
        }



        // 执行画像生成
        async function performPortraitGeneration() {
            const stepContent = document.getElementById('stepContent');
            stepContent.innerHTML = `
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">正在分析您的偏好，生成专属姻缘画像...</p>
                </div>
            `;

            try {
                // 输出完整的选择总结
                console.log('🎉 ===== 姻缘画像选择总结 =====');
                console.log('📋 完整的用户选择数据:', JSON.stringify(wizardState.userData, null, 2));

                // 按步骤输出选择详情
                console.log('\n📝 按步骤详细选择:');
                steps.forEach(step => {
                    const fieldValue = wizardState.userData[step.field];
                    if (step.subField && fieldValue && fieldValue[step.subField] !== undefined) {
                        const value = fieldValue[step.subField];
                        if (Array.isArray(value)) {
                            console.log(`步骤${step.id} - ${step.title}: [${value.join(', ')}]`);
                        } else {
                            console.log(`步骤${step.id} - ${step.title}: ${value}`);
                        }
                    } else if (!step.subField && fieldValue !== undefined) {
                        if (Array.isArray(fieldValue)) {
                            console.log(`步骤${step.id} - ${step.title}: [${fieldValue.join(', ')}]`);
                        } else {
                            console.log(`步骤${step.id} - ${step.title}: ${fieldValue}`);
                        }
                    }
                });
                console.log('🎉 ===== 选择总结结束 =====\n');

                // 调用AI服务生成画像
                const portraitResult = await generatePortraitImages(wizardState.userData);

                // 显示结果
                showPortraitResult(portraitResult);
            } catch (error) {
                console.error('生成画像失败:', error);
                stepContent.innerHTML = `
                    <div class="result-container">
                        <h3 class="result-title">生成失败</h3>
                        <p>抱歉，生成画像时出现错误，请稍后重试。</p>
                        <button class="btn btn-primary" onclick="renderCurrentStep()">重新生成</button>
                    </div>
                `;
            }
        }

        // 显示画像结果
        function showPortraitResult(result) {
            const stepContent = document.getElementById('stepContent');
            stepContent.innerHTML = `
                <div class="result-container">
                    <h3 class="result-title">💕 您的智能姻缘画像</h3>
                    <div class="carousel-container">
                        <div class="carousel-wrapper" id="carouselWrapper">
                            ${result.images.map((img, index) => `
                                <div class="carousel-slide">
                                    <img src="${img}" alt="姻缘画像 ${index + 1}" class="carousel-image" 
                                         onload="this.classList.add('loaded')" 
                                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSIjOTk5Ij7lm77niYfliqDovb3lpLHotKU8L3RleHQ+Cjwvc3ZnPgo='; this.classList.add('loaded')">
                                </div>
                            `).join('')}
                        </div>
                        <button class="carousel-nav prev" onclick="changeSlide(-1)">‹</button>
                        <button class="carousel-nav next" onclick="changeSlide(1)">›</button>
                        <div class="carousel-dots" id="carouselDots">
                            ${result.images.map((_, index) => `
                                <div class="carousel-dot ${index === 0 ? 'active' : ''}" onclick="goToSlide(${index})"></div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="btn-container">
                        <button class="btn btn-secondary" onclick="restartWizard()">重新开始</button>
                        <button class="btn btn-primary" onclick="downloadImages()">保存图片</button>
                    </div>
                </div>
            `;

            // 初始化轮播图
            window.currentSlide = 0;
            window.totalSlides = result.images.length;
        }

        // 轮播图控制
        function changeSlide(direction) {
            const wrapper = document.getElementById('carouselWrapper');
            const dots = document.querySelectorAll('.carousel-dot');
            
            window.currentSlide = (window.currentSlide + direction + window.totalSlides) % window.totalSlides;
            
            wrapper.style.transform = `translateX(-${window.currentSlide * 100}%)`;
            
            // 更新圆点状态
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === window.currentSlide);
            });
        }

        function goToSlide(index) {
            const wrapper = document.getElementById('carouselWrapper');
            const dots = document.querySelectorAll('.carousel-dot');
            
            window.currentSlide = index;
            wrapper.style.transform = `translateX(-${index * 100}%)`;
            
            // 更新圆点状态
            dots.forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });
        }

        // 重新开始向导
        function restartWizard() {
            console.log('\n🚀 ===== 开始姻缘画像向导 =====');
            console.log('📋 总共18个步骤，将收集您的详细偏好');
            console.log('🎯 每个选择都会被记录在日志中');
            console.log('===============================\n');

            wizardState.currentStep = 1;
            wizardState.userData = {
                gender: '',
                age: '',
                zodiac: '',
                preferredGender: '',
                preferredAge: '',
                priorities: [],
                appearance: {},
                personality: {},
                lifestyle: {},
                career: {},
                values: {},
                relationship: {}
            };
            renderCurrentStep();
        }

        // 下载图片
        function downloadImages() {
            // 这里可以实现下载功能
            alert('下载功能开发中...');
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 生成画像图片（调用现有的AI服务）
        async function generatePortraitImages(userData) {
            try {
                // 检查PortraitAI类是否可用
                if (typeof window.PortraitAI === 'undefined') {
                    console.error('PortraitAI类未加载，请检查portrait-ai.js文件');
                    throw new Error('PortraitAI服务不可用');
                }

                // 初始化AI服务
                if (!window.portraitAI) {
                    console.log('初始化PortraitAI服务...');
                    window.portraitAI = new window.PortraitAI();
                }
                
                // 调用AI服务生成画像
                const result = await window.portraitAI.generateWizardPortrait(userData);
                
                // 确保返回的是数组格式
                if (result && Array.isArray(result)) {
                    return {
                        images: result
                    };
                } else if (result && result.images) {
                    return {
                        images: result.images,
                        analysis: result.analysis
                    };
                } else {
                    // 如果AI服务失败，返回默认图片
                    return {
                        images: [
                            'https://via.placeholder.com/400x600/FFB6C1/FFFFFF?text=优雅风格画像',
                            'https://via.placeholder.com/400x600/FF69B4/FFFFFF?text=休闲风格画像',
                            'https://via.placeholder.com/400x600/DC143C/FFFFFF?text=职业风格画像'
                        ]
                    };
                }
            } catch (error) {
                console.error('生成画像失败:', error);
                // 返回默认图片
                return {
                    images: [
                        'https://via.placeholder.com/400x600/FFB6C1/FFFFFF?text=优雅风格画像',
                        'https://via.placeholder.com/400x600/FF69B4/FFFFFF?text=休闲风格画像',
                        'https://via.placeholder.com/400x600/DC143C/FFFFFF?text=职业风格画像'
                    ]
                };
            }
        }
    </script>
</body>
</html> 