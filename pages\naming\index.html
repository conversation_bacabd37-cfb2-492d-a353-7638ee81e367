<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>姓名详批 - 易海堂算命网</title>
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="./style.css">
    <!-- 认证服务 -->
     <script src="../../js/modules/ai-config-manager.js"></script>
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script src="../../js/modules/auth-service.js"></script>
    <!-- 认证初始化 -->
    <script src="../../js/modules/auth-init.js"></script>
    <!-- 订单和支付服务 -->
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <!-- 业务模块 -->
    <script src="../../js/modules/name-ai.js"></script>
    
    <style>
        /* AI分析区域样式 */
        .ai-analysis-section {
            margin: 0px 0;
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .ai-section-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .header-left {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .ai-section-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
            color: white;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .close-btn:active {
            transform: scale(0.95);
        }

        .close-icon {
            font-size: 20px;
            font-weight: bold;
            line-height: 1;
        }
        
        .ai-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            align-self: flex-start;
            margin-top: 5px;
        }
        
        .ai-icon {
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        /* AI加载动画 */
        .ai-loading {
            text-align: center;
            padding: 40px 20px;
        }
        
        .ai-loading-spinner {
            font-size: 48px;
            animation: aiSpin 3s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes aiSpin {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }
        
        .ai-loading-text {
            font-size: 18px;
            margin-bottom: 25px;
            font-weight: 500;
        }
        
        .ai-loading-steps {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .ai-loading-steps .step {
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0.5;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-size: 14px;
        }
        
        .ai-loading-steps .step.active {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .ai-loading-steps .step.completed {
            opacity: 1;
            background: rgba(40, 167, 69, 0.8);
            color: white;
        }
        
        /* AI内容样式 */
        .ai-content {
            line-height: 1.8;
        }
        
        .ai-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border-left: 4px solid rgba(255, 255, 255, 0.5);
        }
        
        .ai-section h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: bold;
            color: #FFD700;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .ai-section p {
            margin: 0;
            line-height: 1.7;
            font-size: 14px;
        }
        
        .ai-summary {
            background: rgba(255, 215, 0, 0.2);
            border-left-color: #FFD700;
            font-size: 16px;
            font-weight: 500;
        }
        
        .ai-recommendations {
            background: rgba(40, 167, 69, 0.2);
            border-left-color: #28a745;
        }
        
        .ai-recommendations ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .ai-recommendations li {
            margin: 8px 0;
            font-size: 14px;
        }
        
        .ai-score-display {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 15px 0;
        }
        
        .ai-score-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        .ai-score-info {
            flex: 1;
        }
        
        .ai-score-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 5px;
        }
        
        .ai-score-desc {
            font-size: 16px;
            font-weight: 500;
        }
        
        .lucky-elements {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .lucky-element {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 建议列表样式 */
        .recommendations-list {
            margin: 0;
            padding-left: 20px;
        }

        .recommendations-list li {
            margin-bottom: 8px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }

        /* 开运要素网格样式 */
        .lucky-elements-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .lucky-element-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .lucky-label {
            font-size: 13px;
            color: #FFD700;
            font-weight: 500;
        }

        .lucky-values {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 400;
        }

        /* 三才数理样式 */
        .strokes-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-top: 15px;
        }

        .strokes-grid .stroke-item:nth-child(4),
        .strokes-grid .stroke-item:nth-child(5) {
            grid-column: span 1;
        }

        .strokes-grid .stroke-item:nth-child(5) {
            grid-column: 2 / 3;
        }

        .stroke-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 3px solid #4A90E2;
        }

        .stroke-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .stroke-value {
            font-size: 16px;
            color: white;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
        }

        /* 五行配置样式 */
        .wuxing-info {
            margin-top: 15px;
        }

        .wuxing-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 3px solid #FFA500;
        }

        .wuxing-item:last-child {
            margin-bottom: 0;
        }

        .wuxing-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .wuxing-value {
            font-size: 14px;
            color: #FFA500;
            font-weight: bold;
            text-align: right;
            flex: 1;
            margin-left: 15px;
        }

        /* 基础分析结果样式 */
        .result-content {
            padding: 20px;
            background: white;
            border-radius: 0 0 20px 20px;
        }

        .result-content h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: bold;
        }

        .result-content .stroke-item,
        .result-content .wuxing-item {
            background: #f8f9fa;
            border-left-color: #6c757d;
        }

        .result-content .stroke-value,
        .result-content .wuxing-value {
            color: #495057;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .ai-analysis-section {
                margin: 0px 0;
                padding: 20px;
            }
            
            .ai-section-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: flex-start;
                gap: 15px;
            }

            .header-left {
                flex: 1;
            }

            .ai-badge {
                align-self: flex-start;
            }

            .close-btn {
                width: 32px;
                height: 32px;
                flex-shrink: 0;
            }

            .close-icon {
                font-size: 18px;
            }
            
            .ai-loading-steps {
                grid-template-columns: 1fr;
                max-width: 100%;
            }
            
            .ai-section {
                padding: 15px;
            }
            
            .ai-score-display {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .lucky-elements-grid {
                grid-template-columns: 1fr;
            }

            .strokes-grid {
                grid-template-columns: 1fr 1fr;
            }

            .strokes-grid .stroke-item:nth-child(5) {
                grid-column: 1 / -1;
            }

            .stroke-item,
            .wuxing-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .stroke-value,
            .wuxing-value {
                text-align: left;
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="header-container">
            <div class="back-btn" onclick="history.back()">
                <span class="back-icon">←</span>
            </div>
            <h1 class="header-title">📝 姓名详批</h1>
            <div class="header-right"></div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- Canvas动画背景 -->
        <canvas id="namingCanvas" class="naming-canvas"></canvas>
        
        <!-- 页面头部介绍 -->
        <section class="naming-intro">
            <div class="intro-container">
                <div class="naming-icon">📝</div>
                <h2 class="intro-title">姓名五行详批</h2>
                <p class="intro-desc">结合您的八字五行，深度解析姓名的吉凶寓意</p>
            </div>
        </section>

        <!-- 姓名测算表单 -->
        <section class="naming-form-section">
            <div class="form-container">
                <form class="naming-form" id="namingForm">
                    <!-- 姓名输入 -->
                    <div class="input-group">
                        <label for="userName">
                            <span class="label-icon">👤</span>
                            <span class="label-text">您的姓名</span>
                        </label>
                        <input type="text" 
                               id="userName" 
                               name="userName" 
                               placeholder="请输入您的真实姓名" 
                               required>
                    </div>
                    
                    <!-- 性别选择 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">👥</span>
                            <span class="label-text">性别</span>
                        </label>
                        <div class="gender-options">
                            <label class="gender-option">
                                <input type="radio" name="gender" value="male" required>
                                <span class="gender-btn male">
                                    <span class="gender-icon">♂</span>
                                    <span>男</span>
                                </span>
                            </label>
                            <label class="gender-option">
                                <input type="radio" name="gender" value="female" required>
                                <span class="gender-btn female">
                                    <span class="gender-icon">♀</span>
                                    <span>女</span>
                                </span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 宝宝出生日期 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">📅</span>
                            <span class="label-text">请选择您的农历出生日期</span>
                        </label>
                        <div class="baby-date-picker" id="babyDatePicker" onclick="openBabyDatePicker()">
                            <div class="date-input-display">
                                <span class="date-placeholder" id="dateDisplayText">请选择日期</span>
                                <div class="date-picker-btn">
                                    <span class="picker-icon">📅</span>
                                </div>
                            </div>
                        </div>
                        <!-- 隐藏的输入字段存储选中的值 -->
                        <input type="hidden" id="birthDate" name="birthDate" required>
                        <input type="hidden" id="birthHour" name="birthHour" required>
                        <input type="hidden" id="selectedYear" name="selectedYear">
                        <input type="hidden" id="selectedMonth" name="selectedMonth">
                        <input type="hidden" id="selectedDay" name="selectedDay">
                    </div>
                    
                    <!-- 出生时辰 -->
                    <div class="input-group">
                        <label for="birthHourSelect">
                            <span class="label-icon">⏰</span>
                            <span class="label-text">出生时辰</span>
                        </label>
                        <select id="birthHourSelect" name="birthHourSelect" required>
                            <option value="">请选择时辰</option>
                            <option value="zi">子时 (23:00-01:00)</option>
                            <option value="chou">丑时 (01:00-03:00)</option>
                            <option value="yin">寅时 (03:00-05:00)</option>
                            <option value="mao">卯时 (05:00-07:00)</option>
                            <option value="chen">辰时 (07:00-09:00)</option>
                            <option value="si">巳时 (09:00-11:00)</option>
                            <option value="wu">午时 (11:00-13:00)</option>
                            <option value="wei">未时 (13:00-15:00)</option>
                            <option value="shen">申时 (15:00-17:00)</option>
                            <option value="you">酉时 (17:00-19:00)</option>
                            <option value="xu">戌时 (19:00-21:00)</option>
                            <option value="hai">亥时 (21:00-23:00)</option>
                        </select>
                    </div>
                    
                    <!-- 关注重点 -->
                    <div class="input-group">
                        <label>
                            <span class="label-icon">🎯</span>
                            <span class="label-text">关注重点（可选）</span>
                        </label>
                        <div class="focus-options">
                            <div class="focus-item">
                                <input type="checkbox" id="meaning" name="focus" value="meaning">
                                <label for="meaning" class="focus-label">姓名寓意</label>
                            </div>
                            <div class="focus-item">
                                <input type="checkbox" id="wuxing" name="focus" value="wuxing">
                                <label for="wuxing" class="focus-label">五行分析</label>
                            </div>
                            <div class="focus-item">
                                <input type="checkbox" id="stroke" name="focus" value="stroke">
                                <label for="stroke" class="focus-label">笔画吉凶</label>
                            </div>
                            <div class="focus-item">
                                <input type="checkbox" id="personality" name="focus" value="personality">
                                <label for="personality" class="focus-label">性格特征</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="submit-section">
                        <button type="submit" class="submit-btn naming-submit">
                            <span class="btn-icon">📝</span>
                            <span class="btn-text">开始姓名详批</span>
                        </button>
                        <p class="submit-note">🔮 专业解析您的姓名奥秘，洞察人生运势</p>
                    </div>
                </form>
            </div>
        </section>

        <!-- 姓名知识卡片 -->
        <section class="naming-tips">
            <div class="tips-container">
                <h3 class="tips-title">💡 姓名学知识</h3>
                <div class="tips-grid">
                    <div class="tip-card">
                        <div class="tip-icon">📚</div>
                        <h4>五行配置</h4>
                        <p>分析姓名五行搭配，判断相生相克关系</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">🔢</div>
                        <h4>三才数理</h4>
                        <p>天格、人格、地格数理吉凶分析</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">💎</div>
                        <h4>字形字意</h4>
                        <p>汉字本身的寓意和象形含义解析</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">🌟</div>
                        <h4>运势影响</h4>
                        <p>姓名对事业、感情、健康的影响</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载动画 -->
    <div id="loadingOverlay" class="loading-overlay-naming">
        <div class="loading-container">
            <div class="loading-spinner">
                <span class="spinner-icon">📝</span>
            </div>
            <p class="loading-text">正在分析您的姓名...</p>
            <div class="loading-progress">
                <div class="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- 结果展示模态框 -->
    <div id="resultModal" class="result-modal">
        <div class="result-container">
            <!-- AI深度分析区域 -->
            <div class="ai-analysis-section" id="aiAnalysisSection" style="display: none;">
                <div class="ai-section-header">
                    <div class="header-left">
                        <h3>姓名详批</h3>
                        <div class="ai-badge">
                            <span class="ai-icon">✨</span>
                            <span id="aiServiceType">易海堂</span>
                        </div>
                    </div>
                    <button class="close-btn" onclick="closeResultModal()">
                        <span class="close-icon">×</span>
                    </button>
                </div>
                
                <div class="ai-loading" id="aiLoading" style="display: none;">
                    <div class="ai-loading-spinner">🧠</div>
                    <div class="ai-loading-text">正在深度分析您的姓名...</div>
                    <div class="ai-loading-steps">
                        <div class="step" id="aiStep1">📝 分析字义内涵</div>
                        <div class="step" id="aiStep2">⚖️ 计算五行配置</div>
                        <div class="step" id="aiStep3">🔮 预测人生运势</div>
                        <div class="step" id="aiStep4">💡 生成专业建议</div>
                    </div>
                </div>

                <!-- AI分析内容显示区域 -->
                <div class="ai-content" id="aiContent" style="display: none;">
                    <!-- AI分析结果将在这里显示 -->
                </div>
            </div>

            <!-- 基础分析结果显示区域 -->
            <div id="resultContent" class="result-content">
                <!-- 基础分析结果将在这里显示 -->
            </div>
            <div class="result-actions">
                <button class="action-btn save-btn" onclick="saveResult()">
                    <span class="btn-icon">💾</span>
                    <span>保存结果</span>
                </button>
                <button class="action-btn share-btn" onclick="shareResult()">
                    <span class="btn-icon">📤</span>
                    <span>分享结果</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 宝宝日期选择器模态框 -->
    <div id="babyDateModal" class="baby-date-modal">
        <div class="baby-modal-container">
            <!-- 粉色头部 -->
            <div class="baby-modal-header">
                <div class="header-icon">📅</div>
                <h3>请选择农历出生日期</h3>
            </div>
            
            <!-- 日期输入框 -->
            <div class="date-input-container">
                <div class="date-input-box">
                    <span class="date-value" id="modalDateDisplay">请选择日期</span>
                    <div class="date-confirm-btn" onclick="confirmDate()">
                        <span class="confirm-icon">✓</span>
                    </div>
                </div>
            </div>
            
            <!-- 月份导航 -->
            <div class="month-navigation">
                <button class="nav-btn prev-month" onclick="prevMonth()">
                    <span>↑</span>
                </button>
                <div class="current-month" onclick="showYearSelector()">
                    <span id="currentMonthDisplay">2025年07月</span>
                    <span class="month-dropdown">▼</span>
                </div>
                <button class="nav-btn next-month" onclick="nextMonth()">
                    <span>↓</span>
                </button>
            </div>

            <!-- 年份选择器 -->
            <div id="yearSelector" class="year-selector" style="display: none;">
                <div class="year-selector-header">
                    <button class="year-nav-btn" onclick="changeYearRange(-10)">‹‹</button>
                    <span id="yearRangeDisplay">2020-2029</span>
                    <button class="year-nav-btn" onclick="changeYearRange(10)">››</button>
                </div>
                <div class="year-grid" id="yearGrid">
                    <!-- 年份选项将通过JavaScript生成 -->
                </div>
                <div class="year-selector-footer">
                    <button class="year-btn" onclick="hideYearSelector()">返回</button>
                </div>
            </div>
            
            <!-- 日历网格 -->
            <div class="calendar-container">
                <!-- 星期标题 -->
                <div class="week-header">
                    <div class="week-day">一</div>
                    <div class="week-day">二</div>
                    <div class="week-day">三</div>
                    <div class="week-day">四</div>
                    <div class="week-day">五</div>
                    <div class="week-day">六</div>
                    <div class="week-day">日</div>
                </div>
                
                <!-- 日历日期网格 -->
                <div class="calendar-grid" id="calendarGrid">
                    <!-- 日期将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 底部按钮 -->
            <div class="calendar-footer">
                <button class="footer-btn clear-btn" onclick="clearDate()">清除</button>
                <button class="footer-btn today-btn" onclick="selectToday()">今天</button>
            </div>
            
            <!-- 收起箭头 -->
            <div class="collapse-arrow" onclick="closeBabyDatePicker()">
                <span>▼</span>
            </div>
        </div>
    </div>

    <!-- 引入农历转换工具 -->
    <script src="./lunar-calendar.js"></script>
    
    <!-- 引入AI模块 -->
    <script src="../../js/modules/ai-service.js?v=1.2"></script>
    <script src="../../js/modules/name-ai.js?v=1.2"></script>
    
    <script src="./script.js?v=1.2"></script>
    
    <script>
        // 添加点击背景关闭模态框的功能
        document.addEventListener('DOMContentLoaded', function() {
            // 宝宝日期选择器
            const babyModal = document.getElementById('babyDateModal');
            if (babyModal) {
                babyModal.addEventListener('click', function(e) {
                    // 只有点击模态框背景（不是内容区域）时才关闭
                    if (e.target === babyModal) {
                        closeBabyDatePicker();
                    }
                });
            }

            // 结果展示弹窗
            const resultModal = document.getElementById('resultModal');
            if (resultModal) {
                resultModal.addEventListener('click', function(e) {
                    // 只有点击模态框背景（不是内容区域）时才关闭
                    if (e.target === resultModal) {
                        closeResultModal();
                    }
                });
            }
        });
    </script>
</body>
</html> 