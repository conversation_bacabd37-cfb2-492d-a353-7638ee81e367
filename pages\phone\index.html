<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机号测吉凶 - 易海堂算命网</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="../../css/main.css">
    <!-- 订单和支付服务 -->
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <!-- 认证服务 -->
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script src="../../js/modules/auth-service.js"></script>
    <!-- 认证初始化 -->
    <script src="../../js/modules/auth-init.js"></script>
    
    <style>
        /* AI分析区域样式 */
        .ai-analysis-section {
            margin: 30px 0;
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .ai-analysis-section .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .ai-analysis-section h3 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }
        
        .ai-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .ai-icon {
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        /* AI加载动画 */
        .ai-loading {
            text-align: center;
            padding: 40px 20px;
        }
        
        .ai-loading-spinner {
            font-size: 48px;
            animation: aiSpin 3s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes aiSpin {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }
        
        .ai-loading-text {
            font-size: 18px;
            margin-bottom: 25px;
            font-weight: 500;
        }
        
        .ai-loading-steps {
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 300px;
            margin: 0 auto;
        }
        
        .ai-loading-steps .step {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0.5;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .ai-loading-steps .step.active {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .ai-loading-steps .step.completed {
            opacity: 1;
            background: rgba(40, 167, 69, 0.8);
            color: white;
        }
        
        /* AI内容样式 */
        .ai-content {
            line-height: 1.8;
        }
        
        .ai-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border-left: 4px solid rgba(255, 255, 255, 0.5);
        }
        
        .ai-section h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: bold;
            color: #FFD700;
        }
        
        .ai-section p {
            margin: 0;
            line-height: 1.6;
        }
        
        .ai-summary {
            background: rgba(255, 215, 0, 0.2);
            border-left-color: #FFD700;
            font-size: 16px;
            font-weight: 500;
        }
        
        .ai-recommendations {
            background: rgba(40, 167, 69, 0.2);
            border-left-color: #28a745;
        }
        
        .ai-recommendations ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .ai-recommendations li {
            margin: 8px 0;
        }
        
        .ai-score-display {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 15px 0;
        }
        
        .ai-score-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        .ai-score-info {
            flex: 1;
        }
        
        .ai-score-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 5px;
        }
        
        .ai-score-desc {
            font-size: 16px;
            font-weight: 500;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .ai-analysis-section {
                margin: 20px 0;
                padding: 20px;
            }
            
            .ai-analysis-section .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .ai-badge {
                align-self: flex-end;
            }
            
            .ai-loading-steps {
                max-width: 100%;
            }
            
            .ai-section {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 动画背景 -->
    <canvas id="phoneCanvas" class="phone-canvas"></canvas>
    
    <!-- 返回按钮 -->
    <div class="back-button" onclick="window.history.back()">
        <span class="back-icon">←</span>
        <span class="back-text">返回</span>
    </div>

    <!-- 页面头部 -->
    <header class="phone-header">
        <div class="header-content">
            <h1 class="header-title">📱 手机号测吉凶</h1>
            <p class="header-subtitle">数字能量学分析，揭示手机号码的神秘力量</p>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-container">
        <!-- 手机号输入区域 -->
        <section class="phone-input" id="phoneInput">
            <div class="section-header">
                <h2>🔢 输入您的手机号码</h2>
                <p>请输入11位手机号码进行数字能量分析</p>
            </div>
            
            <form class="phone-form" id="phoneForm">
                <div class="input-group">
                    <label for="phoneNumber">
                        <span class="label-icon">📲</span>
                        <span class="label-text">手机号码</span>
                    </label>
                    <div class="phone-input-wrapper">
                        <span class="country-code">+86</span>
                        <input 
                            type="tel" 
                            id="phoneNumber" 
                            name="phoneNumber" 
                            placeholder="请输入11位手机号码" 
                            maxlength="11"
                            pattern="[0-9]{11}"
                            required>
                    </div>
                    <div class="input-hint">
                        <span class="hint-text">支持移动、联通、电信号码</span>
                    </div>
                </div>

                <div class="example-numbers">
                    <h3>📋 示例号码</h3>
                    <div class="example-list">
                        <div class="example-item" onclick="useExample('13888888888')">
                            <span class="example-number">138****8888</span>
                            <span class="example-desc">吉祥号码</span>
                        </div>
                        <div class="example-item" onclick="useExample('15666666666')">
                            <span class="example-number">156****6666</span>
                            <span class="example-desc">财运号码</span>
                        </div>
                        <div class="example-item" onclick="useExample('18999999999')">
                            <span class="example-number">189****9999</span>
                            <span class="example-desc">贵人号码</span>
                        </div>
                    </div>
                </div>

                <button type="submit" class="submit-btn">
                    <span class="btn-icon">🔮</span>
                    <span class="btn-text">开始测算</span>
                </button>
            </form>
        </section>

        <!-- 分析结果展示区域 -->
        <section class="phone-result" id="phoneResult" style="display: none;">
            <div class="result-header">
                <h2>📊 手机号码分析报告</h2>
                <div class="result-phone-info" id="resultPhoneInfo">
                    <!-- 手机号信息会显示在这里 -->
                </div>
            </div>

            <div class="analysis-content" id="analysisContent">
                <!-- 分析内容会通过JavaScript动态生成 -->
            </div>

            <!-- AI深度分析区域 -->
            <div class="ai-analysis-section" id="aiAnalysisSection" style="display: none;">
                <div class="section-header">
                    <h3>深度分析</h3>
                    <div class="ai-badge">
                        <span class="ai-icon">✨</span>
                        <span id="aiServiceType">易海堂</span>
                    </div>
                </div>
                
                <div class="ai-loading" id="aiLoading" style="display: none;">
                    <div class="ai-loading-spinner">🧠</div>
                    <div class="ai-loading-text">正在深度分析您的手机号码...</div>
                    <div class="ai-loading-steps">
                        <div class="step" id="aiStep1">🔍 分析数字组合</div>
                        <div class="step" id="aiStep2">🎯 预测运势趋势</div>
                        <div class="step" id="aiStep3">💡 生成个性化建议</div>
                    </div>
                </div>
                
                <div class="ai-content" id="aiContent" style="display: none;">
                    <!-- AI分析结果会显示在这里 -->
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn primary" onclick="saveAnalysis()">
                    <span class="btn-icon">💾</span>
                    <span>保存分析</span>
                </button>
                <button class="action-btn secondary" onclick="shareAnalysis()">
                    <span class="btn-icon">📤</span>
                    <span>分享结果</span>
                </button>
                <button class="action-btn tertiary" onclick="resetAnalysis()">
                    <span class="btn-icon">🔄</span>
                    <span>重新分析</span>
                </button>
            </div>
        </section>

        <!-- 数字能量学知识 -->
        <section class="knowledge-section">
            <div class="section-header">
                <h2>📚 数字能量学知识</h2>
                <p>了解数字背后的神秘能量</p>
            </div>
            
            <div class="knowledge-content">
                <div class="knowledge-item">
                    <div class="knowledge-icon">🔥</div>
                    <div class="knowledge-text">
                        <h3>生气磁场</h3>
                        <p>代表积极向上的能量，带来活力和机遇</p>
                        <span class="knowledge-numbers">14, 23, 41, 32, 68, 86, 77, 95</span>
                    </div>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-icon">💰</div>
                    <div class="knowledge-text">
                        <h3>天医磁场</h3>
                        <p>主财运和健康，带来财富和身体康健</p>
                        <span class="knowledge-numbers">13, 31, 24, 42, 69, 96, 78, 87</span>
                    </div>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-icon">⚡</div>
                    <div class="knowledge-text">
                        <h3>延年磁场</h3>
                        <p>代表贵人运和人际关系，助力事业发展</p>
                        <span class="knowledge-numbers">19, 91, 28, 82, 67, 76, 34, 43</span>
                    </div>
                </div>
                <div class="knowledge-item">
                    <div class="knowledge-icon">🎯</div>
                    <div class="knowledge-text">
                        <h3>伏位磁场</h3>
                        <p>稳定平和的能量，适合稳健发展</p>
                        <span class="knowledge-numbers">11, 22, 33, 44, 55, 66, 77, 88, 99</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 改运建议 -->
        <section class="improvement-section">
            <div class="section-header">
                <h2>🔧 号码改运建议</h2>
                <p>提升手机号码能量的实用方法</p>
            </div>
            
            <div class="improvement-content">
                <div class="improvement-item">
                    <div class="improvement-step">1</div>
                    <div class="improvement-text">
                        <h3>选择吉祥尾号</h3>
                        <p>尾号是手机号的能量核心，选择与您八字相配的数字</p>
                    </div>
                </div>
                <div class="improvement-item">
                    <div class="improvement-step">2</div>
                    <div class="improvement-text">
                        <h3>避免凶数组合</h3>
                        <p>避免使用绝命、五鬼、六煞、祸害磁场的数字组合</p>
                    </div>
                </div>
                <div class="improvement-item">
                    <div class="improvement-step">3</div>
                    <div class="improvement-text">
                        <h3>数字能量平衡</h3>
                        <p>好的手机号应该包含多种吉祥磁场，形成能量平衡</p>
                    </div>
                </div>
                <div class="improvement-item">
                    <div class="improvement-step">4</div>
                    <div class="improvement-text">
                        <h3>个人命理匹配</h3>
                        <p>结合个人生辰八字，选择最适合的数字能量组合</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载动画 -->
    <div class="loading-overlay-phone" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在分析您的手机号码...</div>
        </div>
    </div>

    <script src="../../js/modules/ai-config-manager.js"></script>
    <!-- 引入AI模块 -->
    <script src="../../js/modules/ai-service.js?v=1.2"></script>
    <script src="../../js/modules/phone-ai.js?v=1.2"></script>
    
    <script src="script.js?v=1.2"></script>
</body>
</html> 