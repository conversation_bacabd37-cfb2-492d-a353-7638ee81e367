// 生肖运势页面JavaScript功能

// 全局变量
let selectedZodiac = null;
let canvas, ctx;

// AI服务变量
let aiService = null;
let zodiacFortuneAI = null;
let currentUserData = null;
let currentBasicResult = null;

// 生肖数据配置
const zodiacData = {
    rat: { name: '鼠', icon: '🐭', element: '水', recentYears: [1984, 1996, 2008, 2020] },
    ox: { name: '牛', icon: '🐮', element: '土', recentYears: [1985, 1997, 2009, 2021] },
    tiger: { name: '虎', icon: '🐯', element: '木', recentYears: [1986, 1998, 2010, 2022] },
    rabbit: { name: '兔', icon: '🐰', element: '木', recentYears: [1987, 1999, 2011, 2023] },
    dragon: { name: '龙', icon: '🐲', element: '土', recentYears: [1988, 2000, 2012, 2024] },
    snake: { name: '蛇', icon: '🐍', element: '火', recentYears: [1989, 2001, 2013, 2025] },
    horse: { name: '马', icon: '🐴', element: '火', recentYears: [1990, 2002, 2014, 2026] },
    goat: { name: '羊', icon: '🐑', element: '土', recentYears: [1991, 2003, 2015, 2027] },
    monkey: { name: '猴', icon: '🐵', element: '金', recentYears: [1992, 2004, 2016, 2028] },
    rooster: { name: '鸡', icon: '🐔', element: '金', recentYears: [1993, 2005, 2017, 2029] },
    dog: { name: '狗', icon: '🐶', element: '土', recentYears: [1994, 2006, 2018, 2030] },
    pig: { name: '猪', icon: '🐷', element: '水', recentYears: [1995, 2007, 2019, 2031] }
};

// 生肖运势详细数据
const zodiacFortunes = {
    rat: {
        overall: { score: 85, description: '2025年对属鼠人来说是充满机遇的一年。你的智慧和敏锐的洞察力将帮助你在各个领域取得成功。保持积极乐观的态度，勇敢面对挑战。' },
        career: { score: 88, description: '事业运势非常旺盛，适合开展新项目或寻求职业突破。你的创新思维将得到领导和同事的认可。', tips: '多参与团队合作，发挥你的协调能力。' },
        love: { score: 82, description: '感情生活较为顺利，单身者有望遇到心仪的对象。已婚者需要多花时间陪伴家人。', tips: '主动表达你的感情，真诚待人。' },
        wealth: { score: 80, description: '财运稳中有升，正财收入稳定，偏财运也不错。投资需要谨慎，避免盲目跟风。', tips: '建议多元化投资，分散风险。' },
        health: { score: 78, description: '整体健康状况良好，但需要注意劳逸结合。避免过度疲劳，保持规律作息。', tips: '多做有氧运动，保持身心健康。' }
    },
    ox: {
        overall: { score: 88, description: '2025年是属牛人的幸运年，你的勤奋和坚持将得到回报。在各个方面都有不错的发展机会，把握时机很重要。' },
        career: { score: 90, description: '事业发展稳步上升，你的踏实和可靠将为你赢得更多机会。可能会有晋升或加薪的好消息。', tips: '保持你的专业精神，继续学习新技能。' },
        love: { score: 85, description: '感情运势平稳向上，已有伴侣的关系更加稳固。单身者可能通过工作场合遇到合适的人。', tips: '耐心经营感情，真诚对待伴侣。' },
        wealth: { score: 87, description: '财运相当不错，收入稳定增长。适合进行长期投资规划，房产投资也有不错前景。', tips: '制定详细的理财计划，稳健投资。' },
        health: { score: 83, description: '健康运势良好，体质强健。注意饮食营养均衡，避免暴饮暴食。', tips: '坚持规律运动，定期体检。' }
    },
    tiger: {
        overall: { score: 92, description: '2025年对属虎人来说是非常有利的一年。你的勇气和决断力将帮助你抓住重要机遇，实现人生的重大突破。' },
        career: { score: 94, description: '事业运势极佳，有望实现重大突破。你的领导才能将得到充分发挥，可能会承担更重要的职责。', tips: '勇于接受挑战，发挥你的领导优势。' },
        love: { score: 88, description: '桃花运旺盛，感情生活多姿多彩。单身者容易遇到心动的人，已婚者感情甜蜜。', tips: '保持自信魅力，但也要专一专情。' },
        wealth: { score: 89, description: '财运亨通，多方面收入增加。投资运势不错，但要注意分散风险。', tips: '把握投资机会，但避免过于冒险。' },
        health: { score: 85, description: '精力充沛，活力十足。注意控制情绪，避免因为冲动影响健康。', tips: '适度运动，保持情绪稳定。' }
    },
    rabbit: {
        overall: { score: 80, description: '2025年对属兔人来说是平稳发展的一年。虽然没有大起大落，但稳中求进的策略会带来不错的收获。' },
        career: { score: 78, description: '工作运势平稳，适合巩固现有成果。人际关系和谐，容易得到贵人相助。', tips: '维护好人际关系，稳扎稳打。' },
        love: { score: 84, description: '感情运势温和，关系和谐稳定。适合考虑结婚或生子等人生大事。', tips: '用心经营感情，创造温馨氛围。' },
        wealth: { score: 76, description: '财运平稳，正财收入稳定。投资宜保守，避免高风险项目。', tips: '以稳健理财为主，积累财富。' },
        health: { score: 82, description: '健康状况良好，心情愉悦。注意肠胃保养，饮食要规律。', tips: '保持良好作息，注意饮食健康。' }
    },
    dragon: {
        overall: { score: 91, description: '2025年是属龙人的大运之年。你的雄心壮志将得到实现的机会，各方面都有望取得突出成就。' },
        career: { score: 93, description: '事业运势强劲，有望获得重要突破。你的才华和能力将得到充分认可。', tips: '抓住机遇，展现你的实力和魄力。' },
        love: { score: 87, description: '感情运势良好，魅力四射容易吸引异性。已有伴侣的关系更加深厚。', tips: '保持自信，但也要体贴对方。' },
        wealth: { score: 90, description: '财运极佳，收入大幅增长。投资眼光独到，容易获得丰厚回报。', tips: '把握投资良机，但要理性分析。' },
        health: { score: 84, description: '体力充沛，精神饱满。注意不要过度劳累，保持工作生活平衡。', tips: '合理安排时间，注意休息。' }
    },
    snake: {
        overall: { score: 95, description: '2025年是属蛇人的本命年，运势极为旺盛。你的智慧和直觉将引导你走向成功，这是实现人生目标的绝佳时机。' },
        career: { score: 96, description: '事业运势达到巅峰，有望实现职业生涯的重大飞跃。你的洞察力和判断力将帮助你做出正确决策。', tips: '相信你的直觉，勇敢追求目标。' },
        love: { score: 92, description: '感情运势非常旺盛，本命年反而带来感情上的大收获。单身者易遇真爱，已婚者感情升温。', tips: '珍惜感情机会，真诚对待爱情。' },
        wealth: { score: 94, description: '财运爆发，多渠道收入增长。投资理财都有不错收获，偏财运也很旺。', tips: '抓住赚钱机会，但要合理规划。' },
        health: { score: 88, description: '本命年需要特别注意健康，但整体运势很好。保持积极心态，注意养生。', tips: '定期体检，保持健康生活方式。' }
    },
    horse: {
        overall: { score: 86, description: '2025年对属马人来说是活力四射的一年。你的热情和行动力将为你带来很多机会，适合大胆尝试新事物。' },
        career: { score: 88, description: '工作运势良好，你的积极态度和执行力将得到认可。适合拓展新业务或寻求新发展。', tips: '发挥你的行动优势，勇于创新。' },
        love: { score: 84, description: '感情生活充满激情，容易产生浪漫的邂逅。但要注意感情的稳定性。', tips: '保持热情，但也要专一。' },
        wealth: { score: 82, description: '财运不错，收入稳步增长。适合投资具有成长性的项目。', tips: '选择有潜力的投资，长期持有。' },
        health: { score: 80, description: '精力旺盛，但要注意不要过度消耗。适度运动，保持身体活力。', tips: '合理安排活动，避免过度疲劳。' }
    },
    goat: {
        overall: { score: 83, description: '2025年对属羊人来说是温和发展的一年。你的温柔和善良将为你带来人缘和机会，适合合作发展。' },
        career: { score: 81, description: '工作运势平稳，人际关系和谐。适合团队合作，你的协调能力将发挥重要作用。', tips: '善用人际关系，发挥团队优势。' },
        love: { score: 86, description: '感情运势温馨，关系和谐美满。适合步入婚姻殿堂或考虑生育。', tips: '珍惜感情，创造浪漫时光。' },
        wealth: { score: 79, description: '财运平稳，收入稳定。适合保守投资，避免高风险项目。', tips: '稳健理财，积少成多。' },
        health: { score: 84, description: '健康运势良好，心情愉悦。注意情绪管理，保持内心平静。', tips: '保持乐观心态，适度运动。' }
    },
    monkey: {
        overall: { score: 89, description: '2025年对属猴人来说是充满变化和机遇的一年。你的机智和适应能力将帮助你在变化中找到新的发展方向。' },
        career: { score: 91, description: '事业运势很好，你的聪明才智将得到充分发挥。适合接受新挑战，开拓新领域。', tips: '保持学习精神，适应变化。' },
        love: { score: 85, description: '感情生活有趣多彩，你的幽默感很容易吸引异性。但要注意感情的深度。', tips: '真诚对待感情，不要只停留在表面。' },
        wealth: { score: 87, description: '财运灵活多变，有多种赚钱机会。投资要灵活应变，抓住时机。', tips: '保持敏锐嗅觉，及时调整策略。' },
        health: { score: 83, description: '健康状况良好，但要注意大脑休息。避免过度用脑，保持身心平衡。', tips: '劳逸结合，保持充足睡眠。' }
    },
    rooster: {
        overall: { score: 87, description: '2025年对属鸡人来说是展现才华的一年。你的勤奋和完美主义将帮助你在各个领域取得优异成绩。' },
        career: { score: 89, description: '工作运势优秀，你的专业能力和责任心将得到充分认可。有望获得重要晋升。', tips: '坚持高标准，展现专业精神。' },
        love: { score: 83, description: '感情运势稳定，关系真诚深厚。你的忠诚和体贴将为感情加分。', tips: '保持诚信，用心经营感情。' },
        wealth: { score: 85, description: '财运稳健，收入持续增长。理财规划完善，财富积累稳步增加。', tips: '坚持理财计划，稳健投资。' },
        health: { score: 81, description: '健康运势不错，但要注意不要过于苛求完美而给自己压力。', tips: '适度放松，保持心理健康。' }
    },
    dog: {
        overall: { score: 84, description: '2025年对属狗人来说是忠诚得回报的一年。你的正直和可靠将为你赢得更多人的信任和机会。' },
        career: { score: 86, description: '工作运势良好，你的忠诚和努力将得到认可。团队合作能力强，容易获得支持。', tips: '保持正直品格，发挥团队精神。' },
        love: { score: 87, description: '感情运势温暖，关系忠诚稳定。你的真诚将深深打动对方。', tips: '保持真诚，用行动证明爱意。' },
        wealth: { score: 78, description: '财运平稳，收入稳定。适合长期投资，避免投机行为。', tips: '坚持长期投资理念，稳步增值。' },
        health: { score: 85, description: '健康运势良好，体质强健。注意情绪管理，避免过度担忧。', tips: '保持乐观态度，定期锻炼。' }
    },
    pig: {
        overall: { score: 90, description: '2025年对属猪人来说是丰收的一年。你的善良和乐观将为你带来福气，各方面都有不错的收获。' },
        career: { score: 88, description: '事业运势良好，你的诚实和勤奋将得到回报。有望在现有岗位上取得重要成就。', tips: '保持诚实品格，踏实工作。' },
        love: { score: 91, description: '感情运势极佳，桃花朵朵开。你的温和善良很容易获得异性好感。', tips: '保持真诚善良，珍惜每一份感情。' },
        wealth: { score: 86, description: '财运亨通，有多方面收入来源。偏财运也不错，但要注意合理消费。', tips: '开源节流，合理规划财务。' },
        health: { score: 88, description: '健康运势很好，心情愉悦。注意饮食控制，避免暴饮暴食。', tips: '保持良好生活习惯，适度运动。' }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    window.unifiedAuthService = new UnifiedAuthService();
    window.memberService = new MemberService();
    window.orderPaymentManager = new OrderPaymentManager();
    initializeCanvas();
    setupEventListeners();
    generateYearOptions();
    animateCanvas();
    initializeAIServices();
    initializeBannerCarousel();
});

// 初始化AI服务
function initializeAIServices() {
    try {
        console.log('🤖 初始化生肖运势AI服务...');
        
        // 检查必要的类是否存在
        if (typeof ZodiacFortuneAI === 'undefined') {
            console.error('❌ ZodiacFortuneAI 类未找到，请检查模块是否正确加载');
            return;
        }
        
        if (typeof AIService === 'undefined' && window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
            console.error('❌ AIService 类未找到，切换到离线模式');
            window.AI_CONFIG.SERVICE_TYPE = 'offline';
        }
        
        // 初始化AI服务
        if (window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
            try {
                aiService = new AIService(window.AI_CONFIG);
                window.aiService = aiService;
                console.log('✅ AI服务初始化成功:', window.AI_CONFIG.SERVICE_TYPE);
            } catch (error) {
                console.warn('⚠️ AI服务初始化失败，切换到离线模式:', error);
                window.AI_CONFIG.SERVICE_TYPE = 'offline';
            }
        }
        
        // 初始化生肖运势AI
        zodiacFortuneAI = new ZodiacFortuneAI();
        console.log('✅ 生肖运势AI模块初始化成功');
        
        // 更新AI服务类型显示
        updateAIServiceDisplay();
        
    } catch (error) {
        console.error('❌ AI服务初始化失败:', error);
        // 继续使用基础功能，不影响主要功能
    }
}

// 更新AI服务类型显示
function updateAIServiceDisplay() {
    const serviceTypeElement = document.getElementById('aiServiceType');
    if (serviceTypeElement) {
        serviceTypeElement.textContent = "易海堂";
    }
}

// 初始化Canvas动画
function initializeCanvas() {
    canvas = document.getElementById('zodiacCanvas');
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    resizeCanvas();
    
    window.addEventListener('resize', resizeCanvas);
}

// 调整Canvas尺寸
function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
}

// Canvas动画
function animateCanvas() {
    if (!ctx) return;
    
    const particles = [];
    const zodiacSymbols = ['🐭', '🐮', '🐯', '🐰', '🐲', '🐍', '🐴', '🐑', '🐵', '🐔', '🐶', '🐷'];
    
    // 创建粒子
    for (let i = 0; i < 20; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            symbol: zodiacSymbols[Math.floor(Math.random() * zodiacSymbols.length)],
            opacity: Math.random() * 0.5 + 0.2,
            size: Math.random() * 20 + 15
        });
    }
    
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        particles.forEach(particle => {
            // 更新位置
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // 边界检测
            if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            
            // 绘制粒子
            ctx.globalAlpha = particle.opacity;
            ctx.font = `${particle.size}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(particle.symbol, particle.x, particle.y);
        });
        
        ctx.globalAlpha = 1;
        requestAnimationFrame(animate);
    }
    
    animate();
}

// 设置事件监听器
function setupEventListeners() {
    // 生肖卡片点击事件
    const zodiacCards = document.querySelectorAll('.zodiac-card');
    zodiacCards.forEach(card => {
        card.addEventListener('click', function() {
            selectZodiac(this.dataset.zodiac);
        });
    });

    // 表单提交事件
    const form = document.getElementById('zodiacForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
}

// 初始化Banner轮播图
function initializeBannerCarousel() {
    const carouselWrapper = document.getElementById('carouselWrapper');
    const slides = document.querySelectorAll('.carousel-slide');
    const indicators = document.querySelectorAll('.indicator');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (!carouselWrapper || slides.length === 0) {
        console.log('轮播图元素未找到，跳过初始化');
        return;
    }

    let currentSlide = 0;
    let autoPlayInterval;

    // 显示指定幻灯片
    function showSlide(index) {
        // 移除所有active类
        slides.forEach(slide => slide.classList.remove('active'));
        indicators.forEach(indicator => indicator.classList.remove('active'));

        // 添加active类到当前幻灯片
        if (slides[index]) {
            slides[index].classList.add('active');
        }
        if (indicators[index]) {
            indicators[index].classList.add('active');
        }

        currentSlide = index;
    }

    // 下一张幻灯片
    function nextSlide() {
        const next = (currentSlide + 1) % slides.length;
        showSlide(next);
    }

    // 上一张幻灯片
    function prevSlide() {
        const prev = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prev);
    }

    // 开始自动播放
    function startAutoPlay() {
        autoPlayInterval = setInterval(nextSlide, 5000); // 5秒切换一次
    }

    // 停止自动播放
    function stopAutoPlay() {
        if (autoPlayInterval) {
            clearInterval(autoPlayInterval);
        }
    }

    // 绑定控制按钮事件
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            stopAutoPlay();
            nextSlide();
            startAutoPlay();
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            stopAutoPlay();
            prevSlide();
            startAutoPlay();
        });
    }

    // 绑定指示器点击事件
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            stopAutoPlay();
            showSlide(index);
            startAutoPlay();
        });
    });

    // 鼠标悬停时停止自动播放
    carouselWrapper.addEventListener('mouseenter', stopAutoPlay);
    carouselWrapper.addEventListener('mouseleave', startAutoPlay);

    // 触摸滑动支持（移动端）
    let touchStartX = 0;
    let touchEndX = 0;

    carouselWrapper.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
    });

    carouselWrapper.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            stopAutoPlay();
            if (diff > 0) {
                nextSlide(); // 向左滑动，显示下一张
            } else {
                prevSlide(); // 向右滑动，显示上一张
            }
            startAutoPlay();
        }
    }

    // 启动自动播放
    startAutoPlay();

    console.log('✅ Banner轮播图初始化成功');
}

// 生成年份选项
function generateYearOptions(selectedZodiacKey = null) {
    const yearSelect = document.getElementById('birthYear');
    if (!yearSelect) return;
    
    // 清空现有选项
    yearSelect.innerHTML = '<option value="">请选择出生年份</option>';
    
    if (selectedZodiacKey) {
        // 如果选择了生肖，只显示对应年份
        const years = generateZodiacYears(selectedZodiacKey);
        years.forEach(year => {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year + '年';
            yearSelect.appendChild(option);
        });
    } else {
        // 如果没有选择生肖，显示所有年份
        const currentYear = new Date().getFullYear();
        for (let year = currentYear; year >= 1940; year--) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year + '年';
            yearSelect.appendChild(option);
        }
    }
}

// 生成指定生肖的所有年份
function generateZodiacYears(zodiacKey) {
    // 十二生肖的基础年份（以鼠年为起点）
    const baseYear = 1972; // 1972年是鼠年
    const zodiacOrder = ['rat', 'ox', 'tiger', 'rabbit', 'dragon', 'snake', 'horse', 'goat', 'monkey', 'rooster', 'dog', 'pig'];
    
    const zodiacIndex = zodiacOrder.indexOf(zodiacKey);
    if (zodiacIndex === -1) return [];
    
    const years = [];
    const currentYear = new Date().getFullYear();
    
    // 从1940年开始，到当前年份+12年（一个轮回）
    const startYear = 1940;
    const endYear = currentYear + 12;
    
    // 计算该生肖的起始年份
    let zodiacStartYear = baseYear + zodiacIndex;
    
    // 往前推到1940年之后的第一个该生肖年份
    while (zodiacStartYear > startYear) {
        zodiacStartYear -= 12;
    }
    while (zodiacStartYear < startYear) {
        zodiacStartYear += 12;
    }
    
    // 生成所有该生肖的年份
    for (let year = zodiacStartYear; year <= endYear; year += 12) {
        if (year >= startYear && year <= currentYear + 12) {
            years.push(year);
        }
    }
    
    // 按从新到老排序
    return years.sort((a, b) => b - a);
}

// 选择生肖
function selectZodiac(zodiacKey) {
    // 清除之前的选择
    document.querySelectorAll('.zodiac-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // 选中当前生肖
    const selectedCard = document.querySelector(`[data-zodiac="${zodiacKey}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');
    }
    
    selectedZodiac = zodiacKey;
    
    // 显示详细信息输入区域
    showInfoInput();
    
    // 更新选中的生肖显示
    updateSelectedZodiacDisplay();
    
    // 根据选中的生肖更新年份选项
    generateYearOptions(zodiacKey);
    
    // 更新年份提示
    updateYearHint(zodiacKey);
    
    // 滚动到输入区域
    setTimeout(() => {
        document.getElementById('infoInput').scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }, 500);
}

// 显示信息输入区域
function showInfoInput() {
    const infoInput = document.getElementById('infoInput');
    if (infoInput) {
        infoInput.style.display = 'block';
        setTimeout(() => {
            infoInput.classList.add('show');
            if (selectedZodiac) {
                infoInput.classList.add('has-zodiac');
            }
        }, 100);
    }
}

// 更新选中的生肖显示
function updateSelectedZodiacDisplay() {
    const container = document.getElementById('selectedZodiac');
    if (!container || !selectedZodiac) return;
    
    const zodiac = zodiacData[selectedZodiac];
    const years = zodiac.recentYears.join(', ');
    
    container.innerHTML = `
        <div class="zodiac-icon">${zodiac.icon}</div>
        <div class="zodiac-info">
            <h3>生肖${zodiac.name}</h3>
            <p>五行属${zodiac.element} • 近年：${years}</p>
        </div>
    `;
}

// 更新年份提示
function updateYearHint(zodiacKey) {
    const hintElement = document.getElementById('yearHint');
    const hintTextElement = document.getElementById('hintText');
    const infoInput = document.getElementById('infoInput');
    
    if (!hintElement || !hintTextElement) return;
    
    if (zodiacKey) {
        const zodiac = zodiacData[zodiacKey];
        const availableYears = generateZodiacYears(zodiacKey);
        
        // 更新样式
        hintElement.style.background = 'rgba(40, 167, 69, 0.2)';
        hintElement.style.borderColor = 'rgba(40, 167, 69, 0.4)';
        hintElement.style.color = 'rgba(255, 255, 255, 0.9)';
        
        // 更新文本
        hintTextElement.innerHTML = `✅ 已筛选生肖${zodiac.name}的${availableYears.length}个年份`;
        
        // 添加样式类
        if (infoInput) {
            infoInput.classList.add('has-zodiac');
        }
    } else {
        // 恢复默认样式
        hintElement.style.background = 'rgba(255, 255, 255, 0.1)';
        hintElement.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        hintElement.style.color = 'rgba(255, 255, 255, 0.8)';
        
        // 恢复默认文本
        hintTextElement.innerHTML = '选择生肖后将只显示对应年份';
        
        // 移除样式类
        if (infoInput) {
            infoInput.classList.remove('has-zodiac');
        }
    }
}

// 处理表单提交
function handleFormSubmit(e) {
    e.preventDefault();
    
    if (!selectedZodiac) {
        alert('请先选择您的生肖');
        return;
    }
    
    const formData = new FormData(e.target);
    const userData = {
        zodiac: selectedZodiac,
        birthYear: formData.get('birthYear'),
        birthMonth: formData.get('birthMonth'),
        gender: formData.get('gender')
    };
    
    // 验证表单数据
    if (!userData.birthYear || !userData.birthMonth || !userData.gender) {
        alert('请完整填写所有信息');
        return;
    }
    
    // 服务配置
    const serviceConfig = {
        type: 'zodiac-fortune',
        name: '生肖运势',
        price: 8.8,
        description: '2025年生肖运势详细分析'
    };

    // 创建订单并支付
    try {
        window.orderPaymentManager.createOrderAndPay(
            serviceConfig,
            userData,
            // 支付成功回调
            async function(order, paymentResult) {
                console.log('支付成功，开始生肖运势分析');
                await performZodiacAnalysis(userData);
            },
            // 取消支付回调
            function(order) {
                console.log('用户取消支付');
            }
        );
    } catch (error) {
        console.error('订单创建失败:', error);
        alert('创建订单失败，请稍后重试');
    }
}

// 执行生肖运势分析
async function performZodiacAnalysis(userData) {
    try {
        // 显示加载动画
        showLoading();
        
        // 模拟分析过程
        setTimeout(() => {
            const result = calculateFortune(userData);
            displayResult(result);
            hideLoading();
        }, 2000);
    } catch (error) {
        console.error('生肖运势分析失败:', error);
        hideLoading();
        alert('分析失败，请稍后重试');
    }
}

// 计算运势 - 返回标准化JSON格式
function calculateFortune(userData) {
    const baseFortune = zodiacFortunes[userData.zodiac];
    if (!baseFortune) return null;

    // 根据年份、月份、性别等因素调整运势
    const ageAdjustment = calculateAgeAdjustment(userData.birthYear);
    const monthAdjustment = calculateMonthAdjustment(userData.birthMonth);
    const genderAdjustment = calculateGenderAdjustment(userData.gender, userData.zodiac);

    // 应用调整因子
    const adjustedFortune = {
        overall: adjustScore(baseFortune.overall, ageAdjustment + monthAdjustment + genderAdjustment),
        career: adjustScore(baseFortune.career, ageAdjustment + genderAdjustment),
        love: adjustScore(baseFortune.love, monthAdjustment + genderAdjustment),
        wealth: adjustScore(baseFortune.wealth, ageAdjustment + monthAdjustment),
        health: adjustScore(baseFortune.health, ageAdjustment)
    };

    // 生成标准化JSON格式结果
    const zodiacInfo = zodiacData[userData.zodiac];
    const currentYear = new Date().getFullYear();
    const age = currentYear - parseInt(userData.birthYear);

    return {
        // 基本信息
        basic: {
            analysisId: generateAnalysisId(),
            timestamp: new Date().toISOString(),
            analysisYear: currentYear,
            version: "1.0"
        },

        // 用户信息
        user: {
            zodiac: userData.zodiac,
            zodiacName: zodiacInfo.name,
            zodiacIcon: zodiacInfo.icon,
            element: zodiacInfo.element,
            birthYear: parseInt(userData.birthYear),
            birthMonth: parseInt(userData.birthMonth),
            age: age,
            gender: userData.gender,
            genderText: userData.gender === 'male' ? '男性' : '女性'
        },

        // 运势评分
        scores: {
            overall: adjustedFortune.overall.score,
            career: adjustedFortune.career.score,
            love: adjustedFortune.love.score,
            wealth: adjustedFortune.wealth.score,
            health: adjustedFortune.health.score
        },

        // 详细运势分析
        fortune: {
            overall: {
                score: adjustedFortune.overall.score,
                level: getFortuneLevel(adjustedFortune.overall.score),
                description: adjustedFortune.overall.description,
                stars: generateStars(adjustedFortune.overall.score)
            },
            career: {
                score: adjustedFortune.career.score,
                level: getFortuneLevel(adjustedFortune.career.score),
                description: adjustedFortune.career.description,
                tips: adjustedFortune.career.tips,
                stars: generateStars(adjustedFortune.career.score)
            },
            love: {
                score: adjustedFortune.love.score,
                level: getFortuneLevel(adjustedFortune.love.score),
                description: adjustedFortune.love.description,
                tips: adjustedFortune.love.tips,
                stars: generateStars(adjustedFortune.love.score)
            },
            wealth: {
                score: adjustedFortune.wealth.score,
                level: getFortuneLevel(adjustedFortune.wealth.score),
                description: adjustedFortune.wealth.description,
                tips: adjustedFortune.wealth.tips,
                stars: generateStars(adjustedFortune.wealth.score)
            },
            health: {
                score: adjustedFortune.health.score,
                level: getFortuneLevel(adjustedFortune.health.score),
                description: adjustedFortune.health.description,
                tips: adjustedFortune.health.tips,
                stars: generateStars(adjustedFortune.health.score)
            }
        },

        // 幸运元素
        lucky: {
            numbers: generateLuckyNumbers(userData),
            colors: generateLuckyColors(userData.zodiac),
            directions: generateLuckyDirections(userData.zodiac)
        },

        // AI分析结果（初始为空，后续填充）
        aiAnalysis: null,

        // 分析方法
        method: 'basic'
    };
}

// 年龄调整因子
function calculateAgeAdjustment(birthYear) {
    const age = 2025 - parseInt(birthYear);
    if (age < 25) return 2;
    if (age < 35) return 5;
    if (age < 50) return 3;
    if (age < 65) return 1;
    return -2;
}

// 月份调整因子
function calculateMonthAdjustment(birthMonth) {
    const month = parseInt(birthMonth);
    // 春季生人运势较好
    if (month >= 3 && month <= 5) return 3;
    // 秋季生人财运较好
    if (month >= 9 && month <= 11) return 2;
    // 夏季生人感情运好
    if (month >= 6 && month <= 8) return 1;
    // 冬季生人平稳
    return 0;
}

// 性别调整因子
function calculateGenderAdjustment(gender, zodiac) {
    // 某些生肖的性别特点
    const maleAdvantageZodiacs = ['tiger', 'dragon', 'horse', 'monkey'];
    const femaleAdvantageZodiacs = ['rabbit', 'snake', 'goat', 'pig'];
    
    if (gender === 'male' && maleAdvantageZodiacs.includes(zodiac)) return 3;
    if (gender === 'female' && femaleAdvantageZodiacs.includes(zodiac)) return 3;
    return 1;
}

// 调整分数
function adjustScore(original, adjustment) {
    const newScore = Math.max(0, Math.min(100, original.score + adjustment));
    return {
        ...original,
        score: newScore
    };
}

// 生成幸运数字
function generateLuckyNumbers(userData) {
    const baseNumbers = [1, 3, 5, 7, 9, 11, 13, 15];
    const birth = parseInt(userData.birthMonth) + parseInt(userData.birthYear.slice(-1));
    return baseNumbers.map(num => (num + birth) % 100).slice(0, 3).sort((a, b) => a - b);
}

// 生成幸运颜色
function generateLuckyColors(zodiac) {
    const colors = {
        rat: ['蓝色', '黑色', '白色'],
        ox: ['黄色', '橙色', '棕色'],
        tiger: ['绿色', '青色', '蓝色'],
        rabbit: ['绿色', '青色', '蓝色'],
        dragon: ['黄色', '橙色', '棕色'],
        snake: ['红色', '紫色', '粉色'],
        horse: ['红色', '紫色', '粉色'],
        goat: ['黄色', '橙色', '棕色'],
        monkey: ['白色', '金色', '银色'],
        rooster: ['白色', '金色', '银色'],
        dog: ['黄色', '橙色', '棕色'],
        pig: ['蓝色', '黑色', '白色']
    };
    return colors[zodiac] || ['红色', '金色', '紫色'];
}

// 生成幸运方位
function generateLuckyDirections(zodiac) {
    const directions = {
        rat: ['北方', '西方'],
        ox: ['东北', '西南'],
        tiger: ['东方', '南方'],
        rabbit: ['东方', '南方'],
        dragon: ['东南', '西北'],
        snake: ['南方', '西南'],
        horse: ['南方', '西南'],
        goat: ['西南', '东北'],
        monkey: ['西方', '西北'],
        rooster: ['西方', '西北'],
        dog: ['西北', '东南'],
        pig: ['北方', '东方']
    };
    return directions[zodiac] || ['正南', '正北'];
}

// 显示结果
function displayResult(result) {
    if (!result) return;

    // 保存数据供AI分析使用（适配新格式）
    currentUserData = result.user;
    currentBasicResult = result;

    // 隐藏输入区域
    document.getElementById('zodiacSelection').style.display = 'none';
    document.getElementById('infoInput').style.display = 'none';

    // 显示结果区域
    const resultSection = document.getElementById('fortuneResult');
    resultSection.style.display = 'block';

    // 更新结果信息
    updateResultInfo(result);

    // 更新运势卡片显示
    updateFortuneCards(result);

    // 显示动画
    setTimeout(() => {
        resultSection.classList.add('show');
        resultSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);

    // 保存到本地存储
    saveResultToStorage(result);

    // 启动AI深度分析（传递适配的数据格式）
    const legacyUserData = {
        zodiac: result.user.zodiac,
        birthYear: result.user.birthYear.toString(),
        birthMonth: result.user.birthMonth.toString(),
        gender: result.user.gender
    };
    const legacyFortune = {
        overall: result.fortune.overall,
        career: result.fortune.career,
        love: result.fortune.love,
        wealth: result.fortune.wealth,
        health: result.fortune.health
    };
    startAIAnalysis(legacyUserData, legacyFortune);
}

// 更新结果信息
function updateResultInfo(result) {
    const container = document.getElementById('resultZodiacInfo');
    if (!container) return;

    const birthInfo = `${result.user.birthYear}年${result.user.birthMonth}月生`;

    container.innerHTML = `
        <div class="zodiac-icon">${result.user.zodiacIcon}</div>
        <div class="zodiac-details">
            <h3>${result.user.genderText} • 生肖${result.user.zodiacName}</h3>
            <p>${birthInfo} • 五行属${result.user.element} • ${result.user.age}岁</p>
        </div>
    `;
}

// 更新运势卡片显示
function updateFortuneCards(result) {
    // 更新各个运势卡片的分数和描述
    const fortuneTypes = ['overall', 'career', 'love', 'wealth', 'health'];

    fortuneTypes.forEach(type => {
        const fortune = result.fortune[type];
        if (!fortune) return;

        // 更新分数显示
        const scoreElement = document.querySelector(`[data-fortune="${type}"] .fortune-score`);
        if (scoreElement) {
            scoreElement.textContent = fortune.score;
        }

        // 更新星级显示
        const starsElement = document.querySelector(`[data-fortune="${type}"] .fortune-stars`);
        if (starsElement) {
            starsElement.textContent = fortune.stars;
        }

        // 更新描述
        const descElement = document.querySelector(`[data-fortune="${type}"] .fortune-description`);
        if (descElement) {
            descElement.textContent = fortune.description;
        }

        // 更新建议
        const tipsElement = document.querySelector(`[data-fortune="${type}"] .fortune-tips`);
        if (tipsElement && fortune.tips) {
            tipsElement.textContent = fortune.tips;
        }
    });

    // 更新幸运元素
    updateLuckyElements(result.lucky);
}

// 更新幸运元素显示
function updateLuckyElements(lucky) {
    // 更新幸运数字
    const numbersElement = document.getElementById('luckyNumbers');
    if (numbersElement && lucky.numbers) {
        numbersElement.textContent = lucky.numbers.join(', ');
    }

    // 更新幸运颜色
    const colorsElement = document.getElementById('luckyColors');
    if (colorsElement && lucky.colors) {
        colorsElement.textContent = lucky.colors.join(', ');
    }

    // 更新幸运方位
    const directionsElement = document.getElementById('luckyDirections');
    if (directionsElement && lucky.directions) {
        directionsElement.textContent = lucky.directions.join(', ');
    }
}


// 生成分析ID
function generateAnalysisId() {
    return 'zodiac_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// 获取运势等级
function getFortuneLevel(score) {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 70) return 'fair';
    if (score >= 60) return 'average';
    return 'poor';
}

// 获取运势等级文本
function getFortuneLevelText(level) {
    const levelTexts = {
        'excellent': '极佳',
        'good': '良好',
        'fair': '一般',
        'average': '平平',
        'poor': '较差'
    };
    return levelTexts[level] || '未知';
}

// 生成星级评分
function generateStars(score) {
    const fullStars = Math.floor(score / 20);
    const halfStar = (score % 20) >= 10 ? 1 : 0;
    const emptyStars = 5 - fullStars - halfStar;

    return '★'.repeat(fullStars) +
           (halfStar ? '☆' : '') +
           '☆'.repeat(emptyStars);
}

// 显示加载动画
function showLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'flex';
    }
}

// 隐藏加载动画
function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// 保存结果到本地存储
function saveResultToStorage(result) {
    const savedResults = JSON.parse(localStorage.getItem('zodiacResults') || '[]');

    // 确保结果包含完整的基本信息
    const newResult = {
        ...result,
        basic: {
            ...result.basic,
            savedAt: new Date().toISOString(),
            id: result.basic.analysisId || Date.now()
        }
    };

    savedResults.unshift(newResult);
    // 只保留最近10次记录
    if (savedResults.length > 10) {
        savedResults.splice(10);
    }

    localStorage.setItem('zodiacResults', JSON.stringify(savedResults));
    console.log('✅ 运势结果已保存到本地存储:', newResult.basic.analysisId);
}

// 保存结果 - 增强的JSON导出功能
function saveResult() {
    const results = JSON.parse(localStorage.getItem('zodiacResults') || '[]');
    if (results.length === 0) {
        alert('没有可保存的结果');
        return;
    }

    const latestResult = results[0];

    // 创建更友好的文件名
    const fileName = `生肖运势_${latestResult.user.zodiacName}_${latestResult.user.birthYear}年${latestResult.user.birthMonth}月_${new Date().toISOString().split('T')[0]}.json`;

    // 创建格式化的JSON内容
    const formattedResult = {
        // 分析基本信息
        analysisInfo: {
            id: latestResult.basic.analysisId,
            timestamp: latestResult.basic.timestamp,
            analysisYear: latestResult.basic.analysisYear,
            version: latestResult.basic.version
        },

        // 用户信息
        userInfo: latestResult.user,

        // 运势评分汇总
        scoresSummary: latestResult.scores,

        // 详细运势分析
        fortuneDetails: latestResult.fortune,

        // 幸运元素
        luckyElements: latestResult.lucky,

        // AI分析结果（如果有）
        aiAnalysis: latestResult.aiAnalysis,

        // 分析方法
        method: latestResult.method
    };

    const blob = new Blob([JSON.stringify(formattedResult, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    alert(`结果已保存到文件: ${fileName}`);
}

// 分享结果
function shareResult() {
    const results = JSON.parse(localStorage.getItem('zodiacResults') || '[]');
    if (results.length === 0) {
        alert('没有可分享的结果');
        return;
    }

    const result = results[0];
    const shareText = `我在易海堂测了2025年生肖运势，我是${result.user.zodiacName}，整体运势${result.scores.overall}分！事业运${result.scores.career}分，感情运${result.scores.love}分，财运${result.scores.wealth}分！`;

    if (navigator.share) {
        navigator.share({
            title: '生肖运势结果',
            text: shareText,
            url: window.location.href
        });
    } else {
        // 复制到剪贴板
        navigator.clipboard.writeText(shareText).then(() => {
            alert('分享内容已复制到剪贴板');
        }).catch(() => {
            alert('分享失败，请手动复制分享内容');
        });
    }
}

// 重新测算
function resetForm() {
    // 重置选择
    selectedZodiac = null;
    document.querySelectorAll('.zodiac-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // 重置表单
    const form = document.getElementById('zodiacForm');
    if (form) {
        form.reset();
    }
    
    // 隐藏所有区域
    document.getElementById('infoInput').style.display = 'none';
    document.getElementById('fortuneResult').style.display = 'none';
    document.getElementById('aiAnalysisSection').style.display = 'none';
    
    // 显示生肖选择
    document.getElementById('zodiacSelection').style.display = 'block';
    
    // 清除AI分析数据
    currentUserData = null;
    currentBasicResult = null;
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// ========== JSON API 接口函数 ==========

// 获取最新的运势分析结果（JSON格式）
function getLatestFortuneResult() {
    const results = JSON.parse(localStorage.getItem('zodiacResults') || '[]');
    if (results.length === 0) {
        return {
            success: false,
            message: '没有可用的运势分析结果',
            data: null
        };
    }

    return {
        success: true,
        message: '获取运势结果成功',
        data: results[0]
    };
}

// 获取所有历史运势分析结果（JSON格式）
function getAllFortuneResults() {
    const results = JSON.parse(localStorage.getItem('zodiacResults') || '[]');

    return {
        success: true,
        message: `获取到${results.length}条历史记录`,
        count: results.length,
        data: results
    };
}

// 根据分析ID获取特定的运势结果
function getFortuneResultById(analysisId) {
    const results = JSON.parse(localStorage.getItem('zodiacResults') || '[]');
    const result = results.find(r => r.basic.analysisId === analysisId);

    if (!result) {
        return {
            success: false,
            message: `未找到ID为${analysisId}的分析结果`,
            data: null
        };
    }

    return {
        success: true,
        message: '获取运势结果成功',
        data: result
    };
}

// 导出运势结果为JSON字符串
function exportFortuneResultAsJSON(analysisId = null) {
    let result;

    if (analysisId) {
        const response = getFortuneResultById(analysisId);
        if (!response.success) {
            return response;
        }
        result = response.data;
    } else {
        const response = getLatestFortuneResult();
        if (!response.success) {
            return response;
        }
        result = response.data;
    }

    return {
        success: true,
        message: '导出JSON成功',
        json: JSON.stringify(result, null, 2),
        data: result
    };
}

// 清空所有运势分析记录
function clearAllFortuneResults() {
    localStorage.removeItem('zodiacResults');
    return {
        success: true,
        message: '所有运势分析记录已清空'
    };
}

// 暴露API到全局作用域，方便外部调用
window.ZodiacFortuneAPI = {
    getLatest: getLatestFortuneResult,
    getAll: getAllFortuneResults,
    getById: getFortuneResultById,
    exportJSON: exportFortuneResultAsJSON,
    clearAll: clearAllFortuneResults
};

// 测试JSON输出功能
function testJSONOutput() {
    const result = getLatestFortuneResult();

    if (!result.success) {
        alert(result.message);
        return;
    }

    // 创建一个模态框显示JSON结果
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        border-radius: 10px;
        padding: 20px;
        max-width: 90%;
        max-height: 90%;
        overflow: auto;
        position: relative;
    `;

    const closeBtn = document.createElement('button');
    closeBtn.textContent = '关闭';
    closeBtn.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        background: #dc3545;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
    `;
    closeBtn.onclick = () => document.body.removeChild(modal);

    const title = document.createElement('h3');
    title.textContent = 'JSON格式运势结果';
    title.style.marginBottom = '15px';

    const jsonDisplay = document.createElement('pre');
    jsonDisplay.style.cssText = `
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow: auto;
        max-height: 400px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
    `;
    jsonDisplay.textContent = JSON.stringify(result.data, null, 2);

    const copyBtn = document.createElement('button');
    copyBtn.textContent = '复制JSON';
    copyBtn.style.cssText = `
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 10px;
    `;
    copyBtn.onclick = () => {
        navigator.clipboard.writeText(JSON.stringify(result.data, null, 2)).then(() => {
            copyBtn.textContent = '已复制!';
            setTimeout(() => copyBtn.textContent = '复制JSON', 2000);
        });
    };

    content.appendChild(closeBtn);
    content.appendChild(title);
    content.appendChild(jsonDisplay);
    content.appendChild(copyBtn);
    modal.appendChild(content);
    document.body.appendChild(modal);

    console.log('🔍 JSON格式运势结果:', result.data);
}

// ========== AI功能相关函数 ==========

// 启动AI深度分析
async function startAIAnalysis(userData, basicFortune) {
    console.log('🚀 启动AI生肖运势分析...');

    // 显示AI分析区域
    const aiSection = document.getElementById('aiAnalysisSection');
    if (aiSection) {
        aiSection.style.display = 'block';

        // 平滑滚动到AI分析区域
        setTimeout(() => {
            aiSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 1000);
    }

    // 检查AI服务是否可用
    if (!zodiacFortuneAI) {
        showAIError('AI分析服务未初始化');
        return;
    }

    try {
        // 显示AI加载动画
        showAILoading();

        // 执行AI分析
        const aiResult = await zodiacFortuneAI.analyzeZodiacFortuneWithAI(userData, basicFortune);

        // 显示AI分析结果
        displayAIResult(aiResult);

        // 更新当前结果的AI分析部分
        if (currentBasicResult) {
            currentBasicResult.aiAnalysis = {
                success: aiResult.success,
                method: aiResult.fortuneAnalysis?.method || 'unknown',
                timestamp: new Date().toISOString(),
                analysis: aiResult.fortuneAnalysis || null
            };

            // 更新本地存储
            saveResultToStorage(currentBasicResult);
            console.log('✅ AI分析结果已合并到主结果中');
        }

    } catch (error) {
        console.error('AI分析失败:', error);
        showAIError(error.message);

        // 即使AI分析失败，也要记录失败信息
        if (currentBasicResult) {
            currentBasicResult.aiAnalysis = {
                success: false,
                method: 'failed',
                timestamp: new Date().toISOString(),
                error: error.message,
                analysis: null
            };
            saveResultToStorage(currentBasicResult);
        }
    }
}

// 显示AI加载动画
function showAILoading() {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';
    
    // 模拟加载步骤
    const steps = ['aiStep1', 'aiStep2', 'aiStep3', 'aiStep4'];
    const statusTexts = [
        '正在分析生肖特质...',
        '正在结合流年趋势...',
        '正在预测运势走向...',
        '正在生成专业建议...'
    ];
    
    let currentStep = 0;
    
    const updateStep = () => {
        // 清除之前的状态
        steps.forEach(stepId => {
            const stepEl = document.getElementById(stepId);
            if (stepEl) {
                stepEl.classList.remove('active', 'completed');
            }
        });
        
        // 标记完成的步骤
        for (let i = 0; i < currentStep; i++) {
            const stepEl = document.getElementById(steps[i]);
            if (stepEl) {
                stepEl.classList.add('completed');
            }
        }
        
        // 标记当前步骤
        if (currentStep < steps.length) {
            const currentEl = document.getElementById(steps[currentStep]);
            if (currentEl) {
                currentEl.classList.add('active');
            }
            
            // 更新状态文本
            const statusEl = document.querySelector('.ai-loading-text');
            if (statusEl) {
                statusEl.textContent = statusTexts[currentStep];
            }
        }
        
        currentStep++;
        
        if (currentStep <= steps.length) {
            setTimeout(updateStep, 2000);
        }
    };
    
    updateStep();
}

// 显示AI分析结果
function displayAIResult(aiResult) {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';
        contentDiv.innerHTML = generateAIContentHTML(aiResult.fortuneAnalysis);
    }
}

// 生成AI内容HTML
function generateAIContentHTML(aiAnalysis) {
    console.log(aiAnalysis)
    let html = '';
    // 详细分析章节
    if (aiAnalysis.sections) {
        const sectionTitles = {
            overall: '🌟 整体运势评估',
            career: '💼 事业工作运势',
            wealth: '💰 财运理财运势',
            love: '❤️ 感情婚姻运势',
            health: '🏥 健康运势养生',
            relationships: '👥 人际关系贵人',
            study: '📚 学习成长发展',
            enhancement: '🔮 开运化解建议',
            general: '🎯 综合分析'
        };
        console.log(aiAnalysis.sections)
        Object.keys(aiAnalysis.sections).forEach(key => {
            const content = aiAnalysis.sections[key];
            if (content && content.trim()) {
                html += `
                    <div class="ai-section">
                        <h4>${sectionTitles[key] || '📊 分析内容'}</h4>
                        <p>${content}</p>
                    </div>
                `;
            }
        });
    }
    
    // 开运要素
    if (aiAnalysis.luckyElements && aiAnalysis.luckyElements.length > 0) {
        html += `
            <div class="ai-section">
                <h4>🍀 开运要素</h4>
                <div class="lucky-elements">
                    ${aiAnalysis.luckyElements.map(element => `<span class="lucky-element">${element}</span>`).join('')}
                </div>
            </div>
        `;
    }
    
    // AI建议
    if (aiAnalysis.recommendations && aiAnalysis.recommendations.length > 0) {
        html += `
            <div class="ai-section ai-recommendations">
                <h4>🎯 专业建议</h4>
                <ul>
                    ${aiAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    // 分析方法说明
    const methodText = aiAnalysis.method === 'ai' ? 
        '本分析由易海堂提供，结合了传统生肖学理论和现代人工智能算法' : 
        '本分析基于传统生肖学理论，结合2025年流年特点生成';
        
    html += `
        <div class="ai-section" style="background: rgba(255, 255, 255, 0.05); font-size: 12px; color: rgba(255, 255, 255, 0.8);">
            <p>${methodText}，仅供参考。建议结合个人实际情况综合考虑。</p>
            <p style="margin: 5px 0 0 0;">分析时间：${new Date(aiAnalysis.timestamp).toLocaleString()}</p>
        </div>
    `;
    
    return html;
}

// 获取AI评分描述
function getAIScoreDescription(score) {
    if (score >= 90) return '评估：运势极佳';
    if (score >= 80) return '评估：运势优秀';
    if (score >= 70) return '评估：运势良好';
    if (score >= 60) return '评估：运势一般';
    return '评估：需要改善';
}

// 显示AI错误
function showAIError(errorMessage) {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';
        contentDiv.innerHTML = `
            <div class="ai-section" style="background: rgba(220, 53, 69, 0.2); border-left-color: #dc3545;">
                <h4>⚠️ 分析暂时不可用</h4>
                <p>原因：${errorMessage}</p>
                <p>已为您提供基础的生肖运势分析，如需深度分析，请稍后重试或联系客服。</p>
            </div>
        `;
    }
}
