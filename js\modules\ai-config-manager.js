/**
 * AI配置管理器
 * 统一管理所有AI服务的配置
 */

class AIConfigManager {
    constructor() {
        this.configs = new Map();
        this.listeners = new Set();
        this.initializeDefaultConfigs();
    }

    // 初始化默认配置
    initializeDefaultConfigs() {
        // 从全局配置中读取
        const globalConfig = window.AI_CONFIG || {};
        
        // 默认配置模板
        const defaultConfig = {
            // 服务选择
            SERVICE_TYPE: 'zhipu',
            FALLBACK_SERVICES: ['deepseek', 'dify', 'offline','zhipu'],
            
            // DeepSeek配置
            DEEPSEEK_API_KEY: '***********************************',
            DEEPSEEK_BASE_URL: 'https://api.deepseek.com/v1',
            DEEPSEEK_MODEL: 'deepseek-chat',
            
            // Dify配置
            DIFY_API_KEY: '',
            DIFY_BASE_URL: 'https://api.dify.ai/v1',
            DIFY_APP_TYPE: 'chatbot',
            
            // 智谱AI配置
            ZHIPU_API_KEY: '38fac9046717433aa99f1b73c97084b1.7ys0b9U1BFfSdGGO',
            ZHIPU_BASE_URL: 'https://open.bigmodel.cn/api/paas/v4',
            ZHIPU_TEXT_MODEL: 'glm-4.5-airx',
            ZHIPU_IMAGE_MODEL: 'CogView-4-250304',
            
            // OpenAI配置
            OPENAI_API_KEY: '',
            OPENAI_BASE_URL: 'https://api.openai.com/v1',
            OPENAI_MODEL: 'gpt-3.5-turbo',
            
            // 请求配置
            MAX_RETRY_ATTEMPTS: 3,
            REQUEST_TIMEOUT: 60000,
            BACKOFF_MULTIPLIER: 2,
            
            // 调试配置
            DEBUG: false
        };

        // 合并全局配置
        this.currentConfig = { ...defaultConfig, ...globalConfig };
        
        // 兼容性处理
        this.handleCompatibility();

        window.AI_CONFIG = defaultConfig;
        
        console.log('🔧 AI配置管理器初始化完成:', this.getConfigSummary());
    }

    // 处理向后兼容性
    handleCompatibility() {
        const config = this.currentConfig;
        
        // 兼容旧的配置键名
        if (config.SERVICE_TYPE) {
            config.PRIMARY_SERVICE = config.SERVICE_TYPE;
        }
        if (config.API_KEY && !config.DEEPSEEK_API_KEY) {
            config.DEEPSEEK_API_KEY = config.API_KEY;
        }
        if (config.BASE_URL && !config.DEEPSEEK_BASE_URL) {
            config.DEEPSEEK_BASE_URL = config.BASE_URL;
        }
        if (config.MODEL && !config.DEEPSEEK_MODEL) {
            config.DEEPSEEK_MODEL = config.MODEL;
        }
    }

    // 获取配置
    getConfig(key = null) {
        if (key) {
            return this.currentConfig[key];
        }
        return { ...this.currentConfig };
    }

    // 设置配置
    setConfig(key, value) {
        if (typeof key === 'object') {
            // 批量设置
            Object.assign(this.currentConfig, key);
        } else {
            // 单个设置
            this.currentConfig[key] = value;
        }
        
        this.notifyListeners();
        return this;
    }

    // 获取特定服务的配置
    getServiceConfig(serviceName) {
        const config = this.currentConfig;
        
        switch (serviceName.toLowerCase()) {
            case 'deepseek':
                return {
                    apiKey: config.DEEPSEEK_API_KEY,
                    baseUrl: config.DEEPSEEK_BASE_URL,
                    model: config.DEEPSEEK_MODEL
                };
            case 'dify':
                return {
                    apiKey: config.DIFY_API_KEY,
                    baseUrl: config.DIFY_BASE_URL,
                    appType: config.DIFY_APP_TYPE
                };
            case 'zhipu':
                return {
                    apiKey: config.ZHIPU_API_KEY,
                    baseUrl: config.ZHIPU_BASE_URL,
                    textModel: config.ZHIPU_TEXT_MODEL,
                    imageModel: config.ZHIPU_IMAGE_MODEL
                };
            case 'openai':
                return {
                    apiKey: config.OPENAI_API_KEY,
                    baseUrl: config.OPENAI_BASE_URL,
                    model: config.OPENAI_MODEL
                };
            default:
                return null;
        }
    }

    // 检查服务是否可用
    isServiceAvailable(serviceName) {
        const serviceConfig = this.getServiceConfig(serviceName);
        
        if (serviceName === 'offline') {
            return true;
        }
        
        return serviceConfig && serviceConfig.apiKey && serviceConfig.baseUrl;
    }

    // 获取可用的服务列表
    getAvailableServices() {
        const services = ['deepseek', 'dify', 'zhipu', 'openai', 'offline'];
        return services.filter(service => this.isServiceAvailable(service));
    }

    // 获取配置摘要
    getConfigSummary() {
        const availableServices = this.getAvailableServices();
        return {
            primaryService: this.currentConfig.PRIMARY_SERVICE,
            availableServices,
            totalServices: availableServices.length,
            debugMode: this.currentConfig.DEBUG
        };
    }

    // 验证配置
    validateConfig() {
        const errors = [];
        const warnings = [];
        
        // 检查主服务是否可用
        if (!this.isServiceAvailable(this.currentConfig.PRIMARY_SERVICE)) {
            errors.push(`主服务 ${this.currentConfig.PRIMARY_SERVICE} 不可用或配置不完整`);
        }
        
        // 检查是否有可用的服务
        const availableServices = this.getAvailableServices();
        if (availableServices.length === 1 && availableServices[0] === 'offline') {
            warnings.push('只有离线模式可用，建议配置至少一个AI服务');
        }
        
        // 检查API密钥格式
        const apiKeys = {
            DEEPSEEK_API_KEY: this.currentConfig.DEEPSEEK_API_KEY,
            DIFY_API_KEY: this.currentConfig.DIFY_API_KEY,
            ZHIPU_API_KEY: this.currentConfig.ZHIPU_API_KEY,
            OPENAI_API_KEY: this.currentConfig.OPENAI_API_KEY
        };
        
        Object.entries(apiKeys).forEach(([key, value]) => {
            if (value && value.length < 10) {
                warnings.push(`${key} 格式可能不正确（长度过短）`);
            }
        });
        
        return { errors, warnings, isValid: errors.length === 0 };
    }

    // 添加配置变更监听器
    addListener(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback);
    }

    // 通知监听器
    notifyListeners() {
        this.listeners.forEach(callback => {
            try {
                callback(this.currentConfig);
            } catch (error) {
                console.error('配置监听器执行失败:', error);
            }
        });
    }

    // 导出配置
    exportConfig() {
        return JSON.stringify(this.currentConfig, null, 2);
    }

    // 导入配置
    importConfig(configJson) {
        try {
            const config = JSON.parse(configJson);
            this.setConfig(config);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 重置为默认配置
    resetToDefaults() {
        this.initializeDefaultConfigs();
        this.notifyListeners();
    }
}

// 创建全局配置管理器实例
window.AIConfigManager = AIConfigManager;

// 如果没有全局实例，创建一个
if (!window.aiConfigManager) {
    window.aiConfigManager = new AIConfigManager();
}

console.log('🔧 AI配置管理器已加载');
