/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

:root {
    --primary-pink: #FF69B4;
    --gradient-pink: linear-gradient(135deg, #FFB6C1, #FF69B4);
    --orange: #FF8C00;
    --blue: #4169E1;
    --red: #FF4444;
    --purple: #8A2BE2;
    --green: #32CD32;
    --cyan: #00CED1;
    --yellow: #FFD700;
    --pink: #FF1493;
    --bg-light: #FFF0F5;
    --text-dark: #333;
    --text-gray: #666;
    --border-light: #E0E0E0;
    --white: #FFFFFF;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --radius: 15px;
    --radius-small: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--gradient-pink);
    min-height: 100vh;
    color: var(--text-dark);
    font-size: 14px;
    line-height: 1.4;
    overflow-x: hidden;
    padding-bottom: 70px; /* 为底部导航留空间 */
}

/* 顶部导航和功能按钮 */
.header {
    background: var(--gradient-pink);
    padding: 10px 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(255,105,180,0.3);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    gap: 15px;
}

.nav-item {
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-item.active {
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.nav-item.master-nav {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: white;
    font-weight: 600;
}

.nav-item.hot-nav {
    background: linear-gradient(45deg, #FF4444, #FF6666);
    color: white;
    font-weight: 600;
    position: relative;
}

.nav-item.hot-nav::after {
    content: '热';
    position: absolute;
    top: -5px;
    right: -5px;
    background: #FF0000;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

/* 功能按钮区域 */
.function-buttons {
    padding: 15px 0;
    background: transparent;
}

.button-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.button-row:last-child {
    margin-bottom: 0;
}

.func-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    border-radius: var(--radius);
    color: white;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    transition: var(--transition);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.func-btn.orange { background: linear-gradient(135deg, #FF8C00, #FFA500); }
.func-btn.blue { background: linear-gradient(135deg, #4169E1, #6495ED); }
.func-btn.red { background: linear-gradient(135deg, #FF4444, #FF6666); }
.func-btn.purple { background: linear-gradient(135deg, #8A2BE2, #9966CC); }
.func-btn.green { background: linear-gradient(135deg, #32CD32, #90EE90); }
.func-btn.cyan { background: linear-gradient(135deg, #00CED1, #40E0D0); }
.func-btn.pink { background: linear-gradient(135deg, #FF1493, #FF69B4); }
.func-btn.orange-light { background: linear-gradient(135deg, #FFA500, #FFB347); }

.func-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.new-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #FF0000;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: bold;
}

/* 轮播图区域 */
.banner-carousel {
    margin: 0 15px 20px;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.carousel-container {
    position: relative;
    height: 150px;
}

.carousel-item {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.carousel-item.active {
    opacity: 1;
}

.banner-content.name-banner {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.banner-text h2 {
    font-size: 24px;
    color: #8B4513;
    font-weight: 700;
    margin-bottom: 5px;
}

.banner-text p {
    font-size: 16px;
    color: #A0522D;
    font-weight: 500;
}

.banner-decoration {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.carousel-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 10px;
    background: var(--bg-light);
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #DDD;
    cursor: pointer;
    transition: var(--transition);
}

.dot.active {
    background: var(--primary-pink);
}

/* 精选服务区域 */
.featured-services {
    padding: 15px 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
    border-radius: 12px;
    margin: 0 8px 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.featured-services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    pointer-events: none;
    border-radius: 12px;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.featured-services .section-header {
    text-align: center;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
}

.featured-services .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    text-shadow: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { filter: brightness(1); }
    to { filter: brightness(1.1); }
}

.services-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 8px;
    position: relative;
    z-index: 2;
}

.service-item {
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
    min-height: 70px;
    animation: cardSlideIn 0.6s ease-out;
    animation-fill-mode: both;
}

.service-item:nth-child(1) { animation-delay: 0.1s; }
.service-item:nth-child(2) { animation-delay: 0.2s; }
.service-item:nth-child(3) { animation-delay: 0.3s; }
.service-item:nth-child(4) { animation-delay: 0.4s; }
.service-item:nth-child(5) { animation-delay: 0.5s; }
.service-item:nth-child(6) { animation-delay: 0.6s; }
.service-item:nth-child(7) { animation-delay: 0.7s; }
.service-item:nth-child(8) { animation-delay: 0.8s; }

@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(15px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: 10px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-item:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 15px rgba(0,0,0,0.12);
    border-color: #d1d5db;
}

.service-item:hover::before {
    opacity: 1;
}

.service-bg {
    padding: 8px 6px;
    min-height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 1;
    border-radius: 10px;
    transition: all 0.3s ease;
}

/* 为每个服务添加独特的渐变背景 */
.service-bg.red-gold {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.service-bg.pink-red {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.service-bg.orange-yellow {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.service-bg.purple-blue {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.service-bg.green-emerald {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.service-bg.blue-cyan {
    background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.service-bg.gold-orange {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.service-bg.teal-green {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255,255,255,0.9);
    border-radius: 8px;
    margin-bottom: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.service-item:hover .service-icon::before {
    left: 100%;
}

.service-item:hover .service-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.icon-symbol {
    font-size: 16px;
    color: #4a5568;
    text-shadow: none;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.service-item:hover .icon-symbol {
    transform: scale(1.1);
    animation: iconBounce 0.6s ease;
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1.1); }
    50% { transform: scale(1.2); }
}

.service-info h3 {
    font-size: 11px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
    line-height: 1.2;
    text-shadow: none;
    transition: color 0.3s ease;
}

.service-item:hover .service-info h3 {
    color: #1a202c;
}

.service-decoration {
    position: absolute;
    top: 4px;
    right: 4px;
    z-index: 2;
}

.lucky-char {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    font-size: 8px;
    font-weight: 600;
    line-height: 16px;
    text-align: center;
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    animation: luckyCharPulse 2s ease-in-out infinite;
}

@keyframes luckyCharPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.service-item:hover .lucky-char {
    transform: scale(1.2);
    box-shadow: 0 3px 8px rgba(0,0,0,0.2);
    animation: none;
}

.service-notice {
    margin-top: 12px;
    padding: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    text-align: center;
    border: none;
    box-shadow: 0 2px 8px rgba(102,126,234,0.3);
    position: relative;
    z-index: 2;
    animation: noticeGlow 3s ease-in-out infinite;
}

@keyframes noticeGlow {
    0%, 100% { box-shadow: 0 2px 8px rgba(102,126,234,0.3); }
    50% { box-shadow: 0 4px 15px rgba(102,126,234,0.5); }
}

.service-notice::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    border-radius: 8px;
    pointer-events: none;
}

.service-notice p {
    color: white;
    font-size: 12px;
    font-weight: 500;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 移动端适配 */
@media (max-width: 480px) {
    .featured-services {
        padding: 12px 8px;
        margin: 0 6px 12px;
    }

    .services-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 6px;
        padding: 0 6px;
    }

    .service-bg {
        padding: 6px 4px;
        min-height: 65px;
    }

    .service-icon {
        width: 28px;
        height: 28px;
        margin-bottom: 4px;
    }

    .service-info h3 {
        font-size: 10px;
    }

    .icon-symbol {
        font-size: 14px;
    }

    .lucky-char {
        width: 14px;
        height: 14px;
        font-size: 7px;
        line-height: 14px;
    }

    .service-decoration {
        top: 3px;
        right: 3px;
    }

    .service-notice {
        margin-top: 10px;
        padding: 8px;
    }

    .service-notice p {
        font-size: 11px;
    }

    .featured-services .section-title {
        font-size: 14px;
    }
}

/* 当前大师部分样式在下一部分继续... */



/* 年度运势专题 */
.fortune-2025 {
    padding: 20px 10px;
    background: #fff; /* 原为linear-gradient(135deg, #FFA500, #FF8C00); 现为白色 */
    border-radius: 18px;
    box-shadow: 0 4px 18px rgba(0,0,0,0.06);
    text-align: center;
    color: #222;
    margin: 0 0 18px 0;
    /* 移除多余的背景色和装饰 */
}

@media (max-width: 768px) {
    .fortune-2025 {
        padding: 12px 4px;
        border-radius: 12px;
        margin-bottom: 12px;
    }
}

.year-badge {
    background: #FF0000;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.fortune-canvas-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    padding: 10px;
    overflow: hidden;
}

#yearlyFortuneCanvas {
    border-radius: 16px;
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    max-width: 100%;
    height: auto;
    transition: all 0.3s ease;
}

#yearlyFortuneCanvas:hover {
    transform: translateY(-2px);
    box-shadow: 0 16px 50px rgba(0,0,0,0.2);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .fortune-canvas-container {
        margin: 15px 0;
        padding: 8px;
    }
    
    #yearlyFortuneCanvas {
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }
    
    #yearlyFortuneCanvas:hover {
        transform: none;
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }
    
    .fortune-2025 {
        padding: 15px 10px;
    }
    
    .year-badge {
        font-size: 11px;
        padding: 4px 12px;
    }
    
    .fortune-title {
        font-size: 20px;
        margin-top: 15px;
    }
}

.fortune-title {
    font-size: 24px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-top: 20px;
}

.fortune-2025-card {
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 6px 32px rgba(255, 140, 66, 0.10);
  padding: 18px 10px 24px 10px;
  margin: 0 0 18px 0;
  text-align: center;
  max-width: 420px;
  margin-left: auto;
  margin-right: auto;
}
.fortune-canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}
.fortune-title {
  font-size: 22px;
  font-weight: 700;
  color: #FF6B35;
  margin: 10px 0 2px 0;
  letter-spacing: 2px;
}
.fortune-subtitle {
  font-size: 14px;
  color: #888;
  margin-bottom: 0;
}
#yearlyFortuneCanvas {
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(255, 140, 66, 0.10);
  max-width: 100%;
  height: auto;
  background: transparent;
}
@media (max-width: 480px) {
  .fortune-2025-card {
    border-radius: 12px;
    padding: 10px 2px 16px 2px;
    max-width: 100%;
  }
  .fortune-title { font-size: 18px; }
  .fortune-subtitle { font-size: 12px; }
  #yearlyFortuneCanvas { border-radius: 10px; }
}


/* 综合测算 */
.comprehensive-calculation {
    padding: 25px 15px;
    background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
    position: relative;
    overflow: hidden;
}

.comprehensive-calculation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 15% 25%, rgba(255,107,107,0.1) 0%, transparent 50%),
        radial-gradient(circle at 85% 75%, rgba(106,90,205,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.comp-header {
    position: relative;
    z-index: 2;
}

.comp-header h3 {
    text-align: center;
    font-size: 22px;
    margin-bottom: 25px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.special-calculation {
    margin-bottom: 25px;
    position: relative;
    z-index: 2;
}

.calc-bg.purple-gradient {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(102,126,234,0.3);
    position: relative;
    overflow: hidden;
}

.calc-bg.purple-gradient::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: rotate 8s linear infinite;
}

.calc-btn.pink {
    background: linear-gradient(135deg, #FF6B9D, #C44569);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255,107,157,0.4);
    position: relative;
    z-index: 2;
}

.calc-btn.pink:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,107,157,0.5);
}

.calculation-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    position: relative;
    z-index: 2;
}

.calc-item {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 
        0 10px 40px rgba(0,0,0,0.1),
        inset 0 1px 1px rgba(255,255,255,0.6);
    display: flex;
    align-items: center;
    gap: 18px;
    border: 1px solid rgba(255,255,255,0.3);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.calc-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s ease;
}

.calc-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 
        0 20px 60px rgba(0,0,0,0.15),
        inset 0 1px 1px rgba(255,255,255,0.8);
    border-color: rgba(255,255,255,0.5);
}

.calc-item:hover::before {
    left: 100%;
}

.calc-icon {
    width: 60px;
    height: 60px;
    border-radius: 18px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(102,126,234,0.3);
    transition: all 0.3s ease;
}

.calc-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #FF6B9D, #667eea, #764ba2, #FF6B9D);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.calc-item:hover .calc-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(102,126,234,0.4);
}

.calc-item:hover .calc-icon::before {
    opacity: 1;
    animation: borderRotate 2s linear infinite;
}

@keyframes borderRotate {
    0% {
        background: linear-gradient(45deg, #FF6B9D, #667eea, #764ba2, #FF6B9D);
    }
    25% {
        background: linear-gradient(135deg, #667eea, #764ba2, #FF6B9D, #667eea);
    }
    50% {
        background: linear-gradient(225deg, #764ba2, #FF6B9D, #667eea, #764ba2);
    }
    75% {
        background: linear-gradient(315deg, #FF6B9D, #667eea, #764ba2, #FF6B9D);
    }
    100% {
        background: linear-gradient(45deg, #FF6B9D, #667eea, #764ba2, #FF6B9D);
    }
}

.calc-icon img {
    width: 75%;
    height: 75%;
    object-fit: contain;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.calc-item:hover .calc-icon img {
    transform: scale(1.1);
}

.calc-content {
    flex: 1;
    position: relative;
    z-index: 2;
}

.calc-content h4 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 6px;
    color: #2D3748;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.calc-item:hover .calc-content h4 {
    color: #667eea;
}

.calc-content p {
    font-size: 13px;
    color: #718096;
    margin-bottom: 10px;
    line-height: 1.4;
    font-weight: 400;
}

.calc-stats {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: #A0AEC0;
    font-weight: 500;
    flex-wrap: nowrap;
}

.calc-stats .views,
.calc-stats .rating {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(102,126,234,0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.calc-item:hover .calc-stats .views,
.calc-item:hover .calc-stats .rating {
    background: rgba(102,126,234,0.15);
    color: #667eea;
}

.calc-action.red {
    background: linear-gradient(135deg, #FF6B9D, #C44569);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 12px 18px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    flex-shrink: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255,107,157,0.3);
    position: relative;
    overflow: hidden;
    min-width: 80px;
    white-space: nowrap;
    text-align: center;
}

.calc-action.red::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.4s ease;
}

.calc-action.red:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,107,157,0.4);
    background: linear-gradient(135deg, #C44569, #FF6B9D);
}

.calc-action.red:hover::before {
    left: 100%;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white);
    display: flex;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}

.bottom-nav .nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    color: var(--text-gray);
    cursor: pointer;
    transition: var(--transition);
}

.bottom-nav .nav-item.active {
    color: var(--primary-pink);
}

.nav-icon {
    font-size: 18px;
    margin-bottom: 2px;
}

.nav-text {
    font-size: 10px;
}

/* 底部信息 */
.footer {
    padding: 15px;
    background: var(--white);
    text-align: center;
}

.contact-info p {
    color: var(--text-gray);
    font-size: 12px;
    margin-bottom: 10px;
}

.copyright p {
    color: var(--text-gray);
    font-size: 10px;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .nav-container {
        gap: 8px;
    }
    
    .nav-item {
        padding: 6px 8px;
        font-size: 12px;
    }
    
    .func-btn {
        font-size: 12px;
        padding: 10px 6px;
    }
    
    /* 移动端综合测算适配 */
    .comprehensive-calculation {
        padding: 20px 10px;
    }
    
    .comp-header h3 {
        font-size: 20px;
        margin-bottom: 20px;
    }
    
    .calc-bg.purple-gradient {
        padding: 20px 15px;
        border-radius: 16px;
    }
    
    .calculation-list {
        gap: 15px;
    }
    
    .calc-item {
        padding: 16px;
        gap: 15px;
        border-radius: 16px;
    }
    
    .calc-icon {
        width: 50px;
        height: 50px;
        border-radius: 14px;
    }
    
    .calc-content h4 {
        font-size: 15px;
        margin-bottom: 5px;
        line-height: 1.2;
    }
    
    .calc-content p {
        font-size: 12px;
        margin-bottom: 8px;
        line-height: 1.3;
    }
    
    .calc-stats {
        gap: 10px;
        font-size: 11px;
        flex-wrap: nowrap;
    }
    
    .calc-stats .views,
    .calc-stats .rating {
        padding: 3px 6px;
        border-radius: 10px;
        font-size: 10px;
    }
    
    .calc-action.red {
        padding: 10px 14px;
        font-size: 12px;
        border-radius: 16px;
        min-width: 72px;
        white-space: nowrap;
    }
    
    .calc-btn.pink {
        padding: 10px 20px;
        font-size: 13px;
        border-radius: 20px;
    }

}

/* 其他组件样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 5px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-dark);
}

.more {
    font-size: 12px;
    color: var(--text-gray);
    cursor: pointer;
}

.calculation-tags,
.naming-tags,
.master-tags,
.tarot-services {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.calc-tag,
.name-tag,
.master-tag,
.tarot-tag {
    background: var(--bg-light);
    border-radius: 12px;
    padding: 6px 12px;
    font-size: 11px;
    color: var(--text-dark);
    cursor: pointer;
    transition: var(--transition);
}

.calc-tag:hover,
.name-tag:hover,
.master-tag:hover,
.tarot-tag:hover {
    background: var(--primary-pink);
    color: white;
}

.name-tag.featured {
    background: linear-gradient(135deg, #FF69B4, #FF1493);
    color: white;
    box-shadow: 0 2px 8px rgba(255,105,180,0.3);
    font-weight: 600;
}

.hot-calculation,
.zodiac-animals,
.naming-section,
.master-calculation,
.tarot-section {
    padding: 20px 15px;
    background: var(--white);
    margin-bottom: 5px;
}

.animals-grid {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 10px 0 20px 0;
    scrollbar-width: thin;
    scrollbar-color: rgba(255,105,180,0.3) transparent;
    -webkit-overflow-scrolling: touch;
}

.animals-grid::-webkit-scrollbar {
    height: 4px;
}

.animals-grid::-webkit-scrollbar-track {
    background: rgba(255,105,180,0.1);
    border-radius: 2px;
}

.animals-grid::-webkit-scrollbar-thumb {
    background: rgba(255,105,180,0.4);
    border-radius: 2px;
}

.animals-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(255,105,180,0.6);
}

.animal-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    flex-shrink: 0;
    min-width: 60px;
    padding: 5px;
    transition: var(--transition);
    border-radius: var(--radius-small);
}

.animal-item:hover {
    background: rgba(255,105,180,0.05);
    transform: translateY(-2px);
}

.animal-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
    transition: var(--transition);
}

.animal-item:hover .animal-icon {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.animal-icon.purple { background: linear-gradient(135deg, #8A2BE2, #9966CC); }
.animal-icon.yellow { background: linear-gradient(135deg, #FFD700, #FFA500); }
.animal-icon.orange { background: linear-gradient(135deg, #FF8C00, #FFA500); }
.animal-icon.blue { background: linear-gradient(135deg, #4169E1, #6495ED); }
.animal-icon.pink { background: linear-gradient(135deg, #FF1493, #FF69B4); }
.animal-icon.green { background: linear-gradient(135deg, #32CD32, #90EE90); }
.animal-icon.red { background: linear-gradient(135deg, #FF4444, #FF6666); }
.animal-icon.brown { background: linear-gradient(135deg, #D2691E, #CD853F); }
.animal-icon.gold { background: linear-gradient(135deg, #DAA520, #FFD700); }
.animal-icon.cyan { background: linear-gradient(135deg, #00CED1, #40E0D0); }
.animal-icon.indigo { background: linear-gradient(135deg, #4B0082, #8A2BE2); }
.animal-icon.violet { background: linear-gradient(135deg, #9370DB, #BA55D3); }

.animal-item span {
    font-size: 12px;
    color: var(--text-dark);
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.tarot-features {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.tarot-card.featured {
    background: linear-gradient(135deg, #E6E6FA, #DDA0DD);
    border-radius: var(--radius-small);
    padding: 8px 12px;
    font-size: 11px;
    color: var(--purple);
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

/* 宝宝起名推广区域样式 */
.naming-promo {
    margin-top: 15px;
    border-radius: var(--radius);
    overflow: hidden;
    position: relative;
}

.promo-background {
    background: linear-gradient(135deg, #FFE4E9 0%, #FFB3C6 50%, #FF8FA3 100%);
    position: relative;
    padding: 30px 20px;
    min-height: 200px;
    overflow: hidden;
}

.promo-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 70%, rgba(255,182,193,0.4) 0%, transparent 30%);
    pointer-events: none;
}

.decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.star, .heart {
    position: absolute;
    font-size: 16px;
    animation: float 3s ease-in-out infinite;
}

.star-1 { top: 15%; left: 10%; animation-delay: 0s; }
.star-2 { top: 25%; right: 15%; animation-delay: 1s; }
.star-3 { bottom: 20%; left: 20%; animation-delay: 2s; }
.heart-1 { top: 60%; right: 10%; animation-delay: 0.5s; }
.heart-2 { bottom: 15%; right: 25%; animation-delay: 1.5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
    50% { transform: translateY(-10px) scale(1.1); opacity: 1; }
}

.content-wrapper {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 25px;
    background: rgba(255,255,255,0.9);
    border-radius: 20px;
    padding: 25px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(255,105,180,0.2);
}

.promo-image {
    flex-shrink: 0;
    width: 140px;
    height: 140px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF69B4, #FFB6C1);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
    opacity: 0.3;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.5; }
}

.promo-image img {
    width: 120px;
    height: 120px;
    object-fit: contain;
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 8px 20px rgba(255,105,180,0.4));
    transition: transform 0.3s ease;
}

.promo-image:hover img {
    transform: scale(1.05);
}

.promo-text {
    flex: 1;
}

.promo-text h4 {
    font-size: 22px;
    font-weight: 700;
    background: linear-gradient(135deg, #FF1493, #FF69B4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(255,105,180,0.1);
}

.subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
    font-style: italic;
}

.promo-features {
    margin-bottom: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px 12px;
    background: rgba(255,105,180,0.1);
    border-radius: 25px;
    transition: transform 0.2s ease;
}

.feature-item:hover {
    transform: translateX(5px);
    background: rgba(255,105,180,0.15);
}

.feature-icon {
    font-size: 16px;
    width: 24px;
    text-align: center;
}

.feature-item span:last-child {
    font-size: 12px;
    color: var(--text-dark);
    font-weight: 500;
}

.cta-button {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    width: 100%;
}

.name-consultation-btn {
    background: linear-gradient(135deg, #FF1493, #FF69B4);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255,20,147,0.3);
    min-width: 160px;
}

.name-consultation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,20,147,0.4);
}

.btn-text {
    font-size: 13px;
}

.btn-arrow {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.name-consultation-btn:hover .btn-arrow {
    transform: translateX(3px);
}

/* 移动端适配 */
@media (max-width: 480px) {
    .promo-background {
        padding: 20px 15px;
        min-height: auto;
    }
    
    .content-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 20px;
    }
    
    .promo-image {
        width: 120px;
        height: 120px;
    }
    
    .promo-image img {
        width: 100px;
        height: 100px;
    }
    
    .promo-text h4 {
        font-size: 20px;
    }
    
    .subtitle {
        font-size: 13px;
    }
    
    .feature-item {
        justify-content: center;
    }
    
    .feature-item span:last-child {
        font-size: 11px;
    }
    
    .name-consultation-btn {
        padding: 10px 20px;
    }
    
    .btn-text {
        font-size: 12px;
    }
}

/* 八字精批模态框样式 */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-overlay.show {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-container {
    background: linear-gradient(135deg, #2C1810, #3D2817, #4A3728);
    border-radius: 20px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 
        0 20px 60px rgba(220, 20, 60, 0.3),
        0 0 100px rgba(255, 215, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    position: relative;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from { 
        transform: translateY(-50px) scale(0.9); 
        opacity: 0; 
    }
    to { 
        transform: translateY(0) scale(1); 
        opacity: 1; 
    }
}

.modal-header {
    background: linear-gradient(135deg, #DC143C, #B22222);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid rgba(255, 215, 0, 0.5);
    position: relative;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(255,215,0,0.2) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 40%);
    pointer-events: none;
}

.modal-header h2 {
    color: #FFD700;
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    text-shadow: 
        0 0 10px rgba(255, 215, 0, 0.5),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    position: relative;
    z-index: 1;
}

.close-btn {
    color: #FFD700;
    font-size: 32px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1;
    position: relative;
    z-index: 1;
}

.close-btn:hover {
    transform: rotate(90deg) scale(1.2);
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
}

.modal-content {
    position: relative;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

.bazi-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.bazi-form-container {
    position: relative;
    z-index: 2;
    padding: 30px 25px;
    background: rgba(0, 0, 0, 0.2);
}

.form-title {
    text-align: center;
    margin-bottom: 30px;
}

.form-title h3 {
    color: #FFD700;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.form-title p {
    color: #D4AF37;
    font-size: 13px;
    margin: 0;
    opacity: 0.9;
}

.bazi-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #D4AF37;
    font-size: 14px;
    font-weight: 600;
}

.label-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.input-group input,
.input-group select {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 12px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2C1810;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 
        inset 0 2px 5px rgba(0, 0, 0, 0.1),
        0 0 15px rgba(255, 215, 0, 0.3);
    transform: translateY(-1px);
}

/* date 特殊样式 */
.input-group input[type="date"] {
    background: rgba(255, 255, 255, 0.95);
    color: #2C1810;
    font-family: inherit;
    cursor: pointer;
}

.input-group input[type="date"]::-webkit-calendar-picker-indicator {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 3px;
    padding: 2px;
    cursor: pointer;
}

.input-group input[type="date"]::-webkit-datetime-edit {
    color: #2C1810;
}

.input-group input[type="date"]::-webkit-datetime-edit-fields-wrapper {
    background: transparent;
}

.gender-options {
    display: flex;
    gap: 15px;
    align-items: center;
}

.gender-option {
    flex: 1;
}

.gender-option input[type="radio"] {
    display: none;
}

.gender-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 12px;
    padding: 15px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.gender-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
}

.gender-option input[type="radio"]:checked + .gender-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    border-color: #FF6347;
    box-shadow: 0 5px 20px rgba(255, 215, 0, 0.4);
}

.gender-btn.male {
    border-left: 4px solid #4169E1;
}

.gender-btn.female {
    border-left: 4px solid #FF69B4;
}

.gender-icon {
    font-size: 24px;
    font-weight: 700;
}

.submit-section {
    margin-top: 20px;
    text-align: center;
}

.submit-btn {
    background: linear-gradient(135deg, #DC143C, #FF6347, #FFD700);
    border: none;
    border-radius: 25px;
    padding: 15px 30px;
    color: white;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(220, 20, 60, 0.4);
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(220, 20, 60, 0.6);
}

.btn-icon {
    font-size: 18px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.2) rotate(90deg); }
    50% { transform: scale(1) rotate(180deg); }
    75% { transform: scale(1.1) rotate(270deg); }
}

.btn-price {
    background: rgba(0, 0, 0, 0.3);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 700;
}

.submit-note {
    color: #D4AF37;
    font-size: 12px;
    margin-top: 15px;
    opacity: 0.9;
}

/* 移动端适配 */
@media (max-width: 480px) {
    .modal-container {
        margin: 10px;
        max-height: 95vh;
    }
    
    .modal-header {
        padding: 15px 20px;
    }
    
    .modal-header h2 {
        font-size: 20px;
    }
    
    .bazi-form-container {
        padding: 20px 15px;
    }
    
    .form-title h3 {
        font-size: 18px;
    }
    
    .gender-options {
        gap: 10px;
    }
    
    .gender-btn {
        flex-direction: row;
        justify-content: center;
        padding: 10px 12px;
        font-size: 13px;
    }
    
    .submit-btn {
        padding: 12px 25px;
        font-size: 14px;
    }
}

/* 八字合婚模态框样式 */
.marriage-modal {
    max-width: 600px;
    background: linear-gradient(135deg, #4A1A1A, #6B2C2C, #8B3A3A);
}

.marriage-header {
    background: linear-gradient(135deg, #E91E63, #AD1457);
    border-bottom: 3px solid rgba(255, 182, 193, 0.5);
}

.marriage-header::before {
    background: 
        radial-gradient(circle at 20% 20%, rgba(255,182,193,0.3) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 40%);
}

.marriage-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.marriage-form-container {
    position: relative;
    z-index: 2;
    padding: 25px 20px;
    background: rgba(0, 0, 0, 0.2);
}

.marriage-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.person-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.male-section {
    border-left: 4px solid #2196F3;
}

.male-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(33,150,243,0.1), rgba(33,150,243,0.2));
    border-radius: 50%;
    transform: translate(20px, -20px);
}

.female-section {
    border-left: 4px solid #E91E63;
}

.female-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(233,30,99,0.1), rgba(233,30,99,0.2));
    border-radius: 50%;
    transform: translate(20px, -20px);
}

.person-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(0,0,0,0.1);
}

.person-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
}

.male-section .person-icon {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.female-section .person-icon {
    background: linear-gradient(135deg, #E91E63, #AD1457);
}

.person-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2C1810;
    margin: 0;
}

.input-row {
    display: flex;
    gap: 15px;
}

.input-group.half {
    flex: 1;
}

.input-group.half label {
    font-size: 12px;
}

.input-group.half select {
    font-size: 12px;
    padding: 10px 12px;
}

.input-group.third {
    flex: 1;
    min-width: 0;
}

.input-group.third label {
    font-size: 12px;
}

.input-group.third select {
    font-size: 12px;
    padding: 10px 12px;
}

.divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    position: relative;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(233,30,99,0.3), transparent);
}

.divider-icon {
    font-size: 24px;
    margin: 0 15px;
    background: rgba(255,255,255,0.9);
    padding: 10px;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(233,30,99,0.2);
    animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    25% { transform: scale(1.1); }
    50% { transform: scale(1); }
    75% { transform: scale(1.05); }
}

.marriage-submit {
    background: linear-gradient(135deg, #E91E63, #FF4081, #FFB6C1);
}

.marriage-submit:hover {
    box-shadow: 0 12px 35px rgba(233,30,99,0.6);
}

.marriage-submit .btn-icon {
    animation: loveFloat 2s ease-in-out infinite;
}

@keyframes loveFloat {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.2) rotate(-5deg); }
    50% { transform: scale(1) rotate(0deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .marriage-modal {
        margin: 5px;
        max-height: 98vh;
    }
    
    .marriage-form-container {
        padding: 15px 10px;
    }
    
    .person-section {
        padding: 15px;
    }
    
    .input-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .input-group.half {
        flex: none;
    }
    
    .input-group.third {
        flex: none;
    }
    
    .person-header {
        margin-bottom: 15px;
    }
    
    .person-icon {
        width: 35px;
        height: 35px;
        font-size: 20px;
    }
    
    .person-header h4 {
        font-size: 14px;
    }
    
    .divider-icon {
        font-size: 20px;
        padding: 8px;
    }
}

/* 宝宝起名模态框样式 */
.baby-name-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.baby-name-modal.show {
    opacity: 1;
}

.baby-name-modal .modal-container {
    max-width: 550px;
    background: linear-gradient(135deg, #FFB6C1, #FFC0CB, #FFE4E1);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transform: translateY(30px);
    transition: transform 0.3s ease;
    overflow: hidden;
    max-height: 90vh;
    width: 90%;
}

.baby-name-modal.show .modal-container {
    transform: translateY(0);
}

.baby-header {
    background: linear-gradient(135deg, #FF69B4, #FF1493, #DC143C);
    border-bottom: 3px solid rgba(255, 182, 193, 0.5);
}

.baby-header::before {
    background: 
        radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(255,192,203,0.2) 0%, transparent 40%);
}

.baby-name-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.baby-name-form-container {
    position: relative;
    z-index: 2;
    padding: 25px 20px;
    background: rgba(255, 255, 255, 0.1);
}

.baby-name-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.baby-gender {
    gap: 10px;
}

.baby-gender .gender-option {
    flex: 1;
}

.baby-gender .gender-btn {
    min-height: 60px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 105, 180, 0.3);
    flex-direction: column;
    gap: 5px;
}

.baby-gender .gender-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
}

.baby-gender .gender-btn.unknown {
    border-left: 4px solid #FFB6C1;
}

.baby-gender input[type="radio"]:checked + .gender-btn {
    background: linear-gradient(135deg, #FF69B4, #FFB6C1);
    color: white;
    border-color: #FF1493;
    box-shadow: 0 5px 20px rgba(255, 105, 180, 0.4);
}

.baby-gender input[type="radio"]:checked + .gender-btn.unknown {
    background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
    color: #333;
}

.name-preferences {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 8px;
}

.preference-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.preference-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #FF69B4;
    border-radius: 3px;
}

.preference-label {
    font-size: 13px;
    color: #2C1810;
    cursor: pointer;
    user-select: none;
}

.preference-label:hover {
    color: #FF1493;
}

.baby-submit {
    background: linear-gradient(135deg, #FF69B4, #FFB6C1, #FFC0CB);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.baby-submit:hover {
    box-shadow: 0 12px 35px rgba(255, 105, 180, 0.6);
}

.baby-submit .btn-icon {
    animation: babySparkle 2s ease-in-out infinite;
}

@keyframes babySparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.2) rotate(-5deg); }
    50% { transform: scale(1) rotate(0deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .baby-name-modal .modal-container {
        margin: 5px;
        max-height: 98vh;
        width: 95%;
    }
    
    .baby-name-form-container {
        padding: 15px 10px;
    }
    
    .baby-gender {
        gap: 8px;
    }
    
    .baby-gender .gender-btn {
        min-height: 50px;
        font-size: 12px;
    }
    
    .name-preferences {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .preference-label {
        font-size: 12px;
    }
} 