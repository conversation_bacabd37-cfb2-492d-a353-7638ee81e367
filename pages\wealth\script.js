// 财运预测页面JavaScript功能

// Canvas动画变量
let canvas, ctx;
let particles = [];
let animationId;

// AI服务变量
let aiService = null;
let wealthAnalysisAI = null;
let currentUserData = null;
let currentBasicResult = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 开始初始化财运预测页面...');
    // 初始化认证服务
    window.unifiedAuthService = new UnifiedAuthService();
    window.memberService = new MemberService();
    window.orderPaymentManager = new OrderPaymentManager();
    // 初始化认证服务
    initializeAuthServices();
    
    // 初始化其他服务
    initCanvas();
    initForm();
    generateParticles();
    animateCanvas();
    initializeAIServices();
    initWealthDatePicker();
    
    console.log('✅ 财运预测页面初始化完成');
    console.log('💰 支付流程已优化：支付完成后自动开始预测');
});

// 初始化认证服务
function initializeAuthServices() {
    try {
        console.log('🔐 初始化认证服务...');
        
        // 检查统一认证服务
        if (typeof UnifiedAuthService !== 'undefined') {
            window.unifiedAuthService = new UnifiedAuthService();
            console.log('✅ 统一认证服务初始化成功');
            
            // 检查登录状态
            const isLoggedIn = window.unifiedAuthService.isLoggedIn();
            const userType = window.unifiedAuthService.getUserType();
            const currentUser = window.unifiedAuthService.getCurrentUser();
            
            console.log('🔍 登录状态检查:', {
                isLoggedIn,
                userType,
                hasCurrentUser: !!currentUser,
                currentUser: currentUser
            });
        } else {
            console.warn('⚠️ UnifiedAuthService 未找到');
        }
        
        // 检查会员服务
        if (typeof MemberService !== 'undefined') {
            window.memberService = new MemberService();
            console.log('✅ 会员服务初始化成功');
        } else {
            console.warn('⚠️ MemberService 未找到');
        }
        
        // 初始化订单支付管理器
        if (typeof OrderPaymentManager !== 'undefined') {
            window.orderPaymentManager = new OrderPaymentManager();
            console.log('✅ 订单支付管理器初始化成功');
        } else {
            console.warn('⚠️ OrderPaymentManager 未找到');
        }
        
    } catch (error) {
        console.error('❌ 认证服务初始化失败:', error);
    }
}

// 初始化AI服务
function initializeAIServices() {
    try {
        console.log('�� 初始化财运分析AI服务...');
        console.log('AI配置:', window.AI_CONFIG);
        
        // 检查必要的类是否存在
        if (typeof WealthAnalysisAI === 'undefined') {
            console.error('❌ WealthAnalysisAI 类未找到，请检查模块是否正确加载');
            return;
        }
        
        // 初始化财运分析AI
        wealthAnalysisAI = new WealthAnalysisAI();
        console.log('✅ 财运分析AI模块初始化成功');
        
        // 更新AI服务类型显示
        updateAIServiceDisplay();
        
    } catch (error) {
        console.error('❌ AI服务初始化失败:', error);
        // 继续使用基础功能，不影响主要功能
    }
}

// 更新AI服务类型显示
function updateAIServiceDisplay() {
    const serviceTypeElement = document.getElementById('aiServiceType');
    if (serviceTypeElement) {
        serviceTypeElement.textContent = '易海堂分析';
    }
}

// Canvas初始化
function initCanvas() {
    canvas = document.getElementById('wealthCanvas');
    ctx = canvas.getContext('2d');
    
    // 设置canvas尺寸
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
}

// 生成粒子
function generateParticles() {
    particles = [];
    const particleCount = 50;
    
    for (let i = 0; i < particleCount; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            size: Math.random() * 3 + 1,
            speedX: (Math.random() - 0.5) * 0.5,
            speedY: (Math.random() - 0.5) * 0.5,
            opacity: Math.random() * 0.5 + 0.2,
            color: getRandomGoldColor(),
            symbol: getRandomWealthSymbol()
        });
    }
}

// 获取随机金色
function getRandomGoldColor() {
    const colors = [
        'rgba(255, 215, 0, ',
        'rgba(255, 165, 0, ',
        'rgba(255, 140, 0, ',
        'rgba(218, 165, 32, '
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}

// 获取随机财运符号
function getRandomWealthSymbol() {
    const symbols = ['💰', '💎', '🪙', '💵', '🏆', '⭐', '✨', '🌟'];
    return symbols[Math.floor(Math.random() * symbols.length)];
}

// Canvas动画
function animateCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制渐变背景
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, 'rgba(255, 215, 0, 0.1)');
    gradient.addColorStop(0.5, 'rgba(255, 165, 0, 0.05)');
    gradient.addColorStop(1, 'rgba(255, 140, 0, 0.1)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 更新和绘制粒子
    particles.forEach(particle => {
        // 更新位置
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // 边界检查
        if (particle.x < 0 || particle.x > canvas.width) particle.speedX *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.speedY *= -1;
        
        // 绘制粒子
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        ctx.font = `${particle.size * 8}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText(particle.symbol, particle.x, particle.y);
        ctx.restore();
        
        // 绘制发光效果
        ctx.save();
        ctx.globalAlpha = particle.opacity * 0.3;
        ctx.fillStyle = particle.color + (particle.opacity * 0.3) + ')';
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size * 3, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    });
    
    animationId = requestAnimationFrame(animateCanvas);
}

// 表单初始化
function initForm() {
    const form = document.getElementById('wealthForm');
    form.addEventListener('submit', handleFormSubmit);
}

// 表单提交处理
async function handleFormSubmit(e) {
    e.preventDefault();
    
    try {
        // 检查登录状态
        console.log('🔍 表单提交前检查登录状态...');
        
        let isLoggedIn = false;
        let loginInfo = {};
        
        // 检查统一认证服务
        if (window.unifiedAuthService) {
            const unifiedLoggedIn = window.unifiedAuthService.isLoggedIn();
            const userType = window.unifiedAuthService.getUserType();
            const currentUser = window.unifiedAuthService.getCurrentUser();
            
            loginInfo.unifiedAuth = {
                isLoggedIn: unifiedLoggedIn,
                userType: userType,
                hasCurrentUser: !!currentUser,
                currentUser: currentUser
            };
            
            if (unifiedLoggedIn && userType === 'member') {
                isLoggedIn = true;
                console.log('✅ 统一认证服务检测到会员已登录');
            }
        }
        
        // 检查会员服务
        if (window.memberService && window.memberService.isLoggedIn) {
            const memberLoggedIn = window.memberService.isLoggedIn();
            const memberInfo = window.memberService.getMemberInfo();
            
            loginInfo.memberService = {
                isLoggedIn: memberLoggedIn,
                hasMemberInfo: !!memberInfo,
                memberInfo: memberInfo
            };
            
            if (memberLoggedIn && !isLoggedIn) {
                isLoggedIn = true;
                console.log('✅ 会员服务检测到已登录');
            }
        }
        
        console.log('📊 登录状态汇总:', {
            isLoggedIn,
            loginInfo
        });
        
        const formData = new FormData(e.target);
        const birthDate = formData.get('birthDate');
        const date = new Date(birthDate);
        
        const userData = {
            userName: formData.get('userName'),
            name: formData.get('userName'),
            gender: formData.get('gender'),
            birthYear: date.getFullYear(),
            birthMonth: date.getMonth() + 1,
            birthDay: date.getDate(),
            birthDate: birthDate,
            birthHour: formData.get('birthHour'),
            focus: formData.getAll('focus')
        };

        // 验证表单数据
        if (!validateFormData(userData)) {
            return;
        }

        // 服务配置
        const serviceConfig = {
            type: 'wealth',
            name: '财运预测',
            price: 25.9,
            description: '通过生辰八字精准分析财运走势'
        };

        console.log('💰 开始创建订单和支付流程...');

        // 创建订单并支付
        try {
            console.log('💰 正在创建订单...');
            
            // 显示支付处理提示
            const submitBtn = document.querySelector('.wealth-submit');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = `
                    <span class="btn-icon">⏳</span>
                    <span class="btn-text">正在处理支付...</span>
                `;
                submitBtn.disabled = true;
                
                // 恢复按钮状态
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            }
            
            await window.orderPaymentManager.createOrderAndPay(
                serviceConfig,
                userData,
                // 支付成功回调
                async function(order, paymentResult) {
                    console.log('✅ 支付成功！');
                    console.log('📊 订单信息:', {
                        订单ID: order.id,
                        用户ID: order.userId,
                        服务类型: order.serviceType,
                        服务名称: order.serviceName,
                        金额: `¥${order.amount}`,
                        状态: order.status
                    });
                    console.log('💳 支付结果:', paymentResult);
                    
                    // 支付成功后开始财运分析
                    console.log('🎯 支付完成，开始财运分析...');
                    await performWealthAnalysis(userData);
                },
                // 取消支付回调
                function(order) {
                    console.log('❌ 用户取消支付');
                    console.log('📊 取消的订单信息:', {
                        订单ID: order.id,
                        服务名称: order.serviceName,
                        金额: `¥${order.amount}`
                    });
                    alert('支付已取消，如需进行财运预测请重新提交');
                }
            );
        } catch (error) {
            console.error('❌ 订单创建失败:', error);
            alert('创建订单失败，请稍后重试');
        }
    } catch (error) {
        console.error('❌ 表单处理失败:', error);
        alert('表单处理失败，请稍后重试');
    }
}

// 验证表单数据
function validateFormData(userData) {
    if (!userData.userName || userData.userName.trim() === '') {
        alert('请输入姓名');
        return false;
    }
    
    if (!userData.gender) {
        alert('请选择性别');
        return false;
    }
    
    if (!userData.birthDate) {
        alert('请选择出生日期');
        return false;
    }
    
    if (!userData.birthHour) {
        alert('请选择出生时辰');
        return false;
    }
    
    return true;
}

// 执行财运分析
async function performWealthAnalysis(userData) {
    try {
        console.log('🎯 开始执行财运分析...');
        console.log('用户数据:', userData);
        
        // 保存用户数据
        currentUserData = userData;
        
        // 显示加载动画
        showLoading();

        
        // 生成财运结果
        const result = generateWealthFortune(userData);
        currentBasicResult = result;
        // 隐藏加载动画并显示结果
        hideLoading();
        // showResult(result);
        
        // 启动AI深度分析
        console.log('🚀 准备启动AI深度分析...');
        
        // 延迟启动AI分析，确保基础结果显示完成
        setTimeout(() => {
        startAIAnalysis(userData, result);
        }, 1000);
        
    } catch (error) {
        console.error('❌ 财运分析失败:', error);
        hideLoading();
        alert('分析失败，请稍后重试');
    }
}

// 表单验证
function validateForm(data) {
    if (!data.name || data.name.length < 2) {
        alert('请输入正确的姓名（至少2个字符）');
        return false;
    }
    
    if (!data.gender || (data.gender !== 'male' && data.gender !== 'female')) {
        alert('请选择性别');
        return false;
    }
    
    if (!data.birthDate) {
        alert('请选择出生日期');
        return false;
    }
    
    // 验证日期格式和范围
    const birthDate = new Date(data.birthDate);
    const currentYear = new Date().getFullYear();
    const birthYear = birthDate.getFullYear();
    
    if (birthYear < 1930 || birthYear > currentYear) {
        alert('请输入正确的出生日期');
        return false;
    }
    
    if (!data.birthHour) {
        alert('请选择出生时辰');
        return false;
    }
    
    return true;
}

// 显示加载动画
function showLoading() {
    console.log('🔄 显示加载动画...');
    
    // 隐藏结果区域
    const resultContainer = document.getElementById('resultContainer');
    if (resultContainer) {
        resultContainer.style.display = 'none';
    }
    
    // 显示加载动画
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
        
        // 添加支付成功提示
        const successMessage = document.createElement('div');
        successMessage.id = 'paymentSuccessMessage';
        successMessage.style.cssText = `
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1001;
        `;
        successMessage.innerHTML = `
            <div style="font-size: 24px; margin-bottom: 10px;">✅</div>
            <div>支付成功！正在为您分析财运...</div>
        `;
        
        loadingOverlay.appendChild(successMessage);
    }
    
    console.log('✅ 加载动画已显示');
}

// 隐藏加载动画
function hideLoading() {
    console.log('🔄 隐藏加载动画...');
    
    // 隐藏加载动画
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
    
    // 移除支付成功提示
    const successMessage = document.getElementById('paymentSuccessMessage');
    if (successMessage) {
        successMessage.remove();
    }
    
    console.log('✅ 加载动画已隐藏');
}

// 模拟分析过程
function simulateAnalysis() {
    return new Promise(resolve => {
        setTimeout(resolve, 3200);
    });
}

// 生成财运预测结果
function generateWealthFortune(userData) {
    const { name, gender, birthYear, birthMonth, birthDay, birthHour, focus } = userData;
    
    // 计算五行属性
    const wuxing = calculateWuxing(birthYear, birthMonth, birthDay, birthHour);
    
    // 计算财运分数
    const wealthScore = calculateWealthScore(wuxing, gender);
    
    // 生成具体分析
    const analysis = generateAnalysis(wuxing, wealthScore, focus, gender);
    
    // 生成建议
    const suggestions = generateSuggestions(wuxing, wealthScore, focus);
    
    return {
        name,
        wealthScore,
        wuxing,
        analysis,
        suggestions,
        luckMonths: generateLuckMonths(),
        direction: getBestDirection(wuxing),
        industry: getBestIndustry(wuxing)
    };
}

// 计算五行属性
function calculateWuxing(year, month, day, hour) {
    const yearWuxing = ['金', '木', '水', '火', '土'][year % 5];
    const monthWuxing = ['水', '土', '木', '木', '土', '火', '火', '土', '金', '金', '土', '水'][month - 1];
    const dayWuxing = ['金', '木', '水', '火', '土'][day % 5];
    
    const hourMap = {
        'zi': '水', 'chou': '土', 'yin': '木', 'mao': '木',
        'chen': '土', 'si': '火', 'wu': '火', 'wei': '土',
        'shen': '金', 'you': '金', 'xu': '土', 'hai': '水'
    };
    const hourWuxing = hourMap[hour] || '土';
    
    return {
        year: yearWuxing,
        month: monthWuxing,
        day: dayWuxing,
        hour: hourWuxing,
        main: dayWuxing // 以日元为主
    };
}

// 计算财运分数
function calculateWealthScore(wuxing, gender) {
    let score = 70; // 基础分数
    
    // 根据五行财星强弱调整
    const wealthElements = {
        '金': ['木', '火'],
        '木': ['土', '金'],
        '水': ['火', '土'],
        '火': ['金', '水'],
        '土': ['水', '木']
    };
    
    const mainElement = wuxing.main;
    const wealth = wealthElements[mainElement] || [];
    
    // 检查财星是否出现
    Object.values(wuxing).forEach(element => {
        if (wealth.includes(element)) {
            score += 8;
        }
    });
    
    // 性别调整
    if (gender === 'male') {
        score += Math.random() * 10 - 5;
    } else {
        score += Math.random() * 8 - 4;
    }
    
    return Math.min(100, Math.max(30, Math.round(score)));
}

// 生成详细分析
function generateAnalysis(wuxing, score, focus, gender) {
    const analyses = [];
    
    // 基础财运分析
    if (score >= 85) {
        analyses.push('您的八字财星很旺，天生具备良好的财运基础，容易获得意外之财。');
    } else if (score >= 70) {
        analyses.push('您的财运中等偏上，通过努力可以获得不错的财富积累。');
    } else if (score >= 55) {
        analyses.push('您的财运平稳，需要谨慎理财，避免投机取巧。');
    } else {
        analyses.push('您的财运较弱，建议多积德行善，稳扎稳打发展事业。');
    }
    
    // 五行财运分析
    const elementAnalysis = {
        '金': '金主财，您具备敏锐的商业嗅觉，适合金融投资领域。',
        '木': '木主生长，您的财运需要时间积累，坚持不懈必有收获。',
        '水': '水主流动，您适合与流动性相关的行业，财来财去要注意储蓄。',
        '火': '火主旺盛，您的财运起伏较大，把握机遇很重要。',
        '土': '土主稳定，您适合稳健投资，不宜冒险求财。'
    };
    
    analyses.push(elementAnalysis[wuxing.main]);
    
    // 关注重点分析
    if (focus.includes('investment')) {
        analyses.push('投资方面：建议选择与您五行相合的投资项目，避免过度投机。');
    }
    if (focus.includes('career')) {
        analyses.push('事业财运：您的事业运势与财运相辅相成，稳定发展事业是增财之道。');
    }
    if (focus.includes('business')) {
        analyses.push('生意经营：选择合适的合作伙伴很重要，注意现金流管理。');
    }
    if (focus.includes('windfall')) {
        analyses.push('偏财运势：您的偏财运一般，不建议过度依赖投机获利。');
    }
    
    return analyses;
}

// 生成建议
function generateSuggestions(wuxing, score, focus) {
    const suggestions = [];
    
    // 基础建议
    if (score >= 80) {
        suggestions.push('把握当前的好运势，适当增加投资比例');
        suggestions.push('注意分散风险，不要过于集中投资');
    } else if (score >= 60) {
        suggestions.push('稳健投资为主，避免高风险项目');
        suggestions.push('多学习理财知识，提升财商');
    } else {
        suggestions.push('以储蓄为主，建立应急资金');
        suggestions.push('专注本职工作，提升技能获得加薪');
    }
    
    // 五行建议
    const elementSuggestions = {
        '金': ['佩戴金银饰品增旺财运', '办公桌放置金属装饰品'],
        '木': ['办公室摆放绿色植物', '多去公园树林散步'],
        '水': ['保持环境整洁流通', '可在家中摆放鱼缸'],
        '火': ['穿红色衣物增旺运势', '保持积极向上的心态'],
        '土': ['佩戴玉石类饰品', '脚踏实地稳步发展']
    };
    
    suggestions.push(...elementSuggestions[wuxing.main]);
    
    return suggestions;
}

// 生成幸运月份
function generateLuckMonths() {
    const months = ['一月', '二月', '三月', '四月', '五月', '六月', 
                   '七月', '八月', '九月', '十月', '十一月', '十二月'];
    
    // 随机选择3-4个幸运月份
    const luckyCount = 3 + Math.floor(Math.random() * 2);
    const selected = [];
    
    while (selected.length < luckyCount) {
        const month = months[Math.floor(Math.random() * months.length)];
        if (!selected.includes(month)) {
            selected.push(month);
        }
    }
    
    return selected.sort((a, b) => months.indexOf(a) - months.indexOf(b));
}

// 获取最佳方位
function getBestDirection(wuxing) {
    const directions = {
        '金': '西方、西北方',
        '木': '东方、东南方',
        '水': '北方',
        '火': '南方',
        '土': '中央、西南方'
    };
    
    return directions[wuxing.main] || '根据个人情况而定';
}

// 获取最佳行业
function getBestIndustry(wuxing) {
    const industries = {
        '金': ['金融投资', '珠宝首饰', '机械制造', '汽车行业'],
        '木': ['林业园艺', '文化教育', '医药保健', '纺织服装'],
        '水': ['物流运输', '酒店餐饮', '清洁服务', '水产养殖'],
        '火': ['能源电力', '广告传媒', '化工塑料', '娱乐休闲'],
        '土': ['房地产', '建筑工程', '农业种植', '陶瓷制品']
    };
    
    const list = industries[wuxing.main] || ['服务行业', '贸易行业'];
    return list.slice(0, 3); // 返回前3个
}

// 显示结果
function showResult(result) {
    const modal = document.getElementById('resultModal');
    const content = document.getElementById('resultContent');
    
    if (content) {
    content.innerHTML = generateResultHTML(result);
    }
    
    if (modal) {
    modal.style.display = 'flex';
    
        // 添加强制启动AI分析的按钮
        setTimeout(() => {
            addForceAIAnalysisButton();
        }, 2000);
    }
}

// 添加强制启动AI分析按钮
function addForceAIAnalysisButton() {
    const resultContent = document.getElementById('resultContent');
    if (!resultContent) return;
    
    // 检查是否已经有AI分析按钮
    const existingButton = document.getElementById('forceAIAnalysisBtn');
    if (existingButton) return;
    
    // 创建强制AI分析按钮
    const forceButton = document.createElement('div');
    forceButton.id = 'forceAIAnalysisBtn';
    forceButton.style.cssText = `
        margin-top: 20px;
        padding: 15px;
        background: linear-gradient(135deg, #FFD700, #FFA500);
        border-radius: 10px;
        text-align: center;
        cursor: pointer;
        color: white;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        transition: all 0.3s ease;
    `;
    forceButton.innerHTML = `
        <div style="font-size: 18px; margin-bottom: 5px;">🤖</div>
        <div>点击启动AI深度分析</div>
        <div style="font-size: 12px; opacity: 0.8; margin-top: 5px;">如果AI分析没有自动启动，请点击此按钮</div>
    `;
    
    forceButton.addEventListener('click', () => {
        console.log('🔧 用户手动触发AI分析');
        if (currentUserData && currentBasicResult) {
            startAIAnalysis(currentUserData, currentBasicResult);
        } else {
            console.error('❌ 没有用户数据或基础结果');
            alert('无法启动AI分析，请重新提交表单');
        }
    });
    
    forceButton.addEventListener('mouseenter', () => {
        forceButton.style.transform = 'translateY(-2px)';
        forceButton.style.boxShadow = '0 6px 20px rgba(255, 215, 0, 0.4)';
    });
    
    forceButton.addEventListener('mouseleave', () => {
        forceButton.style.transform = 'translateY(0)';
        forceButton.style.boxShadow = '0 4px 15px rgba(255, 215, 0, 0.3)';
    });
    
    resultContent.appendChild(forceButton);
    console.log('✅ 已添加强制AI分析按钮');
    
    // 添加一个强制显示AI结果的按钮
    setTimeout(() => {
        addForceShowAIResultButton();
    }, 1000);
}



// 强制显示原始AI响应
function forceShowRawAIResponse() {
    console.log('🔧 强制显示原始AI响应...');
    
    // 检查是否有AI结果
    if (!currentBasicResult || !currentBasicResult.aiResult) {
        console.error('❌ 没有结果可显示');
        alert('没有分析结果可显示');
        return;
    }
    
    // 显示AI分析区域
    const aiSection = document.getElementById('aiAnalysisSection');
    if (aiSection) {
        aiSection.style.display = 'block';
        aiSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    
    // 隐藏加载动画
    const loadingDiv = document.getElementById('aiLoading');
    if (loadingDiv) {
        loadingDiv.style.display = 'none';
    }
    
    // 显示原始AI响应
    const contentDiv = document.getElementById('aiContent');
    if (contentDiv) {
        contentDiv.style.display = 'block';
        
        // 获取原始AI响应（如果有的话）
        let rawResponse = '分析已完成，但无法获取原始响应。';
        
        // 尝试从AI结果中获取原始响应
        if (currentBasicResult.aiResult && currentBasicResult.aiResult.wealthAnalysis) {
            const analysis = currentBasicResult.aiResult.wealthAnalysis;
            
            let html = `
                <div class="ai-section ai-summary">
                    <h3>🤖 财运分析结果</h3>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0;">
                        <h4>📊 分析状态</h4>
                        <p>✅ 分析已完成</p>
                        <p>📏 响应长度: 已生成</p>
                        <p>⏰ 生成时间: ${new Date().toLocaleString()}</p>
                    </div>
                </div>
            `;
            
            // 显示评分
            if (analysis.score) {
                html += `
                    <div class="ai-section">
                        <h4>⭐ 财运评分</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #FFD700; text-align: center; padding: 10px;">
                            ${analysis.score}分
                        </div>
                    </div>
                `;
            }
            
            // 显示摘要
            if (analysis.summary) {
                html += `
                    <div class="ai-section">
                        <h4>📋 智能摘要</h4>
                        <p>${analysis.summary}</p>
                    </div>
                `;
            }
            
            // 显示所有章节
            if (analysis.sections) {
                const sectionTitles = {
                    overall: '🌟 整体财运评估',
                    regular: '💼 正财运势分析',
                    investment: '💎 投资理财运势',
                    timing: '⏰ 财运时机把握',
                    direction: '🧭 求财方位指导',
                    advice: '💡 财富增值建议',
                    enhancement: '🔮 开运改善方法'
                };
                
                Object.keys(analysis.sections).forEach(key => {
                    const content = analysis.sections[key];
                    if (content && content.trim()) {
                        html += `
                            <div class="ai-section">
                                <h4>${sectionTitles[key] || '📊 分析内容'}</h4>
                                <p style="white-space: pre-line; line-height: 1.6;">${content}</p>
                            </div>
                        `;
                    }
                });
            }
            
            // 显示建议
            if (analysis.recommendations && analysis.recommendations.length > 0) {
                html += `
                    <div class="ai-section">
                        <h4>💡 建议</h4>
                        <ul style="padding-left: 20px;">
                            ${analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // 显示幸运元素
            if (analysis.luckyElements && analysis.luckyElements.length > 0) {
                html += `
                    <div class="ai-section">
                        <h4>🍀 开运要素</h4>
                        <div class="lucky-elements">
                            ${analysis.luckyElements.map(element => `<span class="lucky-element">${element}</span>`).join('')}
                        </div>
                    </div>
                `;
            }
            
            // 添加成功提示
            html += `
                <div class="ai-section" style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 10px; padding: 15px; margin-top: 20px;">
                    <h4 style="color: #155724; margin: 0;">✅ 分析完成</h4>
                    <p style="color: #155724; margin: 10px 0 0 0;">财运分析已成功完成，请查看上方详细内容。</p>
                </div>
            `;
            
            contentDiv.innerHTML = html;
            console.log('✅ 原始AI响应显示成功');
        } else {
            contentDiv.innerHTML = `
                <div class="ai-section">
                    <h3>⚠️ 分析结果异常</h3>
                    <p>分析已完成，但结果格式异常。请重新尝试分析。</p>
                </div>
            `;
        }
    }
}

// 修改addForceShowAIResultButton函数，添加强制显示原始响应的功能
function addForceShowAIResultButton() {
    const resultContent = document.getElementById('resultContent');
    if (!resultContent) return;
    
    // 检查是否已经有显示AI结果按钮
    const existingButton = document.getElementById('forceShowAIResultBtn');
    if (existingButton) return;
    
    // 创建强制显示AI结果按钮
    const showResultButton = document.createElement('div');
    showResultButton.id = 'forceShowAIResultBtn';
    showResultButton.style.cssText = `
        margin-top: 15px;
        padding: 12px;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        color: white;
        font-weight: bold;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
        transition: all 0.3s ease;
        font-size: 14px;
    `;
    showResultButton.innerHTML = `
        <div style="font-size: 16px; margin-bottom: 3px;">📊</div>
        <div>显示分析结果</div>
        <div style="font-size: 11px; opacity: 0.8; margin-top: 3px;">如果分析已完成但结果未显示，请点击此按钮</div>
    `;
    
    showResultButton.addEventListener('click', () => {
        console.log('🔧 用户手动显示AI结果');
        forceShowRawAIResponse();
    });
    
    showResultButton.addEventListener('mouseenter', () => {
        showResultButton.style.transform = 'translateY(-1px)';
        showResultButton.style.boxShadow = '0 4px 12px rgba(40, 167, 69, 0.4)';
    });
    
    showResultButton.addEventListener('mouseleave', () => {
        showResultButton.style.transform = 'translateY(0)';
        showResultButton.style.boxShadow = '0 3px 10px rgba(40, 167, 69, 0.3)';
    });
    
    resultContent.appendChild(showResultButton);
    console.log('✅ 已添加强制显示结果按钮');
}

// 生成结果HTML
function generateResultHTML(result) {
    return `
        <div class="result-summary">
            <div class="name-info">
                <h3>${result.name} 的财运分析</h3>
                <div class="score-display">
                    <div class="score-circle">
                        <span class="score-number">${result.wealthScore}</span>
                        <span class="score-text">分</span>
                    </div>
                    <p class="score-desc">${getScoreDescription(result.wealthScore)}</p>
                </div>
            </div>
        </div>
        
        <div class="result-section">
            <h4>🔮 五行财运分析</h4>
            <div class="wuxing-display">
                <div class="wuxing-item">
                    <span class="wuxing-label">年柱：</span>
                    <span class="wuxing-value">${result.wuxing.year}</span>
                </div>
                <div class="wuxing-item">
                    <span class="wuxing-label">月柱：</span>
                    <span class="wuxing-value">${result.wuxing.month}</span>
                </div>
                <div class="wuxing-item">
                    <span class="wuxing-label">日柱：</span>
                    <span class="wuxing-value">${result.wuxing.day}</span>
                </div>
                <div class="wuxing-item">
                    <span class="wuxing-label">时柱：</span>
                    <span class="wuxing-value">${result.wuxing.hour}</span>
                </div>
            </div>
        </div>
        
        <div class="result-section">
            <h4>📊 详细分析</h4>
            <div class="analysis-list">
                ${result.analysis.map(item => `<p class="analysis-item">• ${item}</p>`).join('')}
            </div>
        </div>
        
        <div class="result-section">
            <h4>💡 旺财建议</h4>
            <div class="suggestions-list">
                ${result.suggestions.map(item => `<p class="suggestion-item">• ${item}</p>`).join('')}
            </div>
        </div>
        
        <div class="result-section">
            <h4>🌟 财运信息</h4>
            <div class="info-grid">
                <div class="info-item">
                    <strong>幸运月份：</strong>
                    <span>${result.luckMonths.join('、')}</span>
                </div>
                <div class="info-item">
                    <strong>财运方位：</strong>
                    <span>${result.direction}</span>
                </div>
                <div class="info-item">
                    <strong>有利行业：</strong>
                    <span>${result.industry.join('、')}</span>
                </div>
            </div>
        </div>
        
        <style>
            .result-summary { text-align: center; margin-bottom: 25px; }
            .score-display { margin-top: 15px; }
            .score-circle {
                width: 80px; height: 80px; border-radius: 50%;
                background: linear-gradient(135deg, #FFD700, #FF8C00);
                display: inline-flex; align-items: center; justify-content: center;
                flex-direction: column; color: white; margin-bottom: 10px;
            }
            .score-number { font-size: 24px; font-weight: bold; }
            .score-text { font-size: 12px; margin-top: -5px; }
            .score-desc { font-size: 14px; color: #666; }
            
            .result-section { margin-bottom: 20px; }
            .result-section h4 {
                color: #FF8C00; margin-bottom: 10px; font-size: 16px;
                display: flex; align-items: center; gap: 8px;
            }
            
            .wuxing-display { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
            .wuxing-item {
                padding: 8px 12px; background: #f8f9fa; border-radius: 8px;
                display: flex; justify-content: space-between;
            }
            .wuxing-label { color: #666; font-size: 14px; }
            .wuxing-value { color: #FF8C00; font-weight: bold; }
            
            .analysis-list, .suggestions-list { line-height: 1.6; }
            .analysis-item, .suggestion-item {
                margin-bottom: 8px; color: #333; font-size: 14px;
            }
            
            .info-grid { display: flex; flex-direction: column; gap: 10px; }
            .info-item {
                padding: 10px; background: #f8f9fa; border-radius: 8px;
                font-size: 14px;
            }
            .info-item strong { color: #FF8C00; margin-right: 8px; }
        </style>
    `;
}

// 获取分数描述
function getScoreDescription(score) {
    if (score >= 90) return '财运极佳';
    if (score >= 80) return '财运很好';
    if (score >= 70) return '财运良好';
    if (score >= 60) return '财运一般';
    if (score >= 50) return '财运较弱';
    return '财运需改善';
}

// 获取财运分数描述（AI专用）
function getWealthScoreDescription(score) {
    if (score >= 95) return '财运极佳，天赋异禀';
    if (score >= 90) return '财运很好，前景光明';
    if (score >= 85) return '财运良好，稳步上升';
    if (score >= 80) return '财运不错，有所收获';
    if (score >= 75) return '财运中等，需要努力';
    if (score >= 70) return '财运一般，谨慎理财';
    if (score >= 60) return '财运较弱，保守为主';
    return '财运需改善，积德行善';
}

// 关闭结果
function closeResult() {
    const modal = document.getElementById('resultModal');
    modal.style.display = 'none';
}

// 保存结果
function saveResult() {
    const result = localStorage.getItem('wealthResult');
    if (result) {
        const blob = new Blob([result], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '财运预测结果.json';
        a.click();
        URL.revokeObjectURL(url);
    }
}

// 分享结果
function shareResult() {
    const result = JSON.parse(localStorage.getItem('wealthResult') || '{}');
    if (result.name) {
        const shareText = `我在易海堂测了财运，得分${result.wealthScore}分！五行主元素是${result.wuxing?.main}，财运${getScoreDescription(result.wealthScore)}！`;
        
        if (navigator.share) {
            navigator.share({
                title: '财运预测结果',
                text: shareText,
                url: window.location.href
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareText).then(() => {
                alert('结果已复制到剪贴板');
            });
        }
    }
}

// 页面卸载时清理动画
window.addEventListener('beforeunload', function() {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
});

// ========== AI功能相关函数 ==========

// 启动AI深度分析
async function startAIAnalysis(userData, basicResult) {
    console.log('🚀 启动AI财运分析...');
    console.log('用户数据:', userData);
    console.log('基础结果:', basicResult);
    
    // 显示AI分析区域
    const aiSection = document.getElementById('aiAnalysisSection');
    console.log('🔍 AI分析区域元素:', aiSection);
    
    if (aiSection) {
        aiSection.style.display = 'block';
        console.log('✅ AI分析区域已显示');
        
        // 强制滚动到AI分析区域
        setTimeout(() => {
            try {
            aiSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
                console.log('✅ 已滚动到AI分析区域');
            } catch (error) {
                console.error('❌ 滚动到AI分析区域失败:', error);
            }
        }, 500);
    } else {
        console.error('❌ 找不到AI分析区域元素');
        // 尝试创建AI分析区域
        createAIAnalysisSection();
    }
    
    // 检查AI服务是否可用
    console.log('🔍 检查AI服务状态:');
    console.log('- wealthAnalysisAI:', !!wealthAnalysisAI);
    console.log('- window.aIService:', !!window.aIService);
    console.log('- AI_CONFIG:', window.AI_CONFIG);
    
    if (!wealthAnalysisAI) {
        console.error('❌ AI分析服务未初始化');
        showAIError('AI分析服务未初始化');
        return;
    }
    
    try {
        // 显示AI加载动画
        showAILoading();
        
        console.log('📞 开始调用AI分析...');
        
        // 执行AI分析
        const aiResult = await wealthAnalysisAI.analyzeWealthWithAI(userData);
        
        console.log('📊 AI分析结果:', aiResult);
        
        if (!aiResult || !aiResult.wealthAnalysis) {
            console.error('❌ AI分析结果为空');
            throw new Error('AI分析结果为空');
        }
        
        // 显示AI分析结果
        displayAIResult(aiResult);
        
        // 更新当前结果
        if (currentBasicResult) {
            currentBasicResult.aiResult = aiResult;
        }
        
        console.log('✅ AI分析完成');
        
    } catch (error) {
        console.error('❌ AI分析失败:', error);
        showAIError(error.message || '分析过程中发生错误');
    }
}

// 创建AI分析区域（如果不存在）
function createAIAnalysisSection() {
    console.log('🔧 尝试创建AI分析区域...');
    
    const existingSection = document.getElementById('aiAnalysisSection');
    if (existingSection) {
        console.log('✅ AI分析区域已存在');
        return;
    }
    
    // 创建AI分析区域
    const aiSection = document.createElement('div');
    aiSection.id = 'aiAnalysisSection';
    aiSection.className = 'ai-analysis-section';
    aiSection.style.display = 'block';
    
    aiSection.innerHTML = `
        <div class="ai-container">
            <div class="ai-header">
                <h2>深度财运分析</h2>
                <p class="ai-subtitle">由 <span id="aiServiceType">易海堂</span> 提供专业分析</p>
            </div>
            
            <!-- AI加载动画 -->
            <div id="aiLoading" class="ai-loading">
                <div class="ai-loading-steps">
                    <div class="ai-step" id="aiStep1">
                        <div class="step-icon">📊</div>
                        <div class="step-text">分析财运格局</div>
                    </div>
                    <div class="ai-step" id="aiStep2">
                        <div class="step-icon">💎</div>
                        <div class="step-text">计算投资潜力</div>
                    </div>
                    <div class="ai-step" id="aiStep3">
                        <div class="step-icon">🎯</div>
                        <div class="step-text">预测财运趋势</div>
                    </div>
                    <div class="ai-step" id="aiStep4">
                        <div class="step-icon">🔮</div>
                        <div class="step-text">生成开运建议</div>
                    </div>
                </div>
                <div class="ai-loading-text">正在启动财运分析...</div>
                <div class="ai-progress-bar">
                    <div class="ai-progress-fill"></div>
                </div>
            </div>
            
            <!-- AI分析结果 -->
            <div id="aiContent" class="ai-content" style="display: none;">
                <!-- AI分析内容将通过JavaScript动态生成 -->
            </div>
        </div>
    `;
    
    // 添加到页面
    const main = document.querySelector('main');
    if (main) {
        main.appendChild(aiSection);
        console.log('✅ AI分析区域已创建并添加到页面');
    } else {
        console.error('❌ 找不到main元素，无法添加AI分析区域');
    }
}

// 显示AI加载动画
function showAILoading() {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';
    
    // 模拟加载步骤
    const steps = ['aiStep1', 'aiStep2', 'aiStep3', 'aiStep4'];
    const statusTexts = [
        '正在分析财运格局...',
        '正在计算投资潜力...',
        '正在预测财运趋势...',
        '正在生成开运建议...'
    ];
    
    let currentStep = 0;
    
    const updateStep = () => {
        // 清除之前的状态
        steps.forEach(stepId => {
            const stepEl = document.getElementById(stepId);
            if (stepEl) {
                stepEl.classList.remove('active', 'completed');
            }
        });
        
        // 标记完成的步骤
        for (let i = 0; i < currentStep; i++) {
            const stepEl = document.getElementById(steps[i]);
            if (stepEl) {
                stepEl.classList.add('completed');
            }
        }
        
        // 标记当前步骤
        if (currentStep < steps.length) {
            const currentEl = document.getElementById(steps[currentStep]);
            if (currentEl) {
                currentEl.classList.add('active');
            }
            
            // 更新状态文本
            const statusEl = document.querySelector('.ai-loading-text');
            if (statusEl) {
                statusEl.textContent = statusTexts[currentStep];
            }
        }
        
        currentStep++;
        
        if (currentStep <= steps.length) {
            setTimeout(updateStep, 2000);
        }
    };
    
    updateStep();
}

// 显示AI分析结果
function displayAIResult(aiResult) {
    try {
        console.log('🎨 开始显示AI分析结果...');
        console.log('AI结果对象:', aiResult);
        
        const loadingDiv = document.getElementById('aiLoading');
        const contentDiv = document.getElementById('aiContent');
        
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
            console.log('✅ 隐藏AI加载动画');
        }
        
        if (contentDiv) {
            contentDiv.style.display = 'block';
            
            // 尝试生成AI内容HTML
            try {
                const html = generateAIContentHTML(aiResult.wealthAnalysis);
                contentDiv.innerHTML = html;
                console.log('✅ AI内容HTML生成成功');
            } catch (error) {
                console.error('❌ AI内容HTML生成失败:', error);
                // 如果解析失败，显示简化的结果
                contentDiv.innerHTML = generateSimpleAIContent(aiResult);
                console.log('✅ 显示简化AI内容');
            }
        } else {
            console.error('❌ 找不到AI内容容器');
        }
    } catch (error) {
        console.error('❌ 显示AI结果失败:', error);
        showAIError('显示分析结果时发生错误');
    }
}

// 生成简化的AI内容
function generateSimpleAIContent(aiResult) {
    console.log('🔧 生成简化AI内容...');
    
    let html = `
        <div class="ai-section ai-summary">
            <h3>🤖 AI财运分析结果</h3>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0;">
                <h4>📊 分析状态</h4>
                <p>✅ AI分析已完成</p>
                <p>📏 响应长度: ${aiResult.wealthAnalysis ? '已生成' : '未知'}</p>
                <p>⏰ 生成时间: ${new Date().toLocaleString()}</p>
            </div>
        </div>
    `;
    
    // 如果有摘要，显示摘要
    if (aiResult.wealthAnalysis && aiResult.wealthAnalysis.summary) {
        html += `
            <div class="ai-section">
                <h4>📋 智能摘要</h4>
                <p>${aiResult.wealthAnalysis.summary}</p>
            </div>
        `;
    }
    
    // 如果有评分，显示评分
    if (aiResult.wealthAnalysis && aiResult.wealthAnalysis.score) {
        html += `
            <div class="ai-section">
                <h4>⭐ AI财运评分</h4>
                <div style="font-size: 24px; font-weight: bold; color: #FFD700; text-align: center; padding: 10px;">
                    ${aiResult.wealthAnalysis.score}分
                </div>
            </div>
        `;
    }
    
    // 如果有建议，显示建议
    if (aiResult.wealthAnalysis && aiResult.wealthAnalysis.recommendations && aiResult.wealthAnalysis.recommendations.length > 0) {
        html += `
            <div class="ai-section">
                <h4>💡 AI建议</h4>
                <ul style="padding-left: 20px;">
                    ${aiResult.wealthAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    // 如果有详细章节，显示第一个有内容的章节
    if (aiResult.wealthAnalysis && aiResult.wealthAnalysis.sections) {
        const sections = aiResult.wealthAnalysis.sections;
        const sectionTitles = {
            overall: '🌟 整体财运评估',
            regular: '💼 正财运势分析',
            investment: '💎 投资理财运势',
            timing: '⏰ 财运时机把握',
            direction: '🧭 求财方位指导',
            advice: '💡 财富增值建议',
            enhancement: '🔮 开运改善方法'
        };
        
        for (const [key, content] of Object.entries(sections)) {
            if (content && content.trim()) {
                html += `
                    <div class="ai-section">
                        <h4>${sectionTitles[key] || '📊 分析内容'}</h4>
                        <p style="white-space: pre-line; line-height: 1.6;">${content}</p>
                    </div>
                `;
                break; // 只显示第一个有内容的章节
            }
        }
    }
    
    // 添加成功提示
    html += `
        <div class="ai-section" style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 10px; padding: 15px; margin-top: 20px;">
            <h4 style="color: #155724; margin: 0;">✅ AI分析完成</h4>
            <p style="color: #155724; margin: 10px 0 0 0;">AI财运分析已成功完成，请查看上方详细内容。</p>
        </div>
    `;
    
    return html;
}

// 生成AI内容HTML
function generateAIContentHTML(wealthAnalysis) {
    try {
        let html = '';

        // AI评分显示
        if (wealthAnalysis.score) {
            html += `
                <div class="ai-section ai-summary">
                    <div class="ai-score-display">
                        <div class="ai-score-circle">${wealthAnalysis.score}分</div>
                        <div class="ai-score-info">
                            <div class="ai-score-label">AI财运评分</div>
                            <div class="ai-score-desc">${getWealthScoreDescription(wealthAnalysis.score)}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // AI摘要
        if (wealthAnalysis.summary) {
            html += `
                <div class="ai-section ai-summary">
                    <h4>📋 智能摘要</h4>
                    <p style="line-height: 1.6; color: #333;">${wealthAnalysis.summary}</p>
                </div>
            `;
        }

        // 详细分析章节
        if (wealthAnalysis.sections) {
            const sectionTitles = {
                overall: '🌟 整体财运评估',
                regular: '💼 正财运势分析',
                investment: '💎 投资理财运势',
                timing: '⏰ 财运时机把握',
                direction: '🧭 求财方位指导',
                advice: '💡 财富增值建议',
                enhancement: '🔮 开运改善方法'
            };

            Object.keys(wealthAnalysis.sections).forEach(key => {
                const content = wealthAnalysis.sections[key];
                if (content && content.trim()) {
                    html += `
                        <div class="ai-section">
                            <h4>${sectionTitles[key] || '📊 分析内容'}</h4>
                            <p style="line-height: 1.6; color: #333; white-space: pre-line;">${content}</p>
                        </div>
                    `;
                }
            });
        }

        // 月度财运预测（新增）
        if (wealthAnalysis.monthlyForecast) {
            html += `
                <div class="ai-section">
                    <h4>📅 月度财运预测</h4>
                    <div class="monthly-forecast">
            `;

            if (wealthAnalysis.monthlyForecast.bestMonths && wealthAnalysis.monthlyForecast.bestMonths.length > 0) {
                html += `
                    <div class="forecast-item good">
                        <span class="forecast-label">🌟 财运旺盛月份：</span>
                        <span class="forecast-value">${wealthAnalysis.monthlyForecast.bestMonths.join('、')}</span>
                    </div>
                `;
            }

            if (wealthAnalysis.monthlyForecast.cautionMonths && wealthAnalysis.monthlyForecast.cautionMonths.length > 0) {
                html += `
                    <div class="forecast-item caution">
                        <span class="forecast-label">⚠️ 谨慎理财月份：</span>
                        <span class="forecast-value">${wealthAnalysis.monthlyForecast.cautionMonths.join('、')}</span>
                    </div>
                `;
            }

            html += `
                    </div>
                </div>
            `;
        }

        // 风险提醒（新增）
        if (wealthAnalysis.riskWarnings && wealthAnalysis.riskWarnings.length > 0) {
            html += `
                <div class="ai-section ai-warnings">
                    <h4>⚠️ 风险提醒</h4>
                    <ul class="ai-warning-list">
                        ${wealthAnalysis.riskWarnings.map(warning => `<li style="color: #e74c3c; margin-bottom: 8px;">${warning}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        // 开运要素
        if (wealthAnalysis.luckyElements && wealthAnalysis.luckyElements.length > 0) {
            html += `
                <div class="ai-section">
                    <h4>🍀 开运要素</h4>
                    <div class="lucky-elements">
                        ${wealthAnalysis.luckyElements.map(element => `<span class="lucky-element">${element}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        // AI建议
        if (wealthAnalysis.recommendations && wealthAnalysis.recommendations.length > 0) {
            html += `
                <div class="ai-section ai-recommendations">
                    <h4>🎯 AI专业建议</h4>
                    <ul class="ai-recommendation-list">
                        ${wealthAnalysis.recommendations.map(rec => `<li style="margin-bottom: 8px; line-height: 1.5;">${rec}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        html += `
            <div class="ai-section ai-footer">
                <p style="color: #666; font-size: 14px; line-height: 1.5;">本分析由易海堂提供，仅供参考。建议结合个人实际情况综合考虑。</p>
                <p style="color: #999; font-size: 12px;">分析时间：${new Date(wealthAnalysis.timestamp).toLocaleString()}</p>
            </div>
        `;

        // 添加样式
        html += `
            <style>
                .ai-section {
                    margin-bottom: 20px;
                    padding: 15px;
                    background: #fff;
                    border-radius: 10px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }
                .ai-section h4 {
                    margin: 0 0 10px 0;
                    color: #FF8C00;
                    font-size: 16px;
                }
                .ai-score-display {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }
                .ai-score-circle {
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #FFD700, #FF8C00);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                }
                .monthly-forecast {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                .forecast-item {
                    padding: 10px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                .forecast-item.good {
                    background: #d4edda;
                    border: 1px solid #c3e6cb;
                }
                .forecast-item.caution {
                    background: #f8d7da;
                    border: 1px solid #f5c6cb;
                }
                .forecast-label {
                    font-weight: bold;
                    min-width: 120px;
                }
                .lucky-elements {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                }
                .lucky-element {
                    background: linear-gradient(135deg, #FFD700, #FFA500);
                    color: white;
                    padding: 5px 12px;
                    border-radius: 15px;
                    font-size: 14px;
                    font-weight: bold;
                }
                .ai-recommendation-list {
                    padding-left: 20px;
                }
                .ai-warning-list {
                    padding-left: 20px;
                }
            </style>
        `;

        return html;
    } catch (error) {
        console.error('生成AI内容HTML失败:', error);
        return `<div class="ai-section"><h4>⚠️ 内容生成错误</h4><p>生成分析内容时发生错误: ${error.message || '未知错误'}</p></div>`;
    }
}

// 获取财运评分描述
function getWealthScoreDescription(score) {
    if (score >= 90) return '评估：财运极佳';
    if (score >= 80) return '评估：财运优秀';
    if (score >= 70) return '评估：财运良好';
    if (score >= 60) return '评估：财运一般';
    return '评估：需要改善';
}

// 显示AI错误
function showAIError(errorMessage) {
    const loadingDiv = document.getElementById('aiLoading');
    const contentDiv = document.getElementById('aiContent');
    
    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) {
        contentDiv.style.display = 'block';
        contentDiv.innerHTML = `
            <div class="ai-section" style="background: rgba(220, 53, 69, 0.2); border-left-color: #dc3545;">
                <h4>⚠️ 分析暂时不可用</h4>
                <p>原因：${errorMessage}</p>
                <p>已为您提供基础的财运分析，如需深度分析，请稍后重试或联系客服。</p>
            </div>
        `;
    }
}

// ====== 财运日期选择器功能 ======

// 财运日期选择器变量
let currentYear = new Date().getFullYear();
let currentMonth = new Date().getMonth() + 1;
let selectedDate = null;
// 年份选择器变量
let yearRangeStart = Math.floor(currentYear / 10) * 10;

// 初始化财运日期选择器
function initWealthDatePicker() {
    try {
        const today = new Date();
        currentYear = today.getFullYear();
        currentMonth = today.getMonth() + 1;
        
        // 添加点击背景关闭功能
        const modal = document.getElementById('wealthDateModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeWealthDatePicker();
                }
            });
        }
        
        console.log('🗓️ 财运日期选择器初始化完成');
    } catch (error) {
        console.error('❌ 财运日期选择器初始化失败:', error);
    }
}

// 打开财运日期选择器
function openWealthDatePicker() {
    const modal = document.getElementById('wealthDateModal');
    const wealthDatePicker = document.getElementById('wealthDatePicker');
    
    if (modal) {
        modal.style.display = 'flex';
        if (wealthDatePicker) wealthDatePicker.classList.add('active');
        
        // 生成日历
        generateCalendar();
        updateMonthDisplay();
        
        console.log('📅 财运日期选择器已打开');
    }
}

// 关闭财运日期选择器
function closeWealthDatePicker() {
    const modal = document.getElementById('wealthDateModal');
    const wealthDatePicker = document.getElementById('wealthDatePicker');
    
    if (modal) {
        modal.style.display = 'none';
        if (wealthDatePicker) wealthDatePicker.classList.remove('active');
        
        console.log('📅 财运日期选择器已关闭');
    }
}

// 上一个月
function prevMonth() {
    currentMonth--;
    if (currentMonth < 1) {
        currentMonth = 12;
        currentYear--;
    }
    generateCalendar();
    updateMonthDisplay();
}

// 下一个月
function nextMonth() {
    currentMonth++;
    if (currentMonth > 12) {
        currentMonth = 1;
        currentYear++;
    }
    generateCalendar();
    updateMonthDisplay();
}

// 生成日历
function generateCalendar() {
    const calendarGrid = document.getElementById('calendarGrid');
    if (!calendarGrid) return;
    
    calendarGrid.innerHTML = '';
    
    // 获取当前月份第一天
    const firstDay = new Date(currentYear, currentMonth - 1, 1);
    // 获取当前月份最后一天
    const lastDay = new Date(currentYear, currentMonth, 0);
    // 获取上个月最后一天
    const prevLastDay = new Date(currentYear, currentMonth - 1, 0);
    
    // 计算第一天是星期几（0=周日，1=周一...）
    let firstDayOfWeek = firstDay.getDay();
    if (firstDayOfWeek === 0) firstDayOfWeek = 7; // 调整为周一开始
    
    // 添加上个月的日期
    for (let i = firstDayOfWeek - 1; i > 0; i--) {
        const day = prevLastDay.getDate() - i + 1;
        const dayElement = createDayElement(day, true, currentMonth === 1 ? currentYear - 1 : currentYear, currentMonth === 1 ? 12 : currentMonth - 1);
        calendarGrid.appendChild(dayElement);
    }
    
    // 添加当前月份的日期
    for (let day = 1; day <= lastDay.getDate(); day++) {
        const dayElement = createDayElement(day, false, currentYear, currentMonth);
        calendarGrid.appendChild(dayElement);
    }
    
    // 添加下个月的日期（填满42格）
    const totalCells = 42;
    const filledCells = calendarGrid.children.length;
    const remainingCells = totalCells - filledCells;
    
    for (let day = 1; day <= remainingCells; day++) {
        const dayElement = createDayElement(day, true, currentMonth === 12 ? currentYear + 1 : currentYear, currentMonth === 12 ? 1 : currentMonth + 1);
        calendarGrid.appendChild(dayElement);
    }
}

// 创建日期元素
function createDayElement(day, isOtherMonth, year, month) {
    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day';
    dayElement.textContent = day;
    dayElement.dataset.year = year;
    dayElement.dataset.month = month;
    dayElement.dataset.day = day;
    
    if (isOtherMonth) {
        dayElement.classList.add('other-month');
    }
    
    // 检查是否为今天
    const today = new Date();
    if (year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate()) {
        dayElement.classList.add('today');
    }
    
    // 检查是否为选中日期
    if (selectedDate && selectedDate.year === year && selectedDate.month === month && selectedDate.day === day) {
        dayElement.classList.add('selected');
    }
    
    // 添加点击事件
    dayElement.addEventListener('click', () => {
        selectDate(year, month, day);
    });
    
    return dayElement;
}

// 更新月份显示
function updateMonthDisplay() {
    const monthDisplay = document.getElementById('currentMonthDisplay');
    if (monthDisplay) {
        monthDisplay.textContent = `${currentYear}年${currentMonth.toString().padStart(2, '0')}月`;
    }
}

// 选择日期
function selectDate(year, month, day) {
    selectedDate = { year, month, day };
    
    // 更新隐藏字段
    const birthDate = document.getElementById('birthDate');
    if (birthDate) {
        birthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }
    
    // 更新显示
    updateDateDisplay();
    updateModalDateDisplay();
    
    // 重新生成日历以更新选中状态
    generateCalendar();
    
    // 延迟关闭模态框
    setTimeout(() => {
        closeWealthDatePicker();
    }, 300);
}

// 清除日期
function clearDate() {
    selectedDate = null;
    
    const birthDate = document.getElementById('birthDate');
    if (birthDate) birthDate.value = '';
    
    updateDateDisplay();
    updateModalDateDisplay();
    generateCalendar();
    
    setTimeout(() => {
        closeWealthDatePicker();
    }, 300);
}

// 选择今天
function selectToday() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const day = today.getDate();
    
    currentYear = year;
    currentMonth = month;
    
    selectDate(year, month, day);
    updateMonthDisplay();
    generateCalendar();
}

// 确认日期选择
function confirmDate() {
    if (selectedDate) {
        closeWealthDatePicker();
    } else {
        alert('请先选择一个日期');
    }
}

// 更新日期显示（主界面）
function updateDateDisplay() {
    const dateDisplayText = document.getElementById('dateDisplayText');
    if (dateDisplayText) {
        if (selectedDate) {
            const { year, month, day } = selectedDate;
            dateDisplayText.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
            dateDisplayText.classList.add('selected');
        } else {
            dateDisplayText.textContent = '请选择日期';
            dateDisplayText.classList.remove('selected');
        }
    }
}

// 更新模态框日期显示
function updateModalDateDisplay() {
    const modalDateDisplay = document.getElementById('modalDateDisplay');
    if (modalDateDisplay) {
        if (selectedDate) {
            const { year, month, day } = selectedDate;
            modalDateDisplay.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
            modalDateDisplay.classList.add('selected');
        } else {
            modalDateDisplay.textContent = '请选择日期';
            modalDateDisplay.classList.remove('selected');
        }
    }
}

// 显示年份选择器
function showYearSelector() {
    const yearSelector = document.getElementById('yearSelector');
    const calendarContainer = document.querySelector('.calendar-container');
    const calendarFooter = document.querySelector('.calendar-footer');
    const monthNavigation = document.querySelector('.month-navigation');
    
    if (yearSelector) {
        yearSelector.style.display = 'block';
        if (calendarContainer) calendarContainer.style.display = 'none';
        if (calendarFooter) calendarFooter.style.display = 'none';
        if (monthNavigation) monthNavigation.style.display = 'none';
        
        generateYearGrid();
        updateYearRangeDisplay();
    }
}

// 隐藏年份选择器
function hideYearSelector() {
    const yearSelector = document.getElementById('yearSelector');
    const calendarContainer = document.querySelector('.calendar-container');
    const calendarFooter = document.querySelector('.calendar-footer');
    const monthNavigation = document.querySelector('.month-navigation');
    
    if (yearSelector) {
        yearSelector.style.display = 'none';
        if (calendarContainer) calendarContainer.style.display = 'block';
        if (calendarFooter) calendarFooter.style.display = 'flex';
        if (monthNavigation) monthNavigation.style.display = 'flex';
    }
}

// 改变年份范围
function changeYearRange(delta) {
    yearRangeStart += delta;
    if (yearRangeStart < 1900) yearRangeStart = 1900;
    if (yearRangeStart > 2100) yearRangeStart = 2100;
    
    generateYearGrid();
    updateYearRangeDisplay();
}

// 更新年份范围显示
function updateYearRangeDisplay() {
    const yearRangeDisplay = document.getElementById('yearRangeDisplay');
    if (yearRangeDisplay) {
        yearRangeDisplay.textContent = `${yearRangeStart}-${yearRangeStart + 9}`;
    }
}

// 生成年份网格
function generateYearGrid() {
    const yearGrid = document.getElementById('yearGrid');
    if (!yearGrid) return;
    
    yearGrid.innerHTML = '';
    const currentYearValue = new Date().getFullYear();
    
    for (let i = 0; i < 10; i++) {
        const year = yearRangeStart + i;
        const yearItem = document.createElement('div');
        yearItem.className = 'year-item';
        yearItem.textContent = year;
        yearItem.dataset.year = year;
        
        // 添加当前年份标记
        if (year === currentYearValue) {
            yearItem.classList.add('current');
        }
        
        // 添加选中年份标记
        if (year === currentYear) {
            yearItem.classList.add('selected');
        }
        
        // 添加点击事件
        yearItem.addEventListener('click', () => {
            selectYear(year);
        });
        
        yearGrid.appendChild(yearItem);
    }
}

// 选择年份
function selectYear(year) {
    currentYear = year;
    
    // 更新月份显示
    updateMonthDisplay();
    
    // 重新生成日历
    generateCalendar();
    
    // 隐藏年份选择器
    hideYearSelector();
}