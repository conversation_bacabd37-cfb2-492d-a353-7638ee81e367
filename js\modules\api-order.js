// 订单API调用模块
const API_BASE = '/api/orders';

// 获取token（优先使用authService，兼容其他方式）
function getToken() {
    console.log('🔍 开始获取认证Token...');
    
    // 优先使用统一认证服务获取token
    if (window.unifiedAuthService && window.unifiedAuthService.getToken) {
        const token = window.unifiedAuthService.getToken();
        if (token) {
            console.log('✅ 从统一认证服务获取Token成功:', token.substring(0, 20) + '...');
            return token;
        } else {
            console.warn('⚠️ 统一认证服务返回空Token');
        }
    }
    
    // 降级使用authService获取token
    if (window.authService && window.authService.getToken) {
        const token = window.authService.getToken();
        if (token) {
            console.log('✅ 从authService获取Token成功:', token.substring(0, 20) + '...');
            return token;
        } else {
            console.warn('⚠️ authService返回空Token');
        }
    }
    
    // 兼容其他token存储方式
    const localToken = localStorage.getItem('unified_token');
    const sessionToken = sessionStorage.getItem('token');
    const localToken2 = localStorage.getItem('token');
    
    if (localToken) {
        console.log('✅ 从localStorage获取unified_token成功:', localToken.substring(0, 20) + '...');
        return localToken;
    }
    
    if (sessionToken) {
        console.log('✅ 从sessionStorage获取token成功:', sessionToken.substring(0, 20) + '...');
        return sessionToken;
    }
    
    if (localToken2) {
        console.log('✅ 从localStorage获取token成功:', localToken2.substring(0, 20) + '...');
        return localToken2;
    }
    
    console.error('❌ 未找到任何认证Token');
    return null;
}

// 获取会员ID
function getMemberId() {
    console.log('🔍 开始获取会员ID...');
    
    // 优先使用统一认证服务获取会员ID
    if (window.unifiedAuthService && window.unifiedAuthService.isLoggedIn()) {
        const userType = window.unifiedAuthService.getUserType();
        console.log('📋 统一认证服务用户类型:', userType);
        
        if (userType === 'member') {
            const currentUser = window.unifiedAuthService.getCurrentUser();
            if (currentUser && currentUser.id) {
                console.log('✅ 从统一认证服务获取会员ID:', currentUser.id);
                return currentUser.id;
            } else {
                console.warn('⚠️ 统一认证服务用户信息不完整:', currentUser);
            }
        } else {
            console.warn('⚠️ 统一认证服务用户类型不是member:', userType);
        }
    } else {
        console.warn('⚠️ 统一认证服务未登录或不存在');
    }
    
    // 降级使用memberService获取会员ID
    if (window.memberService && window.memberService.getMemberId) {
        const memberId = window.memberService.getMemberId();
        if (memberId) {
            console.log('✅ 从会员服务获取会员ID:', memberId);
            return memberId;
        } else {
            console.warn('⚠️ 会员服务返回空会员ID');
        }
    }
    
    // 兼容其他会员ID存储方式
    const localMemberId = localStorage.getItem('member_id');
    const sessionMemberId = sessionStorage.getItem('member_id');
    
    if (localMemberId) {
        console.log('✅ 从localStorage获取会员ID:', localMemberId);
        return localMemberId;
    }
    
    if (sessionMemberId) {
        console.log('✅ 从sessionStorage获取会员ID:', sessionMemberId);
        return sessionMemberId;
    }
    
    console.error('❌ 未找到任何会员ID');
    return null;
}

// 检查登录状态
function checkLoginStatus() {
    console.log('🔍 检查登录状态...');
    
    const token = getToken();
    const memberId = getMemberId();
    
    return {
        hasToken: !!token,
        hasMemberId: !!memberId,
        token: token,
        memberId: memberId
    };
}

// 通用请求方法
async function request(url, options = {}) {
    console.log('🌐 发起API请求:', url);
    
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        ...options
    };

    // 添加认证token
    const token = getToken();
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
        console.log('✅ 已添加认证头:', `Bearer ${token.substring(0, 20)}...`);
    } else {
        console.warn('⚠️ 未找到认证token，请求可能被拒绝');
    }

    try {
        console.log('📤 发送请求配置:', {
            url: url,
            method: config.method || 'GET',
            headers: config.headers
        });
        
        const response = await fetch(url, config);
        
        console.log('📥 收到响应:', {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ HTTP错误:', response.status, errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }
        
        const data = await response.json();
        console.log('📋 响应数据:', data);
        
        // 检查后端返回的Result格式
        if (data && typeof data === 'object' && 'code' in data) {
            if (data.code === 200) {
                console.log('✅ 请求成功');
                return data.data;
            } else {
                console.error('❌ 业务错误:', data.message);
                throw new Error(data.message || '请求失败');
            }
        }
        
        return data;
    } catch (error) {
        console.error('❌ API请求失败:', error);
        
        // 如果是认证错误，提供更详细的诊断信息
        if (error.message.includes('401') || error.message.includes('未登录')) {
            console.error('🔐 认证失败诊断:');
            const loginStatus = checkLoginStatus();
            console.error('  - 当前登录状态:', loginStatus);
            
            // 建议用户重新登录
            if (confirm('检测到登录状态异常，是否跳转到登录页面？')) {
                window.location.href = '/pages/member/login/index.html';
            }
        }
        
        throw error;
    }
}

// 订单API方法
const OrderAPI = {
    // 创建订单
    async createOrder(orderData) {
        console.log('🛒 开始创建订单...');
        
        // 检查登录状态
        const loginStatus = checkLoginStatus();
        if (!loginStatus.hasToken) {
            throw new Error('用户未登录，请先登录');
        }
        
        // 后端现在从Sa-Token获取用户ID，不需要前端传递memberId
        // 但为了兼容性，保留memberId字段用于调试
        const memberId = getMemberId();
        if (memberId) {
            orderData.memberId = memberId;
            console.log('📝 调试信息 - 前端会员ID:', memberId);
        } else {
            console.warn('⚠️ 调试信息 - 未找到会员ID');
        }
        
        console.log('📦 订单数据:', orderData);
        
        return await request(`${API_BASE}`, {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
    },

    // 获取订单列表（兼容旧方法名）
    async getOrders() {
        console.log('📋 获取订单列表...');
        return await request(`${API_BASE}`);
    },

    // 获取订单列表（新方法名）
    async getOrderList() {
        console.log('📋 获取订单列表...');
        return await request(`${API_BASE}`);
    },

    // 获取会员订单列表
    async getMemberOrders() {
        console.log('👤 获取会员订单列表...');
        const memberId = getMemberId();
        if (!memberId) {
            throw new Error('未找到会员ID，无法获取会员订单');
        }
        return await request(`${API_BASE}/member/${memberId}`);
    },

    // 获取订单详情
    async getOrderDetail(orderId) {
        console.log('📄 获取订单详情:', orderId);
        return await request(`${API_BASE}/${orderId}`);
    },

    // 支付订单
    async payOrder(orderId) {
        console.log('💳 支付订单:', orderId);
        return await request(`${API_BASE}/${orderId}/pay`, {
            method: 'POST'
        });
    },

    // 取消订单
    async cancelOrder(orderId) {
        console.log('❌ 取消订单:', orderId);
        return await request(`${API_BASE}/${orderId}/cancel`, {
            method: 'POST'
        });
    },

    // 获取订单统计信息
    async getOrderStats() {
        console.log('📊 获取订单统计信息...');
        const memberId = getMemberId();
        if (!memberId) {
            console.warn('⚠️ 未找到会员ID，返回默认统计信息');
            return {
                totalOrders: 0,
                totalAmount: 0,
                pendingOrders: 0,
                completedOrders: 0
            };
        }
        return await request(`${API_BASE}/stats/${memberId}`);
    },

    // 诊断方法 - 用于调试
    diagnoseLoginStatus() {
        return checkLoginStatus();
    }
};

// 导出API对象
window.OrderAPI = OrderAPI; 