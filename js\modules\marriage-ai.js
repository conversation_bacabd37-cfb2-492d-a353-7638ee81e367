/**
 * 八字合婚AI分析模块
 * 基于传统命理学分析两个人的八字合婚情况
 */
class MarriageAnalysisAI {
    constructor() {
        this.initializeKnowledgeBase();
        // 初始化认证服务
        if (window.initializeAuthServices) {
            window.initializeAuthServices();
        }
    }

    // 初始化合婚知识库
    initializeKnowledgeBase() {
        this.marriageKnowledge = {
            // 五行相生相克
            wuxing: {
                sheng: { // 相生关系
                    木: '火', 火: '土', 土: '金', 金: '水', 水: '木'
                },
                ke: { // 相克关系
                    木: '土', 土: '水', 水: '火', 火: '金', 金: '木'
                }
            },
            
            // 十神关系
            shishen: {
                // 日主与配偶关系
                relationship: {
                    bijian: { name: '比肩', score: 60, description: '志同道合，但易有竞争' },
                    jiecai: { name: '劫财', score: 55, description: '性格相似，但易争执' },
                    shishan: { name: '食神', score: 85, description: '情投意合，生活多彩' },
                    shangguang: { name: '伤官', score: 70, description: '才华相惜，但易冲突' },
                    piancan: { name: '偏财', score: 80, description: '互补性强，财运良好' },
                    zhengcai: { name: '正财', score: 90, description: '稳定和谐，家庭美满' },
                    qisha: { name: '七杀', score: 65, description: '互相欣赏，但易有压力' },
                    zhengguan: { name: '正官', score: 75, description: '相敬如宾，责任感强' },
                    pianyin: { name: '偏印', score: 70, description: '互相支持，精神契合' },
                    zhengyin: { name: '正印', score: 80, description: '相互扶持，和睦相处' }
                }
            },
            
            // 地支六冲六合
            dizhi: {
                // 六冲关系（相冲）
                chong: {
                    zi: 'wu', wu: 'zi',
                    chou: 'wei', wei: 'chou',
                    yin: 'shen', shen: 'yin',
                    mao: 'you', you: 'mao',
                    chen: 'xu', xu: 'chen',
                    si: 'hai', hai: 'si'
                },
                // 六合关系（相合）
                he: {
                    zi: 'chou', chou: 'zi',
                    yin: 'hai', hai: 'yin',
                    mao: 'xu', xu: 'mao',
                    chen: 'you', you: 'chen',
                    si: 'shen', shen: 'si',
                    wu: 'wei', wei: 'wu'
                },
                // 三合关系
                sanhe: {
                    yin_wu_xu: ['yin', 'wu', 'xu'], // 寅午戌三合
                    hai_mao_wei: ['hai', 'mao', 'wei'], // 亥卯未三合
                    shen_zi_chen: ['shen', 'zi', 'chen'], // 申子辰三合
                    si_you_chou: ['si', 'you', 'chou'] // 巳酉丑三合
                }
            },
            
            // 合婚评分标准
            scoreStandard: {
                excellent: { min: 85, description: '天作之合，婚姻幸福' },
                good: { min: 75, max: 84, description: '相配良缘，和谐美满' },
                moderate: { min: 60, max: 74, description: '一般匹配，需要磨合' },
                poor: { min: 0, max: 59, description: '八字相冲，婚姻有挑战' }
            }
        };
    }

    // 计算生辰八字（简化算法）
    calculateBazi(birthDate, birthHour) {
        const date = new Date(birthDate);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        // 简化的天干地支计算
        const yearGan = (year - 4) % 10;
        const yearZhi = (year - 4) % 12;
        const monthGan = (year * 2 + month) % 10;
        const monthZhi = (month + 2) % 12 || 12;
        const dayGan = Math.floor((Date.UTC(year, month - 1, day) / 86400000 + 49) % 10);
        const dayZhi = Math.floor((Date.UTC(year, month - 1, day) / 86400000 + 49) % 12);
        
        // 时辰地支
        const hourZhiIndex = this.getHourZhiIndex(birthHour);
        // 时辰天干 = (日干 * 2 + 时支) % 10
        const hourGan = (dayGan * 2 + hourZhiIndex) % 10;

        // 天干地支名称映射
        const ganNames = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
        const zhiNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
        const ganElements = ['木', '木', '火', '火', '土', '土', '金', '金', '水', '水'];

        return {
            year: { 
                gan: ganNames[yearGan], 
                zhi: zhiNames[yearZhi],
                element: ganElements[yearGan]
            },
            month: { 
                gan: ganNames[monthGan], 
                zhi: zhiNames[monthZhi],
                element: ganElements[monthGan]
            },
            day: { 
                gan: ganNames[dayGan], 
                zhi: zhiNames[dayZhi],
                element: ganElements[dayGan]
            },
            hour: { 
                gan: ganNames[hourGan], 
                zhi: zhiNames[hourZhiIndex],
                element: ganElements[hourGan]
            },
            dayGanElement: ganElements[dayGan]
        };
    }

    // 获取时辰地支索引
    getHourZhiIndex(hourCode) {
        const hourMap = {
            zi: 0, chou: 1, yin: 2, mao: 3, chen: 4, si: 5,
            wu: 6, wei: 7, shen: 8, you: 9, xu: 10, hai: 11
        };
        return hourMap[hourCode] || 0;
    }

    // 分析五行分布
    analyzeFiveElements(bazi) {
        const elements = { 木: 0, 火: 0, 土: 0, 金: 0, 水: 0 };
        
        // 统计八字中的五行
        ['year', 'month', 'day', 'hour'].forEach(pillar => {
            if (bazi[pillar] && bazi[pillar].element) {
                elements[bazi[pillar].element]++;
            }
        });

        const sorted = Object.entries(elements).sort((a, b) => b[1] - a[1]);
        
        return {
            elements,
            strongest: sorted[0][0],
            weakest: sorted[sorted.length - 1][0]
        };
    }

    // 分析八字合婚
    analyzeMarriageCompatibility(maleBazi, femaleBazi) {
        // 计算日干五行关系
        const maleElement = maleBazi.dayGanElement;
        const femaleElement = femaleBazi.dayGanElement;
        
        // 五行相生相克分析
        const elementRelation = this.analyzeElementRelation(maleElement, femaleElement);
        
        // 地支冲合分析
        const zhiRelation = this.analyzeZhiRelation(maleBazi, femaleBazi);
        
        // 计算合婚总分
        const totalScore = this.calculateMarriageScore(elementRelation, zhiRelation);
        
        // 评定合婚等级
        const marriageLevel = this.getMarriageLevel(totalScore);
        
        return {
            elementRelation,
            zhiRelation,
            totalScore,
            marriageLevel
        };
    }

    // 分析五行关系
    analyzeElementRelation(maleElement, femaleElement) {
        let relation = '普通';
        let score = 70;
        
        // 相生关系
        if (this.marriageKnowledge.wuxing.sheng[maleElement] === femaleElement) {
            relation = '男生五行生女生';
            score = 85;
        } else if (this.marriageKnowledge.wuxing.sheng[femaleElement] === maleElement) {
            relation = '女生五行生男生';
            score = 80;
        }
        // 相克关系
        else if (this.marriageKnowledge.wuxing.ke[maleElement] === femaleElement) {
            relation = '男生五行克女生';
            score = 60;
        } else if (this.marriageKnowledge.wuxing.ke[femaleElement] === maleElement) {
            relation = '女生五行克男生';
            score = 65;
        }
        // 相同五行
        else if (maleElement === femaleElement) {
            relation = '五行相同';
            score = 75;
        }
        
        return { relation, score };
    }

    // 分析地支关系
    analyzeZhiRelation(maleBazi, femaleBazi) {
        const relations = [];
        let score = 70;
        
        // 检查地支六冲
        let chongCount = 0;
        ['year', 'month', 'day', 'hour'].forEach(pillar => {
            const maleZhi = maleBazi[pillar].zhi;
            ['year', 'month', 'day', 'hour'].forEach(femalePillar => {
                const femaleZhi = femaleBazi[femalePillar].zhi;
                if (this.isZhiChong(maleZhi, femaleZhi)) {
                    chongCount++;
                    relations.push(`${pillar}支冲${femalePillar}支`);
                }
            });
        });
        
        // 检查地支六合
        let heCount = 0;
        ['year', 'month', 'day', 'hour'].forEach(pillar => {
            const maleZhi = maleBazi[pillar].zhi;
            ['year', 'month', 'day', 'hour'].forEach(femalePillar => {
                const femaleZhi = femaleBazi[femalePillar].zhi;
                if (this.isZhiHe(maleZhi, femaleZhi)) {
                    heCount++;
                    relations.push(`${pillar}支合${femalePillar}支`);
                }
            });
        });
        
        // 根据冲合情况调整分数
        if (chongCount > 2) {
            score -= 20;
        } else if (chongCount > 0) {
            score -= 10;
        }
        
        if (heCount > 2) {
            score += 20;
        } else if (heCount > 0) {
            score += 10;
        }
        
        return { relations, score };
    }

    // 判断地支是否相冲
    isZhiChong(zhi1, zhi2) {
        return this.marriageKnowledge.dizhi.chong[zhi1] === zhi2;
    }

    // 判断地支是否相合
    isZhiHe(zhi1, zhi2) {
        return this.marriageKnowledge.dizhi.he[zhi1] === zhi2;
    }

    // 计算合婚总分
    calculateMarriageScore(elementRelation, zhiRelation) {
        // 基础分数
        let baseScore = 70;
        
        // 五行关系分数
        const elementScore = elementRelation.score;
        
        // 地支关系分数
        const zhiScore = zhiRelation.score;
        
        // 加权计算总分
        const totalScore = Math.round(baseScore * 0.2 + elementScore * 0.4 + zhiScore * 0.4);
        
        // 确保分数在0-100之间
        return Math.min(Math.max(totalScore, 0), 100);
    }

    // 获取合婚等级
    getMarriageLevel(score) {
        const { scoreStandard } = this.marriageKnowledge;
        
        if (score >= scoreStandard.excellent.min) {
            return { level: '上等', description: scoreStandard.excellent.description };
        } else if (score >= scoreStandard.good.min && score <= scoreStandard.good.max) {
            return { level: '中上', description: scoreStandard.good.description };
        } else if (score >= scoreStandard.moderate.min && score <= scoreStandard.moderate.max) {
            return { level: '中等', description: scoreStandard.moderate.description };
        } else {
            return { level: '下等', description: scoreStandard.poor.description };
        }
    }

    // 生成AI提示词
    generatePrompt(maleData, femaleData, maleAnalysis, femaleAnalysis, compatibilityResult) {
        return `请为以下两位用户进行八字合婚分析：

男方信息：
- 姓名：${maleData.userName}
- 出生日期：${maleData.birthDate}
- 出生时辰：${maleData.birthHour}
- 八字：${maleAnalysis.year.gan}${maleAnalysis.year.zhi} ${maleAnalysis.month.gan}${maleAnalysis.month.zhi} ${maleAnalysis.day.gan}${maleAnalysis.day.zhi} ${maleAnalysis.hour.gan}${maleAnalysis.hour.zhi}
- 日主五行：${maleAnalysis.dayGanElement}

女方信息：
- 姓名：${femaleData.userName}
- 出生日期：${femaleData.birthDate}
- 出生时辰：${femaleData.birthHour}
- 八字：${femaleAnalysis.year.gan}${femaleAnalysis.year.zhi} ${femaleAnalysis.month.gan}${femaleAnalysis.month.zhi} ${femaleAnalysis.day.gan}${femaleAnalysis.day.zhi} ${femaleAnalysis.hour.gan}${femaleAnalysis.hour.zhi}
- 日主五行：${femaleAnalysis.dayGanElement}

初步分析结果：
- 五行关系：${compatibilityResult.elementRelation.relation}
- 地支关系：${compatibilityResult.zhiRelation.relations.join('，')}
- 合婚评分：${compatibilityResult.totalScore}分
- 合婚等级：${compatibilityResult.marriageLevel.level}

请按照以下固定标题格式进行详细分析：

【八字合婚总评】
总体评价两人八字合婚情况，包括婚姻契合度和幸福指数

【五行喜忌分析】
分析双方五行相生相克关系，日主喜忌是否互补

【性格互动分析】
分析双方性格特点和互动模式

【婚后生活分析】
分析婚后的家庭生活、经济状况和相处模式

【子女缘分析】
分析两人的子女缘分和教育理念

【事业互助分析】
分析两人在事业上的互助关系

【婚姻建议】
针对双方八字特点，提供具体的婚姻相处建议

请确保分析专业、详细，符合传统命理学理论，同时结合现代婚姻观念。回复请用中文，内容积极正面。`;
    }

    // 使用AI进行合婚分析
    async analyzeMarriageWithAI(maleData, femaleData) {
        try {
            // 计算双方八字
            const maleBazi = this.calculateBazi(maleData.birthDate, maleData.birthHour);
            const femaleBazi = this.calculateBazi(femaleData.birthDate, femaleData.birthHour);
            
            // 分析八字合婚
            const compatibilityResult = this.analyzeMarriageCompatibility(maleBazi, femaleBazi);
            
            // 检查AI服务是否可用
            if (!window.aiService) {
                return this.generateLocalAnalysis(maleData, femaleData, maleBazi, femaleBazi, compatibilityResult);
            }
            
            // 生成AI分析提示词
            const prompt = this.generatePrompt(maleData, femaleData, maleBazi, femaleBazi, compatibilityResult);
            
            // 使用统一AI调用接口
            const systemPrompt = "你是一个专业的八字合婚分析师，精通传统命理学理论，能够进行准确的八字合婚分析和婚姻指导。请用中文回复，严格按照指定的格式输出分析结果。";
            const aiResponse = await window.aIService.callAI(prompt, systemPrompt, {
                enableFallback: true  // 启用服务降级
            });
            
            // 解析AI响应
            return this.parseAIResponse(aiResponse, maleData, femaleData, maleBazi, femaleBazi, compatibilityResult);
            
        } catch (error) {
            console.error('八字合婚AI分析失败:', error);
            
            // 降级到本地分析
            const maleBazi = this.calculateBazi(maleData.birthDate, maleData.birthHour);
            const femaleBazi = this.calculateBazi(femaleData.birthDate, femaleData.birthHour);
            const compatibilityResult = this.analyzeMarriageCompatibility(maleBazi, femaleBazi);
            
            return this.generateLocalAnalysis(maleData, femaleData, maleBazi, femaleBazi, compatibilityResult);
        }
    }

    // 解析AI响应
    parseAIResponse(aiResponse, maleData, femaleData, maleBazi, femaleBazi, compatibilityResult) {
        // 提取各个分析部分
        const sections = this.extractSections(aiResponse);
        
        return {
            success: true,
            marriageAnalysis: {
                method: 'ai',
                maleBazi,
                femaleBazi,
                maleData,
                femaleData,
                compatibility: compatibilityResult,
                summary: this.extractSummary(aiResponse),
                sections: {
                    overall: sections.overall || `八字合婚总评：合婚等级${compatibilityResult.marriageLevel.level}，${compatibilityResult.marriageLevel.description}。`,
                    elements: sections.elements || `五行喜忌分析：${compatibilityResult.elementRelation.relation}，相互影响${compatibilityResult.elementRelation.score >= 75 ? '良好' : '一般'}。`,
                    personality: sections.personality || '性格互动分析：需要进一步了解双方性格特点。',
                    life: sections.life || '婚后生活分析：婚后需要相互理解，共同经营。',
                    children: sections.children || '子女缘分析：子女缘分一般，需要共同努力。',
                    career: sections.career || '事业互助分析：在事业上可以相互支持。',
                    advice: sections.advice || '婚姻建议：建议增进沟通，相互理解，共同成长。'
                },
                suggestions: this.extractSuggestions(aiResponse),
                timestamp: new Date().toISOString()
            }
        };
    }
    
    // 提取各个分析部分
    extractSections(content) {
        const sections = {};
        
        // 定义各部分的匹配模式
        const patterns = {
            overall: /【八字合婚总评】[\s\S]*?(?=【五行喜忌分析】|$)/i,
            elements: /【五行喜忌分析】[\s\S]*?(?=【性格互动分析】|$)/i,
            personality: /【性格互动分析】[\s\S]*?(?=【婚后生活分析】|$)/i,
            life: /【婚后生活分析】[\s\S]*?(?=【子女缘分析】|$)/i,
            children: /【子女缘分析】[\s\S]*?(?=【事业互助分析】|$)/i,
            career: /【事业互助分析】[\s\S]*?(?=【婚姻建议】|$)/i,
            advice: /【婚姻建议】[\s\S]*?(?=$)/i
        };
        
        // 提取各部分内容
        Object.keys(patterns).forEach(key => {
            const match = content.match(patterns[key]);
            if (match && match[0]) {
                // 清理内容，移除标题部分
                let text = match[0];
                const titleMatch = text.match(/【[^】]*】/);
                if (titleMatch) {
                    text = text.substring(titleMatch[0].length);
                }
                sections[key] = text.trim();
            }
        });
        
        return sections;
    }
    
    // 提取摘要
    extractSummary(content) {
        // 尝试提取总评部分作为摘要
        const overallMatch = content.match(/【八字合婚总评】[\s\S]*?(?=【|$)/i);
        if (overallMatch && overallMatch[0]) {
            const text = overallMatch[0].replace(/【八字合婚总评】/, '').trim();
            return text.substring(0, 200) + (text.length > 200 ? '...' : '');
        }
        
        // 如果没有总评部分，提取开头部分作为摘要
        const firstParagraph = content.split(/\n\s*\n/)[0];
        if (firstParagraph && firstParagraph.length > 20) {
            return firstParagraph.substring(0, 200) + (firstParagraph.length > 200 ? '...' : '');
        }
        
        return '八字合婚分析结果，请查看详细内容。';
    }
    
    // 提取建议
    extractSuggestions(content) {
        const suggestions = [];
        
        // 尝试从婚姻建议部分提取
        const adviceMatch = content.match(/【婚姻建议】[\s\S]*?(?=$)/i);
        if (adviceMatch && adviceMatch[0]) {
            // 移除标题
            const adviceText = adviceMatch[0].replace(/【婚姻建议】/, '').trim();
            
            // 按行或句号分割
            const lines = adviceText.split(/[。；;]/);
            lines.forEach(line => {
                if (line.length > 10 && line.length < 100 && 
                    (line.includes('建议') || line.includes('可以') || line.includes('应该') || 
                     line.includes('宜') || line.includes('需要'))) {
                    const cleanLine = line.replace(/^\d+[\.、]/, '').trim();
                    if (cleanLine.length > 5) {
                        suggestions.push(cleanLine);
                    }
                }
            });
        }
        
        // 如果没有提取到足够的建议，添加默认建议
        if (suggestions.length < 3) {
            suggestions.push(
                '建议双方多沟通，增进了解',
                '尊重彼此的性格差异，取长补短',
                '共同规划未来，建立稳固的家庭基础',
                '遇到问题时保持冷静，共同面对'
            );
        }
        
        return suggestions.slice(0, 6); // 最多返回6条建议
    }

    // 本地合婚分析
    generateLocalAnalysis(maleData, femaleData, maleBazi, femaleBazi, compatibilityResult) {
        const { elementRelation, zhiRelation, totalScore, marriageLevel } = compatibilityResult;
        
        // 根据分数生成不同的描述
        let description, adviceText;
        if (totalScore >= 85) {
            description = '两位八字相合度高，是非常理想的婚配。在五行上相互补充，地支关系和谐，预示着婚姻生活幸福美满。';
            adviceText = '珍惜这段良缘，相互扶持，共同成长。建议在生活中多沟通，增进了解，共同规划未来。';
        } else if (totalScore >= 75) {
            description = '两位八字相合度较高，是良好的婚配。五行关系基本和谐，地支关系较为稳定，婚后生活应该比较顺利。';
            adviceText = '保持良好的沟通，尊重彼此的差异，共同经营婚姻。在遇到问题时，互相理解，共同面对。';
        } else if (totalScore >= 60) {
            description = '两位八字相合度一般，需要一定的磨合。五行和地支关系有一些不协调之处，但通过努力可以建立和谐的婚姻。';
            adviceText = '需要更多的包容和理解，尊重彼此的不同。建议在生活中多沟通，找到平衡点，共同成长。';
        } else {
            description = '两位八字相合度较低，存在一些挑战。五行和地支关系有较多不协调之处，婚后可能需要更多的努力来维持关系。';
            adviceText = '婚姻需要双方共同努力，建议多沟通，增进了解。尊重彼此的差异，学会换位思考，共同面对挑战。';
        }
        
        return {
            success: true,
            marriageAnalysis: {
                method: 'local',
                maleBazi,
                femaleBazi,
                maleData,
                femaleData,
                compatibility: compatibilityResult,
                summary: description,
                sections: {
                    overall: `八字合婚总评：合婚等级${marriageLevel.level}，总分${totalScore}分。${description}`,
                    elements: `五行喜忌分析：男方日主五行为${maleBazi.dayGanElement}，女方日主五行为${femaleBazi.dayGanElement}。${elementRelation.relation}，相互影响${elementRelation.score >= 75 ? '良好' : '一般'}。`,
                    personality: '性格互动分析：根据八字特点，双方性格各有特点，需要相互理解和包容。',
                    life: '婚后生活分析：婚后生活需要双方共同经营，相互支持，共同面对挑战。',
                    children: '子女缘分析：子女缘分一般，需要共同努力，营造良好的家庭环境。',
                    career: '事业互助分析：在事业上可以相互支持，取长补短，共同发展。',
                    advice: `婚姻建议：${adviceText}`
                },
                suggestions: [
                    '建议双方多沟通，增进了解',
                    '尊重彼此的性格差异，取长补短',
                    '共同规划未来，建立稳固的家庭基础',
                    '遇到问题时保持冷静，共同面对'
                ],
                timestamp: new Date().toISOString()
            }
        };
    }
}

// 导出类
window.MarriageAnalysisAI = MarriageAnalysisAI; 