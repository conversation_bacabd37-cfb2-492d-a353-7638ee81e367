/**
 * 姻缘画像AI系统
 * 更智能的匹配算法和个性化画像生成
 */

class PortraitAI {
    constructor() {
        this.initializeDatabase();
        this.isAnalyzing = false;
        this.analysisResult = null;
        this.currentImageUrl = null;
        // 初始化认证服务
        if (window.initializeAuthServices) {
            window.initializeAuthServices();
        }
    }

    // 参数验证与默认值设置
    validatePortraitParams(params) {
        const defaults = {
            // 外貌特征参数
            appearance: {
                age: 25,
                height: 165,
                bodyType: 'average', // slim, athletic, average, curvy
                hairStyle: 'medium', // short, medium, long, curly, straight
                hairColor: 'black', // black, brown, blonde, dyed
                eyeShape: 'almond', // almond, round, upturned, downturned
                skinTone: 'warm', // fair, warm, olive, tan
                facialFeatures: [], // 面部特征数组
                style: 'elegant' // elegant, casual, sporty, artistic, business
            },
            
            // 性格特征参数
            personality: {
                primary: ['温柔'], // 主要性格特征
                communication: 'gentle', // direct, gentle, humorous
                emotional: 'stable', // stable, passionate, calm
                social: 'balanced', // introverted, extroverted, balanced
                decision: 'analytical', // intuitive, analytical, collaborative
                loveLanguage: 'quality-time' // words, acts, gifts, touch, time
            },
            
            // 生活方式参数
            lifestyle: {
                activities: ['健身'], // 兴趣爱好
                livingStyle: 'organized', // organized, spontaneous, minimalist
                socialCircle: 'medium', // small, medium, large
                weekendHabits: ['outdoor'], // 周末习惯
                travelPreference: 'adventure', // adventure, luxury, cultural, budget
                foodPreference: 'healthy', // healthy, gourmet, simple, diverse
                entertainment: ['movies', 'music'] // 娱乐方式
            },
            
            // 职业发展参数
            career: {
                field: ['教育'], // 行业领域
                level: 'mid-career', // entry, mid-career, senior, expert
                workStyle: 'collaborative', // independent, collaborative, leadership
                ambition: 'moderate', // high, moderate, balanced
                workLifeBalance: 'balanced', // work-focused, balanced, life-focused
                futureGoals: ['specialization'] // 未来规划
            },
            
            // 价值观匹配参数
            values: {
                family: 'traditional', // traditional, modern, flexible
                finance: 'conservative', // conservative, moderate, aggressive
                education: 'continuous', // formal, continuous, practical
                religion: 'respectful', // practicing, respectful, secular
                politics: 'moderate', // conservative, moderate, liberal
                environmental: 'conscious' // conscious, moderate, indifferent
            },
            
            // 关系期望参数
            relationship: {
                commitment: 'serious', // casual, serious, marriage-focused
                timeline: '1-2-years', // immediate, 6-months, 1-2-years, flexible
                living: 'separate', // together, separate, flexible
                children: 'open', // want, open, not-now, never
                marriage: 'traditional', // traditional, modern, flexible
                independence: 'balanced' // high, balanced, interdependent
            },
            
            // 文化背景参数
            cultural: {
                background: 'chinese', // chinese, international, mixed
                language: ['mandarin'], // 语言能力
                education: 'university', // high-school, university, graduate
                region: 'eastern', // northern, southern, eastern, western, overseas
                traditions: 'modern', // traditional, modern, mixed
                socialClass: 'middle' // working, middle, upper
            },
            
            // 健康与生活习惯参数
            health: {
                fitness: 'active', // active, moderate, casual
                diet: 'balanced', // vegetarian, balanced, flexible
                sleep: 'regular', // early-bird, night-owl, regular
                stress: 'low', // low, moderate, high
                hobbies: ['sports'], // 具体爱好
                socialHabits: 'moderate' // frequent, moderate, selective
            }
        };
        
        // 深度合并参数
        return this.deepMerge(defaults, params);
    }

    // 深度合并对象
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(target[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }

    // 初始化数据库
    initializeDatabase() {
        // 扩展的人物特征数据库
        this.portraitDatabase = {
            // 基础信息
            profiles: {
                male: {
                    names: ['志强', '俊杰', '文轩', '浩然', '子涵', '梓豪', '思远', '明轩', '博文', '天佑'],
                    avatars: ['👨', '🧑', '👨‍💼', '👨‍🎓', '👨‍🎨', '👨‍💻', '👨‍⚕️', '👨‍🏫', '👨‍🎤', '👨‍🚀'],
                    traits: {
                        leadership: '有领导气质，能够承担责任',
                        creativity: '富有创造力，思维活跃',
                        stability: '稳重可靠，值得信赖',
                        humor: '幽默风趣，善于调节气氛',
                        ambition: '有理想有抱负，积极进取'
                    }
                },
                female: {
                    names: ['雨萱', '诗涵', '梦琪', '语嫣', '思怡', '婉儿', '若汐', '艺涵', '梓萱', '欣妍'],
                    avatars: ['👩', '👩‍💼', '👩‍🎓', '👩‍🎨', '👩‍🌾', '👩‍⚕️', '👩‍🏫', '👩‍🎤', '👩‍🚀', '👩‍💻'],
                    traits: {
                        gentleness: '温柔善良，体贴入微',
                        independence: '独立自主，有自己的想法',
                        wisdom: '聪慧机敏，善于思考',
                        grace: '优雅得体，气质出众',
                        warmth: '热情开朗，给人温暖感'
                    }
                }
            },

            // 详细特征描述
            detailedTraits: {
                appearance: {
                    tall: { desc: '身材高挑，气质出众', weight: 1.2 },
                    cute: { desc: '容貌甜美，让人心动', weight: 1.1 },
                    elegant: { desc: '优雅迷人，气质非凡', weight: 1.3 },
                    sporty: { desc: '体态健美，活力四射', weight: 1.1 },
                    mature: { desc: '成熟稳重，魅力深沉', weight: 1.2 },
                    youthful: { desc: '青春洋溢，朝气蓬勃', weight: 1.0 }
                },
                personality: {
                    gentle: { desc: '温柔体贴，善解人意', weight: 1.3 },
                    cheerful: { desc: '开朗活泼，充满阳光', weight: 1.2 },
                    intelligent: { desc: '聪明睿智，才华横溢', weight: 1.4 },
                    calm: { desc: '沉稳内敛，值得依靠', weight: 1.2 },
                    passionate: { desc: '热情奔放，富有激情', weight: 1.1 },
                    mysterious: { desc: '神秘迷人，引人探索', weight: 1.0 }
                },
                lifestyle: {
                    reading: { desc: '热爱阅读，知识渊博', weight: 1.2 },
                    travel: { desc: '喜欢旅行，见识广博', weight: 1.3 },
                    music: { desc: '精通音乐，艺术修养高', weight: 1.1 },
                    cooking: { desc: '擅长烹饪，生活情趣浓', weight: 1.2 },
                    fitness: { desc: '热爱运动，身体健康', weight: 1.1 },
                    art: { desc: '艺术天赋，创意无限', weight: 1.2 }
                },
                career: {
                    creative: { desc: '从事创意工作，思维活跃', weight: 1.2 },
                    business: { desc: '商业精英，事业有成', weight: 1.3 },
                    education: { desc: '教育工作者，温文尔雅', weight: 1.1 },
                    tech: { desc: '科技达人，理性思维', weight: 1.2 },
                    medical: { desc: '医护人员，爱心满满', weight: 1.4 },
                    arts: { desc: '艺术工作者，浪漫情怀', weight: 1.1 }
                }
            },

            // 星座匹配度
            zodiacCompatibility: {
                '白羊座': { high: ['狮子座', '射手座'], medium: ['双子座', '水瓶座'], low: ['巨蟹座', '摩羯座'] },
                '金牛座': { high: ['处女座', '摩羯座'], medium: ['巨蟹座', '双鱼座'], low: ['狮子座', '水瓶座'] },
                '双子座': { high: ['天秤座', '水瓶座'], medium: ['白羊座', '狮子座'], low: ['处女座', '双鱼座'] },
                '巨蟹座': { high: ['天蝎座', '双鱼座'], medium: ['金牛座', '处女座'], low: ['白羊座', '天秤座'] },
                '狮子座': { high: ['白羊座', '射手座'], medium: ['双子座', '天秤座'], low: ['金牛座', '天蝎座'] },
                '处女座': { high: ['金牛座', '摩羯座'], medium: ['巨蟹座', '天蝎座'], low: ['双子座', '射手座'] },
                '天秤座': { high: ['双子座', '水瓶座'], medium: ['狮子座', '射手座'], low: ['巨蟹座', '摩羯座'] },
                '天蝎座': { high: ['巨蟹座', '双鱼座'], medium: ['处女座', '摩羯座'], low: ['狮子座', '水瓶座'] },
                '射手座': { high: ['白羊座', '狮子座'], medium: ['天秤座', '水瓶座'], low: ['处女座', '双鱼座'] },
                '摩羯座': { high: ['金牛座', '处女座'], medium: ['天蝎座', '双鱼座'], low: ['白羊座', '天秤座'] },
                '水瓶座': { high: ['双子座', '天秤座'], medium: ['白羊座', '射手座'], low: ['金牛座', '天蝎座'] },
                '双鱼座': { high: ['巨蟹座', '天蝎座'], medium: ['金牛座', '摩羯座'], low: ['双子座', '射手座'] }
            },

            // 个性化建议模板
            suggestions: {
                dating: [
                    '可以尝试去咖啡馆安静地聊天',
                    '一起看场电影，分享彼此的感受',
                    '去公园散步，享受自然的美好',
                    '参观博物馆，探索文化的魅力',
                    '一起做饭，享受烹饪的乐趣',
                    '去音乐会，感受艺术的熏陶'
                ],
                challenges: [
                    '性格差异需要相互理解和包容',
                    '生活习惯的不同需要慢慢磨合',
                    '价值观念的差异需要深入沟通',
                    '兴趣爱好的差别可以互相学习',
                    '工作压力需要相互支持和理解'
                ],
                strengths: [
                    '你们的性格互补，能够相互成长',
                    '共同的兴趣爱好是很好的感情基础',
                    '价值观念相似，容易产生共鸣',
                    '生活方式匹配，容易适应彼此',
                    '都有上进心，能够共同进步'
                ]
            }
        };
    }

    // 智能匹配算法
    calculateMatch(userPreferences) {
        const {
            userZodiac,
            preferredGender,
            preferredAge,
            appearance,
            personality,
            lifestyle,
            career,
            priorities
        } = userPreferences;

        // 基础匹配分数
        let baseScore = 60;
        let matchDetails = {
            appearanceMatch: 0,
            personalityMatch: 0,
            lifestyleMatch: 0,
            zodiacMatch: 0,
            careerMatch: 0,
            overall: 0
        };

        // 外貌匹配计算
        if (appearance && appearance.length > 0) {
            const appearanceScore = appearance.reduce((score, trait) => {
                const traitData = this.portraitDatabase.detailedTraits.appearance[trait];
                return score + (traitData ? traitData.weight * 10 : 5);
            }, 0) / appearance.length;
            matchDetails.appearanceMatch = Math.min(appearanceScore, 25);
        }

        // 性格匹配计算
        if (personality && personality.length > 0) {
            const personalityScore = personality.reduce((score, trait) => {
                const traitData = this.portraitDatabase.detailedTraits.personality[trait];
                return score + (traitData ? traitData.weight * 12 : 6);
            }, 0) / personality.length;
            matchDetails.personalityMatch = Math.min(personalityScore, 30);
        }

        // 生活方式匹配计算
        if (lifestyle && lifestyle.length > 0) {
            const lifestyleScore = lifestyle.reduce((score, trait) => {
                const traitData = this.portraitDatabase.detailedTraits.lifestyle[trait];
                return score + (traitData ? traitData.weight * 8 : 4);
            }, 0) / lifestyle.length;
            matchDetails.lifestyleMatch = Math.min(lifestyleScore, 20);
        }

        // 星座匹配计算
        if (userZodiac) {
            const randomZodiac = this.getRandomZodiac();
            const compatibility = this.getZodiacCompatibility(userZodiac, randomZodiac);
            matchDetails.zodiacMatch = compatibility.score;
            matchDetails.partnerZodiac = randomZodiac;
            matchDetails.zodiacCompatibility = compatibility.level;
        }

        // 职业匹配计算
        if (career && career.length > 0) {
            const careerScore = career.reduce((score, trait) => {
                const traitData = this.portraitDatabase.detailedTraits.career[trait];
                return score + (traitData ? traitData.weight * 6 : 3);
            }, 0) / career.length;
            matchDetails.careerMatch = Math.min(careerScore, 15);
        }

        // 计算总分
        matchDetails.overall = baseScore + 
            matchDetails.appearanceMatch + 
            matchDetails.personalityMatch + 
            matchDetails.lifestyleMatch + 
            matchDetails.zodiacMatch + 
            matchDetails.careerMatch;

        // 确保分数在合理范围内
        matchDetails.overall = Math.min(Math.max(matchDetails.overall, 75), 99);

        return matchDetails;
    }

    // 获取星座匹配度
    getZodiacCompatibility(zodiac1, zodiac2) {
        const compatibility = this.portraitDatabase.zodiacCompatibility[zodiac1];
        if (!compatibility) return { score: 10, level: '中等' };

        if (compatibility.high.includes(zodiac2)) {
            return { score: 15, level: '高度匹配' };
        } else if (compatibility.medium.includes(zodiac2)) {
            return { score: 10, level: '中等匹配' };
        } else {
            return { score: 5, level: '需要磨合' };
        }
    }

    // 随机获取星座
    getRandomZodiac() {
        const zodiacs = Object.keys(this.portraitDatabase.zodiacCompatibility);
        return zodiacs[Math.floor(Math.random() * zodiacs.length)];
    }

    // 生成详细画像
    generateDetailedPortrait(userPreferences) {
        const matchData = this.calculateMatch(userPreferences);
        const { preferredGender } = userPreferences;
        
        // 选择基础信息
        const profiles = this.portraitDatabase.profiles[preferredGender];
        const name = profiles.names[Math.floor(Math.random() * profiles.names.length)];
        const avatar = profiles.avatars[Math.floor(Math.random() * profiles.avatars.length)];
        
        // 生成年龄
        const age = this.generateAge(userPreferences.preferredAge);
        
        // 生成详细描述
        const description = this.generateDescription(userPreferences, matchData);
        
        // 生成建议
        const suggestions = this.generateSuggestions(userPreferences, matchData);

        return {
            basic: {
                name,
                avatar,
                age,
                zodiac: matchData.partnerZodiac
            },
            match: matchData,
            description,
            suggestions,
            timestamp: new Date().toISOString()
        };
    }

    // 生成年龄
    generateAge(ageRange) {
        const ranges = {
            'young': [20, 25],
            'mature': [26, 35],
            'middle': [36, 45],
            'senior': [45, 60]
        };
        
        const [min, max] = ranges[ageRange] || [25, 35];
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 生成描述
    generateDescription(preferences, matchData) {
        const descriptions = [];
        
        // 外貌描述
        if (preferences.appearance && preferences.appearance.length > 0) {
            preferences.appearance.forEach(trait => {
                const traitData = this.portraitDatabase.detailedTraits.appearance[trait];
                if (traitData) descriptions.push(traitData.desc);
            });
        }
        
        // 性格描述
        if (preferences.personality && preferences.personality.length > 0) {
            preferences.personality.forEach(trait => {
                const traitData = this.portraitDatabase.detailedTraits.personality[trait];
                if (traitData) descriptions.push(traitData.desc);
            });
        }
        
        // 生活方式描述
        if (preferences.lifestyle && preferences.lifestyle.length > 0) {
            preferences.lifestyle.forEach(trait => {
                const traitData = this.portraitDatabase.detailedTraits.lifestyle[trait];
                if (traitData) descriptions.push(traitData.desc);
            });
        }

        return descriptions.join('，');
    }

    // 生成建议
    generateSuggestions(preferences, matchData) {
        const suggestions = {
            dating: [],
            challenges: [],
            strengths: []
        };

        // 随机选择建议
        suggestions.dating = this.getRandomItems(this.portraitDatabase.suggestions.dating, 2);
        suggestions.challenges = this.getRandomItems(this.portraitDatabase.suggestions.challenges, 2);
        suggestions.strengths = this.getRandomItems(this.portraitDatabase.suggestions.strengths, 2);

        return suggestions;
    }

    // 获取随机项目
    getRandomItems(array, count) {
        const shuffled = [...array].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }

    // 图像生成配置
    getImageGenerationConfig() {
        return {
            // 服务优先级列表（从高到低尝试）
            services: [
                {
                    name: 'zhipu',
                    type: 'zhipu-ai',
                    priority: 1,
                    description: '智谱AI CogView'
                },
                {
                    name: 'pollinations',
                    url: 'https://image.pollinations.ai/prompt/',
                    type: 'direct',
                    free: true,
                    priority: 2
                },
                {
                    name: 'huggingface',
                    url: 'https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5',
                    type: 'api',
                    free: true,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    priority: 3
                },
                {
                    name: 'artbreeder',
                    url: 'https://www.artbreeder.com/api/images/generate',
                    type: 'api',
                    free: false, // 备用方案
                    priority: 4
                }
            ],

            // 默认参数
            defaultParams: {
                width: 512,
                height: 512,
                steps: 20,
                guidance_scale: 7.5
            }
        };
    }

    // 生成增强的人物描述英文提示词
    generateImagePrompt(analysisData) {
        const { personality, lifestyle, career, appearance, preferredGender, values, health } = analysisData;
        
        // 性别提示词
        let genderPrompt = '';
        if (preferredGender === 'female') {
            genderPrompt = 'female, woman, young woman, ';
        } else if (preferredGender === 'male') {
            genderPrompt = 'male, man, young man, ';
        }
        
        // 基础提示词
        let prompt = genderPrompt + "A beautiful Chinese person, full body, standing, whole body, fashion pose, Asian face, natural skin tone, gentle temperament, oriental facial features, ";
        
        // 外貌特征
        if (appearance) {
            if (appearance.age) {
                const ageGroup = this.getAgeGroupDescription(appearance.age);
                prompt += `${ageGroup}, `;
            }
            if (appearance.height) {
                const heightDesc = this.getHeightDescription(appearance.height);
                prompt += `${heightDesc}, `;
            }
            if (appearance.bodyType) {
                prompt += `${appearance.bodyType} build, `;
            }
            if (appearance.hairStyle) {
                prompt += `${appearance.hairStyle} hair, `;
            }
            if (appearance.hairColor) {
                prompt += `${appearance.hairColor} hair color, `;
            }
            if (appearance.eyeShape) {
                prompt += `${appearance.eyeShape} eyes, `;
            }
            if (appearance.skinTone) {
                prompt += `${appearance.skinTone} skin, `;
            }
            if (appearance.style) {
                prompt += `${appearance.style} style, `;
            }
        }
        
        // 性格特征对应的视觉风格
        if (personality && personality.primary) {
            const personalityArray = Array.isArray(personality.primary) ? personality.primary : [personality.primary];
            if (personalityArray.includes('温柔')) prompt += "gentle expression, soft features, ";
            if (personalityArray.includes('活泼')) prompt += "bright smile, energetic pose, ";
            if (personalityArray.includes('知性')) prompt += "intelligent eyes, elegant appearance, ";
            if (personalityArray.includes('成熟')) prompt += "sophisticated look, confident posture, ";
            if (personalityArray.includes('浪漫')) prompt += "romantic aura, dreamy expression, ";
            if (personalityArray.includes('阳光')) prompt += "sunny disposition, bright eyes, ";
            if (personalityArray.includes('艺术')) prompt += "artistic flair, creative style, ";
            if (personalityArray.includes('时尚')) prompt += "fashionable appearance, trendy style, ";
            if (personalityArray.includes('爱心')) prompt += "warm smile, caring expression, ";
            if (personalityArray.includes('平静')) prompt += "serene expression, calm demeanor, ";
        }
        
        // 沟通方式特征
        if (personality && personality.communication) {
            if (personality.communication === 'gentle') prompt += "gentle expression, ";
            if (personality.communication === 'humorous') prompt += "cheerful expression, ";
            if (personality.communication === 'direct') prompt += "confident expression, ";
        }
        
        // 情绪特征
        if (personality && personality.emotional) {
            if (personality.emotional === 'passionate') prompt += "passionate aura, ";
            if (personality.emotional === 'calm') prompt += "serene expression, ";
            if (personality.emotional === 'stable') prompt += "balanced expression, ";
        }
        
        // 生活方式对应的背景和装扮
        if (lifestyle && lifestyle.activities) {
            const lifestyleArray = Array.isArray(lifestyle.activities) ? lifestyle.activities : [lifestyle.activities];
            if (lifestyleArray.includes('健身')) prompt += "athletic build, sporty outfit, ";
            if (lifestyleArray.includes('旅行')) prompt += "adventurous style, casual chic, ";
            if (lifestyleArray.includes('读书')) prompt += "bookish charm, cozy setting, ";
            if (lifestyleArray.includes('音乐')) prompt += "artistic vibe, creative atmosphere, ";
            if (lifestyleArray.includes('运动')) prompt += "athletic physique, active lifestyle, ";
            if (lifestyleArray.includes('烹饪')) prompt += "domestic charm, warm setting, ";
            if (lifestyleArray.includes('摄影')) prompt += "creative eye, artistic perspective, ";
            if (lifestyleArray.includes('游戏')) prompt += "youthful energy, modern style, ";
            if (lifestyleArray.includes('户外')) prompt += "natural beauty, outdoor spirit, ";
            if (lifestyleArray.includes('时尚')) prompt += "trendy fashion, stylish appearance, ";
            if (lifestyleArray.includes('宠物')) prompt += "loving nature, gentle touch, ";
            if (lifestyleArray.includes('冥想')) prompt += "peaceful aura, zen-like calm, ";
            if (lifestyleArray.includes('舞蹈')) prompt += "graceful movement, artistic pose, ";
            if (lifestyleArray.includes('写作')) prompt += "intellectual charm, thoughtful expression, ";
            // 新增的兴趣爱好
            if (lifestyleArray.includes('电影')) prompt += "cinematic charm, expressive eyes, ";
            if (lifestyleArray.includes('购物')) prompt += "fashionable style, trendy appearance, ";
            if (lifestyleArray.includes('咖啡')) prompt += "sophisticated taste, cozy atmosphere, ";
            if (lifestyleArray.includes('园艺')) prompt += "natural beauty, gentle hands, ";
            if (lifestyleArray.includes('瑜伽')) prompt += "flexible grace, peaceful expression, ";
            if (lifestyleArray.includes('手工')) prompt += "creative hands, artistic detail, ";
            if (lifestyleArray.includes('收藏')) prompt += "refined taste, cultured appearance, ";
            if (lifestyleArray.includes('桌游')) prompt += "strategic mind, social charm, ";
        }
        
        // 生活风格
        if (lifestyle && lifestyle.livingStyle) {
            if (lifestyle.livingStyle === 'organized') prompt += "neat appearance, ";
            if (lifestyle.livingStyle === 'spontaneous') prompt += "casual style, ";
            if (lifestyle.livingStyle === 'minimalist') prompt += "simple style, ";
        }
        
        // 职业特征
        let careerArray = [];
        if (career && career.field) {
            careerArray = Array.isArray(career.field) ? career.field : [career.field];
            if (careerArray.includes('医生')) prompt += "professional attire, caring demeanor, ";
            if (careerArray.includes('教师')) prompt += "warm personality, approachable look, ";
            if (careerArray.includes('艺术')) prompt += "creative style, unique fashion, ";
            if (careerArray.includes('商务')) prompt += "business casual, confident appearance, ";
            if (careerArray.includes('科技')) prompt += "modern tech-savvy, intelligent look, ";
            if (careerArray.includes('音乐')) prompt += "artistic temperament, creative vibe, ";
            if (careerArray.includes('体育')) prompt += "athletic build, energetic appearance, ";
            if (careerArray.includes('旅游')) prompt += "adventurous spirit, worldly experience, ";
        }
        
        // 工作风格
        if (career && career.workStyle) {
            if (career.workStyle === 'leadership') prompt += "confident posture, ";
            if (career.workStyle === 'collaborative') prompt += "approachable expression, ";
            if (career.workStyle === 'independent') prompt += "focused expression, ";
        }
        
        // 健康生活习惯
        if (health && health.fitness) {
            if (health.fitness === 'active') prompt += "athletic build, ";
            if (health.fitness === 'moderate') prompt += "healthy appearance, ";
            if (health.fitness === 'casual') prompt += "natural build, ";
        }
        
        // 价值观影响
        if (values && values.family) {
            if (values.family === 'traditional') prompt += "traditional elegance, ";
            if (values.family === 'modern') prompt += "contemporary style, ";
            if (values.family === 'flexible') prompt += "adaptable appearance, ";
        }

        // 更多职业特征（使用之前定义的careerArray）
        if (careerArray && careerArray.length > 0) {
            if (careerArray.includes('餐饮')) prompt += "warm hospitality, domestic charm, ";
            if (careerArray.includes('设计')) prompt += "creative flair, artistic vision, ";
            if (careerArray.includes('媒体')) prompt += "communicative charm, expressive face, ";
            if (careerArray.includes('游戏')) prompt += "youthful energy, tech-savvy look, ";
            if (careerArray.includes('环保')) prompt += "natural beauty, eco-conscious style, ";
            if (careerArray.includes('时尚')) prompt += "trendy fashion, stylish appearance, ";
            if (careerArray.includes('兽医')) prompt += "caring nature, gentle touch, ";
            if (careerArray.includes('公益')) prompt += "compassionate heart, warm smile, ";
            if (careerArray.includes('健康')) prompt += "vibrant health, positive energy, ";
            if (careerArray.includes('表演')) prompt += "expressive face, artistic talent, ";
            if (careerArray.includes('出版')) prompt += "intellectual charm, thoughtful expression, ";
        }
        // 通用美化描述
        prompt += "high quality, detailed face, beautiful lighting, professional photography, 4k resolution";
        // 负面提示词
        const negativePrompt = "ugly, distorted, blurry, low quality, cartoon, anime, sketch, black and white, western, caucasian, blue eyes, blonde hair, exaggerated features, close-up, portrait only, headshot";
        return {
            positive: prompt,
            negative: negativePrompt
        };
    }

    // 年龄组描述
    getAgeGroupDescription(age) {
        if (age <= 25) return "young adult";
        if (age <= 35) return "mature young person";
        if (age <= 45) return "middle-aged person";
        return "mature adult";
    }

    // 身高描述
    getHeightDescription(height) {
        if (height <= 160) return "petite build";
        if (height <= 175) return "average height";
        return "tall stature";
    }

    // 图像生成主函数
    async generatePortraitImage(analysisData) {
        const config = this.getImageGenerationConfig();
        const prompts = this.generateImagePrompt(analysisData);
        
        console.log('Generated prompts:', prompts);
        
        // 依次尝试不同的服务
        for (const service of config.services) {
            try {
                console.log(`尝试使用 ${service.name} 生成图像...`);
                const imageUrl = await this.callImageGenerationAPI(service, prompts);
                
                if (imageUrl) {
                    this.currentImageUrl = imageUrl;
                    return imageUrl;
                }
            } catch (error) {
                console.warn(`${service.name} 服务失败:`, error);
                continue;
            }
        }
        
        // 如果所有服务都失败，返回默认占位图
        console.log('所有图像生成服务都失败，使用默认占位图');
        return this.generateDefaultPortrait(analysisData);
    }

    // 新增：生成三张全身姻缘画像
    async generatePortraitImages(analysisData) {
        console.log('开始生成三张全身姻缘画像...', analysisData);

        try {
            // 检查是否使用智谱AI
            if (window.AI_CONFIG && window.AI_CONFIG.SERVICE_TYPE === 'zhipu' && window.zhipuAIService) {
                console.log('使用智谱AI生成画像...');
                return await window.zhipuAIService.generatePortraitImages(analysisData);
            }

            // 使用原有的图像生成服务
            const config = this.getImageGenerationConfig();
            const styleVariants = [
                "wearing a cheongsam, in a garden",
                "in casual clothes, in a modern city",
                "in a suit, standing by a bookshelf",
                "with a gentle smile, holding a book",
                "walking naturally, in a park",
                "traditional Hanfu, by a lake",
                "sporty style, on a running track",
                "elegant dress, at a party"
            ];
            const images = [];
            const usedIndexes = new Set();

            for (let i = 0; i < 3; i++) {
                // 保证每次风格不重复
                let idx;
                do {
                    idx = Math.floor(Math.random() * styleVariants.length);
                } while (usedIndexes.has(idx) && usedIndexes.size < styleVariants.length);
                usedIndexes.add(idx);
                const style = styleVariants[idx];

                try {
                    // 为每次prompt加不同风格
                    const prompts = this.generateImagePrompt({ ...analysisData, _style: style });
                    prompts.positive += ', ' + style;

                    // 跳过智谱AI服务，使用其他服务
                    const nonZhipuServices = config.services.filter(s => s.type !== 'zhipu-ai');
                    const imageUrl = await this.callImageGenerationAPI(nonZhipuServices[0], prompts);
                    if (imageUrl) {
                        images.push(imageUrl);
                    } else {
                        images.push(this.generateDefaultPortrait(analysisData));
                    }
                } catch (error) {
                    console.warn(`生成第${i + 1}张画像失败:`, error);
                    images.push(this.generateDefaultPortrait(analysisData));
                }
            }

            return images;
        } catch (error) {
            console.error('生成画像失败:', error);
            // 返回三张默认图片
            return [
                this.generateDefaultPortrait(analysisData),
                this.generateDefaultPortrait(analysisData),
                this.generateDefaultPortrait(analysisData)
            ];
        }
    }

    // 调用图像生成API
    async callImageGenerationAPI(service, prompts) {
        if (service.type === 'direct') {
            // 直接URL拼接方式（如Pollinations）
            const encodedPrompt = encodeURIComponent(prompts.positive);
            return `${service.url}${encodedPrompt}?width=512&height=512&nologo=true`;
        } else if (service.type === 'api') {
            // API调用方式
            return await this.callPostAPI(service, prompts);
        }
        return null;
    }

    // POST API调用
    async callPostAPI(service, prompts) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
        
        try {
            const response = await fetch(service.url, {
                method: 'POST',
                headers: service.headers || {},
                body: JSON.stringify({
                    inputs: prompts.positive,
                    negative_prompt: prompts.negative,
                    num_inference_steps: 20,
                    guidance_scale: 7.5,
                    width: 512,
                    height: 512
                }),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (response.ok) {
                if (service.name === 'huggingface') {
                    // Hugging Face返回的是图像blob
                    const blob = await response.blob();
                    return URL.createObjectURL(blob);
                } else {
                    const data = await response.json();
                    return data.image || data.images?.[0] || data.url;
                }
            }
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
        return null;
    }

    // 生成默认肖像（SVG占位图）
    generateDefaultPortrait(analysisData) {
        const colors = ['#FF6B9D', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        
        const svg = `
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:${randomColor};stop-opacity:1" />
                        <stop offset="100%" style="stop-color:${randomColor}80;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="512" height="512" fill="url(#grad)"/>
                <circle cx="256" cy="200" r="80" fill="white" opacity="0.8"/>
                <circle cx="240" cy="180" r="8" fill="${randomColor}"/>
                <circle cx="272" cy="180" r="8" fill="${randomColor}"/>
                <path d="M 230 220 Q 256 240 282 220" stroke="${randomColor}" stroke-width="4" fill="none"/>
                <rect x="176" y="280" width="160" height="200" rx="20" fill="white" opacity="0.8"/>
                <text x="256" y="350" text-anchor="middle" font-family="Arial" font-size="16" fill="${randomColor}">
                    理想画像
                </text>
                <text x="256" y="380" text-anchor="middle" font-family="Arial" font-size="14" fill="${randomColor}">
                    ${Array.isArray(analysisData.personality) ? analysisData.personality.slice(0, 2).join(' · ') : '温暖 · 智慧'}
                </text>
            </svg>
        `;
        
        return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
    }

    // 完整的分析流程（包含图像生成）
    async analyzeCompatibility(formData) {
        this.isAnalyzing = true;
        
        try {
            // 1. 基础分析
            const basicAnalysis = this.generateBasicAnalysis(formData);
            
            // 2. 智能增强分析（如果配置了智能服务）
            let enhancedAnalysis = basicAnalysis;
            if (window.AI_CONFIG && window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
                try {
                    enhancedAnalysis = await this.generateAIAnalysis(formData, basicAnalysis);
                } catch (error) {
                    console.warn('智能分析失败，使用基础分析:', error);
                }
            }
            
            // 3. 生成三张全身姻缘画像
            console.log('开始生成三张全身姻缘画像...');
            const portraitImages = await this.generatePortraitImages(enhancedAnalysis);
            
            // 4. 整合结果
            const result = {
                ...enhancedAnalysis,
                portraitImages, // 新增：三张图片数组
                generatedAt: new Date().toISOString()
            };
            
            this.analysisResult = result;
            return result;
            
        } catch (error) {
            console.error('分析过程出错:', error);
            throw error;
        } finally {
            this.isAnalyzing = false;
        }
    }

    // 生成基础分析
    generateBasicAnalysis(formData) {
        console.log('开始基础分析:', formData);
        
        // 验证和设置默认参数
        const validatedData = this.validatePortraitParams(formData);
        
        // 使用现有的 generateDetailedPortrait 方法
        const detailedPortrait = this.generateDetailedPortrait(validatedData);
        
        // 转换为新的格式，包含所有新参数
        return {
            basic: detailedPortrait.basic,
            match: detailedPortrait.match,
            description: detailedPortrait.description,
            suggestions: detailedPortrait.suggestions,
            appearance: validatedData.appearance,
            personality: validatedData.personality,
            lifestyle: validatedData.lifestyle,
            career: validatedData.career,
            values: validatedData.values,
            relationship: validatedData.relationship,
            cultural: validatedData.cultural,
            health: validatedData.health,
            zodiac: detailedPortrait.basic.zodiac
        };
    }

    // AI增强分析（使用配置的AI服务）
    async generateAIAnalysis(formData, basicAnalysis) {
        console.log('开始AI增强分析...');
        
        if (!window.aIService) {
            console.warn('AI服务未初始化，返回基础分析');
            return basicAnalysis;
        }

        try {
            // 构建AI提示词
            const prompt = this.buildAIPrompt(formData, basicAnalysis);
            
            // 使用统一AI调用接口
            const systemPrompt = "你是一个专业的姻缘分析师，能够根据用户的偏好生成详细的理想另一半画像。请用中文回复，并按照指定的JSON格式返回结果。";
            const aiResponse = await window.aIService.callAI(prompt, systemPrompt, {
                enableFallback: true  // 启用服务降级
            });

            // 解析AI响应并增强分析结果
            const enhancedAnalysis = this.parseAIResponse(aiResponse, basicAnalysis);
            return enhancedAnalysis;
            
        } catch (error) {
            console.error('AI分析失败:', error);
            return basicAnalysis;
        }
    }

    // 构建增强的AI提示词
    buildAIPrompt(formData, basicAnalysis) {
        const { 
            preferredGender, 
            preferredAge, 
            priorities, 
            userZodiac,
            appearance,
            personality,
            lifestyle,
            career,
            values,
            relationship,
            cultural,
            health
        } = formData;
        
        return `请基于以下详细参数生成理想伴侣画像：

用户基本信息：
- 理想伴侣性别：${preferredGender === 'male' ? '男性' : '女性'}
- 理想年龄段：${this.getAgeRangeText(preferredAge)}
- 最看重的特质：${priorities.join(', ')}
- 用户星座：${userZodiac || '未提供'}

外貌特征期望：
- 年龄：${appearance.age}岁
- 身高：${appearance.height}cm
- 体型：${this.getBodyTypeText(appearance.bodyType)}
- 发型：${this.getHairStyleText(appearance.hairStyle)}
- 发色：${this.getHairColorText(appearance.hairColor)}
- 眼型：${this.getEyeShapeText(appearance.eyeShape)}
- 肤色：${this.getSkinToneText(appearance.skinTone)}
- 风格：${this.getStyleText(appearance.style)}

性格特征期望：
- 主要性格：${personality.primary.join(', ')}
- 沟通方式：${this.getCommunicationText(personality.communication)}
- 情绪特征：${this.getEmotionalText(personality.emotional)}
- 社交倾向：${this.getSocialText(personality.social)}
- 决策方式：${this.getDecisionText(personality.decision)}
- 爱的语言：${this.getLoveLanguageText(personality.loveLanguage)}

生活方式期望：
- 兴趣爱好：${lifestyle.activities.join(', ')}
- 生活风格：${this.getLivingStyleText(lifestyle.livingStyle)}
- 社交圈：${this.getSocialCircleText(lifestyle.socialCircle)}
- 周末习惯：${lifestyle.weekendHabits.join(', ')}
- 旅行偏好：${this.getTravelPreferenceText(lifestyle.travelPreference)}
- 饮食偏好：${this.getFoodPreferenceText(lifestyle.foodPreference)}

职业发展期望：
- 行业领域：${career.field.join(', ')}
- 职业阶段：${this.getCareerLevelText(career.level)}
- 工作风格：${this.getWorkStyleText(career.workStyle)}
- 事业心：${this.getAmbitionText(career.ambition)}
- 工作生活平衡：${this.getWorkLifeBalanceText(career.workLifeBalance)}

价值观期望：
- 家庭观念：${this.getFamilyValueText(values.family)}
- 理财观念：${this.getFinanceValueText(values.finance)}
- 教育观念：${this.getEducationValueText(values.education)}
- 环保意识：${this.getEnvironmentalValueText(values.environmental)}

关系期望：
- 承诺程度：${this.getCommitmentText(relationship.commitment)}
- 时间规划：${this.getTimelineText(relationship.timeline)}
- 居住安排：${this.getLivingArrangementText(relationship.living)}
- 生育观念：${this.getChildrenValueText(relationship.children)}
- 婚姻观念：${this.getMarriageValueText(relationship.marriage)}

文化背景期望：
- 文化背景：${this.getCulturalBackgroundText(cultural.background)}
- 语言能力：${cultural.language.join(', ')}
- 教育背景：${this.getEducationBackgroundText(cultural.education)}
- 地域：${this.getRegionText(cultural.region)}

健康生活习惯期望：
- 健身习惯：${this.getFitnessText(health.fitness)}
- 饮食习惯：${this.getDietText(health.diet)}
- 作息习惯：${this.getSleepText(health.sleep)}
- 压力水平：${this.getStressText(health.stress)}

基础匹配结果：
- 综合匹配度：${Math.round(basicAnalysis.match.overall)}%
- 理想伴侣姓名：${basicAnalysis.basic.name}
- 理想伴侣年龄：${basicAnalysis.basic.age}岁
- 理想伴侣星座：${basicAnalysis.basic.zodiac}

请基于以上详细参数，生成一个全面的理想伴侣画像，包括：
1. 详细的外貌描述（100字左右）
2. 深入的性格分析（150字左右）
3. 生活方式和兴趣爱好的具体描述（100字左右）
4. 职业发展和价值观的匹配分析（100字左右）
5. 3个具体的相处建议
6. 2个关系优势分析
7. 2个可能的挑战提醒
8. 文化背景和生活习惯的兼容性分析（100字左右）

请用温暖、专业、具体的语调，避免过于绝对的表述，注重实用性和可操作性。`;
    }

    // 解析AI响应
    parseAIResponse(aiResponse, basicAnalysis) {
        try {
            // 简单的文本解析，提取AI生成的内容
            let enhancedDescription = basicAnalysis.description;
            let enhancedSuggestions = basicAnalysis.suggestions;

            if (aiResponse && typeof aiResponse === 'string') {
                enhancedDescription = aiResponse.length > 50 ? aiResponse.substring(0, 200) + '...' : aiResponse;
                
                // 尝试从AI响应中提取建议
                if (aiResponse.includes('建议') || aiResponse.includes('相处')) {
                    enhancedSuggestions = {
                        ...basicAnalysis.suggestions,
                        dating: [
                            '根据AI分析，建议保持真诚沟通',
                            '重视彼此的兴趣爱好',
                            '给对方足够的个人空间'
                        ]
                    };
                }
            }

            return {
                ...basicAnalysis,
                description: enhancedDescription,
                suggestions: enhancedSuggestions,
                aiAnalysis: aiResponse,
                aiEnhanced: true
            };
        } catch (error) {
            console.error('AI响应解析失败:', error);
            return basicAnalysis;
        }
    }

    // 获取年龄范围文本
    getAgeRangeText(ageRange) {
        const mapping = {
            'young': '20-25岁',
            'mature': '26-35岁', 
            'middle': '36-45岁',
            'senior': '45岁以上'
        };
        return mapping[ageRange] || '26-35岁';
    }

    // 文本转换函数 - 外貌特征
    getBodyTypeText(bodyType) {
        const map = {
            'slim': '苗条',
            'athletic': '健壮',
            'average': '标准',
            'curvy': '丰满'
        };
        return map[bodyType] || '标准';
    }

    getHairStyleText(hairStyle) {
        const map = {
            'short': '短发',
            'medium': '中发',
            'long': '长发',
            'curly': '卷发',
            'straight': '直发'
        };
        return map[hairStyle] || '中发';
    }

    getHairColorText(hairColor) {
        const map = {
            'black': '黑色',
            'brown': '棕色',
            'blonde': '金色',
            'dyed': '染发'
        };
        return map[hairColor] || '黑色';
    }

    getEyeShapeText(eyeShape) {
        const map = {
            'almond': '杏仁眼',
            'round': '圆眼',
            'upturned': '上扬眼',
            'downturned': '下垂眼'
        };
        return map[eyeShape] || '杏仁眼';
    }

    getSkinToneText(skinTone) {
        const map = {
            'fair': '白皙',
            'warm': '暖色',
            'olive': '橄榄色',
            'tan': '小麦色'
        };
        return map[skinTone] || '暖色';
    }

    getStyleText(style) {
        const map = {
            'elegant': '优雅',
            'casual': '休闲',
            'sporty': '运动',
            'artistic': '艺术',
            'business': '商务'
        };
        return map[style] || '优雅';
    }

    // 文本转换函数 - 性格特征
    getCommunicationText(communication) {
        const map = {
            'direct': '直接',
            'gentle': '温和',
            'humorous': '幽默'
        };
        return map[communication] || '温和';
    }

    getEmotionalText(emotional) {
        const map = {
            'stable': '稳定',
            'passionate': '热情',
            'calm': '平静'
        };
        return map[emotional] || '稳定';
    }

    getSocialText(social) {
        const map = {
            'introverted': '内向',
            'extroverted': '外向',
            'balanced': '平衡'
        };
        return map[social] || '平衡';
    }

    getDecisionText(decision) {
        const map = {
            'intuitive': '直觉',
            'analytical': '理性',
            'collaborative': '协作'
        };
        return map[decision] || '理性';
    }

    getLoveLanguageText(loveLanguage) {
        const map = {
            'words': '言语表达',
            'acts': '行动服务',
            'gifts': '礼物',
            'touch': '肢体接触',
            'time': '陪伴时间'
        };
        return map[loveLanguage] || '陪伴时间';
    }

    // 文本转换函数 - 生活方式
    getLivingStyleText(livingStyle) {
        const map = {
            'organized': '有条理',
            'spontaneous': '随性',
            'minimalist': '简约'
        };
        return map[livingStyle] || '有条理';
    }

    getSocialCircleText(socialCircle) {
        const map = {
            'small': '小圈子',
            'medium': '中等',
            'large': '大圈子'
        };
        return map[socialCircle] || '中等';
    }

    getTravelPreferenceText(travelPreference) {
        const map = {
            'adventure': '冒险',
            'luxury': '奢华',
            'cultural': '文化',
            'budget': '经济'
        };
        return map[travelPreference] || '冒险';
    }

    getFoodPreferenceText(foodPreference) {
        const map = {
            'healthy': '健康',
            'gourmet': '美食',
            'simple': '简单',
            'diverse': '多样'
        };
        return map[foodPreference] || '健康';
    }

    // 文本转换函数 - 职业发展
    getCareerLevelText(level) {
        const map = {
            'entry': '初级',
            'mid-career': '中级',
            'senior': '高级',
            'expert': '专家'
        };
        return map[level] || '中级';
    }

    getWorkStyleText(workStyle) {
        const map = {
            'independent': '独立',
            'collaborative': '协作',
            'leadership': '领导'
        };
        return map[workStyle] || '协作';
    }

    getAmbitionText(ambition) {
        const map = {
            'high': '高',
            'moderate': '中等',
            'balanced': '平衡'
        };
        return map[ambition] || '中等';
    }

    getWorkLifeBalanceText(workLifeBalance) {
        const map = {
            'work-focused': '工作为重',
            'balanced': '平衡',
            'life-focused': '生活为重'
        };
        return map[workLifeBalance] || '平衡';
    }

    // 文本转换函数 - 价值观
    getFamilyValueText(family) {
        const map = {
            'traditional': '传统',
            'modern': '现代',
            'flexible': '灵活'
        };
        return map[family] || '传统';
    }

    getFinanceValueText(finance) {
        const map = {
            'conservative': '保守',
            'moderate': '适中',
            'aggressive': '积极'
        };
        return map[finance] || '保守';
    }

    getEducationValueText(education) {
        const map = {
            'formal': '正规',
            'continuous': '持续',
            'practical': '实用'
        };
        return map[education] || '持续';
    }

    getEnvironmentalValueText(environmental) {
        const map = {
            'conscious': '重视',
            'moderate': '适中',
            'indifferent': '不在意'
        };
        return map[environmental] || '重视';
    }

    // 文本转换函数 - 关系期望
    getCommitmentText(commitment) {
        const map = {
            'casual': '轻松',
            'serious': '认真',
            'marriage-focused': '以结婚为目的'
        };
        return map[commitment] || '认真';
    }

    getTimelineText(timeline) {
        const map = {
            'immediate': '立即',
            '6-months': '6个月内',
            '1-2-years': '1-2年',
            'flexible': '随缘'
        };
        return map[timeline] || '1-2年';
    }

    getLivingArrangementText(living) {
        const map = {
            'together': '同居',
            'separate': '分开住',
            'flexible': '灵活'
        };
        return map[living] || '分开住';
    }

    getChildrenValueText(children) {
        const map = {
            'want': '想要',
            'open': '开放',
            'not-now': '暂时不要',
            'never': '不要'
        };
        return map[children] || '开放';
    }

    getMarriageValueText(marriage) {
        const map = {
            'traditional': '传统',
            'modern': '现代',
            'flexible': '灵活'
        };
        return map[marriage] || '传统';
    }

    // 文本转换函数 - 文化背景
    getCulturalBackgroundText(background) {
        const map = {
            'chinese': '中国文化',
            'international': '国际化',
            'mixed': '混合'
        };
        return map[background] || '中国文化';
    }

    getEducationBackgroundText(education) {
        const map = {
            'high-school': '高中',
            'university': '大学',
            'graduate': '研究生'
        };
        return map[education] || '大学';
    }

    getRegionText(region) {
        const map = {
            'northern': '北方',
            'southern': '南方',
            'eastern': '东部',
            'western': '西部',
            'overseas': '海外'
        };
        return map[region] || '东部';
    }

    // 文本转换函数 - 健康生活习惯
    getFitnessText(fitness) {
        const map = {
            'active': '积极',
            'moderate': '一般',
            'casual': '偶尔'
        };
        return map[fitness] || '积极';
    }

    getDietText(diet) {
        const map = {
            'vegetarian': '素食',
            'balanced': '均衡',
            'flexible': '灵活'
        };
        return map[diet] || '均衡';
    }

    getSleepText(sleep) {
        const map = {
            'early-bird': '早睡早起',
            'night-owl': '夜猫子',
            'regular': '规律'
        };
        return map[sleep] || '规律';
    }

    getStressText(stress) {
        const map = {
            'low': '低',
            'moderate': '中等',
            'high': '高'
        };
        return map[stress] || '低';
    }

    // 新增：为向导界面优化的画像生成函数
    async generateWizardPortrait(userData) {
        console.log('向导界面生成画像:', userData);

        // 转换中文值到英文值
        const convertedUserData = this.convertChineseToEnglish(userData);

        // 构建符合 generateImagePrompt 期望的数据结构
        const analysisData = {
            gender: userData.gender, // 添加用户性别
            preferredGender: userData.preferredGender,
            age: userData.age, // 添加用户年龄
            zodiac: userData.zodiac, // 添加用户星座
            priorities: userData.priorities, // 添加用户最看重的特质
            appearance: {
                age: this.generateAge(userData.age),
                height: convertedUserData.appearance?.height || Math.floor(Math.random() * 20) + 160,
                bodyType: convertedUserData.appearance?.bodyType || 'average',
                hairStyle: 'medium',
                hairColor: 'black',
                eyeShape: 'almond',
                skinTone: 'warm',
                style: convertedUserData.appearance?.style || 'elegant',
                // 合并用户的外貌偏好
                ...convertedUserData.appearance
            },
            personality: {
                primary: convertedUserData.personality?.primary || this.getPersonalityFromPriorities(userData.priorities),
                communication: convertedUserData.personality?.communication || 'gentle',
                emotional: 'stable',
                social: 'balanced',
                decision: 'analytical',
                loveLanguage: 'quality-time',
                // 合并用户的性格偏好
                ...convertedUserData.personality
            },
            lifestyle: {
                activities: convertedUserData.lifestyle?.activities || this.getLifestyleFromPriorities(userData.priorities),
                livingStyle: convertedUserData.lifestyle?.livingStyle || 'organized',
                socialCircle: 'medium',
                weekendHabits: ['outdoor'],
                travelPreference: 'adventure',
                foodPreference: 'healthy',
                entertainment: ['movies', 'music'],
                // 合并用户的生活方式偏好
                ...convertedUserData.lifestyle
            },
            career: {
                field: convertedUserData.career?.field || this.getCareerFromPriorities(userData.priorities),
                level: convertedUserData.career?.level || 'mid-career',
                workStyle: 'collaborative',
                ambition: 'moderate',
                workLifeBalance: 'balanced',
                futureGoals: ['specialization'],
                // 合并用户的职业偏好
                ...convertedUserData.career
            },
            values: {
                family: convertedUserData.values?.family || 'traditional',
                finance: convertedUserData.values?.finance || 'conservative',
                education: 'continuous',
                religion: 'respectful',
                politics: 'moderate',
                environmental: 'conscious',
                // 合并用户的价值观偏好
                ...convertedUserData.values
            },
            health: {
                fitness: 'active',
                diet: 'balanced',
                sleep: 'regular',
                stress: 'low',
                hobbies: ['sports'],
                socialHabits: 'moderate'
            }
        };

        // 生成画像
        return await this.generatePortraitImages(analysisData);
    }

    // 转换中文值到英文值
    convertChineseToEnglish(userData) {
        const converted = JSON.parse(JSON.stringify(userData)); // 深拷贝

        // 外貌特征映射
        const appearanceMap = {
            bodyType: {
                '苗条': 'slim',
                '运动型': 'athletic',
                '标准': 'average',
                '丰满': 'curvy'
            },
            style: {
                '优雅': 'elegant',
                '休闲': 'casual',
                '运动': 'sporty',
                '艺术': 'artistic'
            }
        };

        // 性格特征映射
        const personalityMap = {
            communication: {
                '直接': 'direct',
                '温和': 'gentle',
                '幽默': 'humorous'
            }
        };

        // 生活方式映射
        const lifestyleMap = {
            livingStyle: {
                '有条理': 'organized',
                '随性': 'spontaneous',
                '极简': 'minimalist'
            },
            activities: {
                '电影': 'movies',
                '购物': 'shopping',
                '咖啡': 'coffee',
                '园艺': 'gardening',
                '瑜伽': 'yoga',
                '手工': 'crafts',
                '收藏': 'collecting',
                '桌游': 'board-games'
            }
        };

        // 职业映射
        const careerMap = {
            level: {
                '入门': 'entry',
                '中层': 'mid-career',
                '高级': 'senior',
                '专家': 'expert'
            }
        };

        // 价值观映射
        const valuesMap = {
            family: {
                '传统': 'traditional',
                '现代': 'modern',
                '灵活': 'flexible'
            },
            finance: {
                '保守': 'conservative',
                '稳健': 'moderate',
                '积极': 'aggressive'
            }
        };

        // 关系期望映射
        const relationshipMap = {
            commitment: {
                '随缘': 'casual',
                '认真': 'serious',
                '以结婚为目的': 'marriage-focused'
            },
            timeline: {
                '立即': 'immediate',
                '6个月内': '6-months',
                '1-2年': '1-2-years',
                '随缘': 'flexible'
            }
        };

        // 转换外貌特征
        if (converted.appearance) {
            for (const [field, mapping] of Object.entries(appearanceMap)) {
                if (converted.appearance[field] && mapping[converted.appearance[field]]) {
                    converted.appearance[field] = mapping[converted.appearance[field]];
                }
            }
        }

        // 转换性格特征
        if (converted.personality) {
            for (const [field, mapping] of Object.entries(personalityMap)) {
                if (converted.personality[field] && mapping[converted.personality[field]]) {
                    converted.personality[field] = mapping[converted.personality[field]];
                }
            }
        }

        // 转换生活方式
        if (converted.lifestyle) {
            for (const [field, mapping] of Object.entries(lifestyleMap)) {
                if (field === 'activities' && converted.lifestyle[field] && Array.isArray(converted.lifestyle[field])) {
                    // 特殊处理activities数组
                    converted.lifestyle[field] = converted.lifestyle[field].map(activity =>
                        mapping[activity] || activity
                    );
                } else if (converted.lifestyle[field] && mapping[converted.lifestyle[field]]) {
                    converted.lifestyle[field] = mapping[converted.lifestyle[field]];
                }
            }
        }

        // 转换职业
        if (converted.career) {
            for (const [field, mapping] of Object.entries(careerMap)) {
                if (converted.career[field] && mapping[converted.career[field]]) {
                    converted.career[field] = mapping[converted.career[field]];
                }
            }
        }

        // 转换价值观
        if (converted.values) {
            for (const [field, mapping] of Object.entries(valuesMap)) {
                if (converted.values[field] && mapping[converted.values[field]]) {
                    converted.values[field] = mapping[converted.values[field]];
                }
            }
        }

        // 转换关系期望
        if (converted.relationship) {
            for (const [field, mapping] of Object.entries(relationshipMap)) {
                if (converted.relationship[field] && mapping[converted.relationship[field]]) {
                    converted.relationship[field] = mapping[converted.relationship[field]];
                }
            }
        }

        return converted;
    }

    // 根据优先级生成性格特征
    getPersonalityFromPriorities(priorities) {
        const personalityMap = {
            'appearance': ['温柔', '优雅'],
            'personality': ['温柔', '体贴'],
            'intelligence': ['知性', '聪明'],
            'humor': ['活泼', '幽默'],
            'career': ['成熟', '稳重'],
            'family': ['温柔', '体贴'],
            'adventure': ['活泼', '开朗'],
            'stability': ['成熟', '稳重'],
            'reading': ['知性', '文静'],
            'music': ['浪漫', '感性'],
            'sports': ['阳光', '活力'],
            'travel': ['开朗', '冒险'],
            'cooking': ['体贴', '细心'],
            'art': ['浪漫', '创意'],
            'photography': ['细心', '观察力强'],
            'gaming': ['有趣', '年轻'],
            'nature': ['平和', '自然'],
            'technology': ['理性', '聪明'],
            'fashion': ['时尚', '品味'],
            'pets': ['爱心', '温柔'],
            'volunteer': ['善良', '热心'],
            'meditation': ['平静', '内敛'],
            'dancing': ['活泼', '艺术'],
            'writing': ['文静', '才华']
        };
        
        const personality = [];
        priorities.forEach(priority => {
            if (personalityMap[priority]) {
                personality.push(...personalityMap[priority]);
            }
        });
        
        return personality.length > 0 ? personality : ['温柔', '体贴'];
    }

    // 根据优先级生成生活方式
    getLifestyleFromPriorities(priorities) {
        const lifestyleMap = {
            'appearance': ['健身'],
            'personality': ['读书'],
            'intelligence': ['读书'],
            'humor': ['音乐'],
            'career': ['商务'],
            'family': ['烹饪'],
            'adventure': ['旅行'],
            'stability': ['健身'],
            'reading': ['读书', '学习'],
            'music': ['音乐', '艺术'],
            'sports': ['健身', '运动'],
            'travel': ['旅行', '探索'],
            'cooking': ['烹饪', '美食'],
            'art': ['艺术', '创作'],
            'photography': ['摄影', '记录'],
            'gaming': ['游戏', '娱乐'],
            'nature': ['户外', '自然'],
            'technology': ['科技', '创新'],
            'fashion': ['时尚', '购物'],
            'pets': ['宠物', '动物'],
            'volunteer': ['公益', '志愿服务'],
            'meditation': ['冥想', '瑜伽'],
            'dancing': ['舞蹈', '表演'],
            'writing': ['写作', '创作']
        };
        
        const lifestyle = [];
        priorities.forEach(priority => {
            if (lifestyleMap[priority]) {
                lifestyle.push(...lifestyleMap[priority]);
            }
        });
        
        return lifestyle.length > 0 ? lifestyle : ['读书'];
    }

    // 根据优先级生成职业特征
    getCareerFromPriorities(priorities) {
        const careerMap = {
            'appearance': ['艺术'],
            'personality': ['教师'],
            'intelligence': ['科技'],
            'humor': ['艺术'],
            'career': ['商务'],
            'family': ['医疗'],
            'adventure': ['艺术'],
            'stability': ['商务'],
            'reading': ['教育', '出版'],
            'music': ['音乐', '艺术'],
            'sports': ['体育', '健身'],
            'travel': ['旅游', '媒体'],
            'cooking': ['餐饮', '美食'],
            'art': ['艺术', '设计'],
            'photography': ['摄影', '媒体'],
            'gaming': ['游戏', '科技'],
            'nature': ['环保', '农业'],
            'technology': ['科技', 'IT'],
            'fashion': ['时尚', '设计'],
            'pets': ['兽医', '宠物'],
            'volunteer': ['公益', '社工'],
            'meditation': ['健康', '心理咨询'],
            'dancing': ['艺术', '表演'],
            'writing': ['媒体', '出版']
        };
        
        const career = [];
        priorities.forEach(priority => {
            if (careerMap[priority]) {
                career.push(...careerMap[priority]);
            }
        });
        
        return career.length > 0 ? career : ['教师'];
    }
}

/**
 * 姻缘画像表单配置类
 * 用于收集用户的详细偏好参数
 */
class PortraitFormConfig {
    constructor() {
        this.config = {
            // 外貌特征配置
            appearance: {
                age: {
                    type: 'range',
                    label: '理想年龄',
                    min: 18,
                    max: 50,
                    default: 25,
                    step: 1,
                    unit: '岁'
                },
                height: {
                    type: 'range',
                    label: '理想身高',
                    min: 150,
                    max: 190,
                    default: 165,
                    step: 1,
                    unit: 'cm'
                },
                bodyType: {
                    type: 'select',
                    label: '体型偏好',
                    options: [
                        { value: 'slim', label: '苗条' },
                        { value: 'athletic', label: '运动型' },
                        { value: 'average', label: '标准' },
                        { value: 'curvy', label: '丰满' }
                    ],
                    default: 'average'
                },
                hairStyle: {
                    type: 'select',
                    label: '发型偏好',
                    options: [
                        { value: 'short', label: '短发' },
                        { value: 'medium', label: '中发' },
                        { value: 'long', label: '长发' },
                        { value: 'curly', label: '卷发' },
                        { value: 'straight', label: '直发' }
                    ],
                    default: 'medium'
                },
                hairColor: {
                    type: 'select',
                    label: '发色偏好',
                    options: [
                        { value: 'black', label: '黑色' },
                        { value: 'brown', label: '棕色' },
                        { value: 'blonde', label: '金色' },
                        { value: 'dyed', label: '染发' }
                    ],
                    default: 'black'
                },
                eyeShape: {
                    type: 'select',
                    label: '眼型偏好',
                    options: [
                        { value: 'almond', label: '杏仁眼' },
                        { value: 'round', label: '圆眼' },
                        { value: 'upturned', label: '上扬眼' },
                        { value: 'downturned', label: '下垂眼' }
                    ],
                    default: 'almond'
                },
                skinTone: {
                    type: 'select',
                    label: '肤色偏好',
                    options: [
                        { value: 'fair', label: '白皙' },
                        { value: 'warm', label: '暖色' },
                        { value: 'olive', label: '橄榄色' },
                        { value: 'tan', label: '小麦色' }
                    ],
                    default: 'warm'
                },
                style: {
                    type: 'select',
                    label: '风格偏好',
                    options: [
                        { value: 'elegant', label: '优雅' },
                        { value: 'casual', label: '休闲' },
                        { value: 'sporty', label: '运动' },
                        { value: 'artistic', label: '艺术' },
                        { value: 'business', label: '商务' }
                    ],
                    default: 'elegant'
                }
            },

            // 性格特征配置
            personality: {
                primary: {
                    type: 'multiSelect',
                    label: '主要性格特征',
                    options: [
                        { value: '温柔', label: '温柔' },
                        { value: '活泼', label: '活泼' },
                        { value: '知性', label: '知性' },
                        { value: '成熟', label: '成熟' },
                        { value: '浪漫', label: '浪漫' },
                        { value: '阳光', label: '阳光' },
                        { value: '艺术', label: '艺术' },
                        { value: '时尚', label: '时尚' },
                        { value: '爱心', label: '爱心' },
                        { value: '平静', label: '平静' }
                    ],
                    default: ['温柔'],
                    max: 3
                },
                communication: {
                    type: 'select',
                    label: '沟通方式',
                    options: [
                        { value: 'direct', label: '直接' },
                        { value: 'gentle', label: '温和' },
                        { value: 'humorous', label: '幽默' }
                    ],
                    default: 'gentle'
                },
                emotional: {
                    type: 'select',
                    label: '情绪特征',
                    options: [
                        { value: 'stable', label: '稳定' },
                        { value: 'passionate', label: '热情' },
                        { value: 'calm', label: '平静' }
                    ],
                    default: 'stable'
                },
                social: {
                    type: 'select',
                    label: '社交倾向',
                    options: [
                        { value: 'introverted', label: '内向' },
                        { value: 'extroverted', label: '外向' },
                        { value: 'balanced', label: '平衡' }
                    ],
                    default: 'balanced'
                },
                decision: {
                    type: 'select',
                    label: '决策方式',
                    options: [
                        { value: 'intuitive', label: '直觉' },
                        { value: 'analytical', label: '分析' },
                        { value: 'collaborative', label: '协作' }
                    ],
                    default: 'analytical'
                },
                loveLanguage: {
                    type: 'select',
                    label: '爱的语言',
                    options: [
                        { value: 'words', label: '言语' },
                        { value: 'acts', label: '行动' },
                        { value: 'gifts', label: '礼物' },
                        { value: 'touch', label: '肢体接触' },
                        { value: 'quality-time', label: '陪伴时间' }
                    ],
                    default: 'quality-time'
                }
            },

            // 生活方式配置
            lifestyle: {
                activities: {
                    type: 'multiSelect',
                    label: '兴趣爱好',
                    options: [
                        { value: '健身', label: '健身' },
                        { value: '旅行', label: '旅行' },
                        { value: '读书', label: '读书' },
                        { value: '音乐', label: '音乐' },
                        { value: '运动', label: '运动' },
                        { value: '烹饪', label: '烹饪' },
                        { value: '摄影', label: '摄影' },
                        { value: '游戏', label: '游戏' },
                        { value: '户外', label: '户外' },
                        { value: '时尚', label: '时尚' },
                        { value: '宠物', label: '宠物' },
                        { value: '冥想', label: '冥想' },
                        { value: '舞蹈', label: '舞蹈' },
                        { value: '写作', label: '写作' }
                    ],
                    default: ['健身'],
                    max: 5
                },
                livingStyle: {
                    type: 'select',
                    label: '生活风格',
                    options: [
                        { value: 'organized', label: '有条理' },
                        { value: 'spontaneous', label: '随性' },
                        { value: 'minimalist', label: '极简' }
                    ],
                    default: 'organized'
                },
                socialCircle: {
                    type: 'select',
                    label: '社交圈',
                    options: [
                        { value: 'small', label: '小圈子' },
                        { value: 'medium', label: '中等' },
                        { value: 'large', label: '大圈子' }
                    ],
                    default: 'medium'
                },
                weekendHabits: {
                    type: 'multiSelect',
                    label: '周末习惯',
                    options: [
                        { value: 'outdoor', label: '户外活动' },
                        { value: 'indoor', label: '室内活动' },
                        { value: 'social', label: '社交聚会' },
                        { value: 'relax', label: '放松休息' },
                        { value: 'study', label: '学习充电' }
                    ],
                    default: ['outdoor'],
                    max: 3
                },
                travelPreference: {
                    type: 'select',
                    label: '旅行偏好',
                    options: [
                        { value: 'adventure', label: '冒险' },
                        { value: 'luxury', label: '奢华' },
                        { value: 'cultural', label: '文化' },
                        { value: 'budget', label: '经济' }
                    ],
                    default: 'adventure'
                },
                foodPreference: {
                    type: 'select',
                    label: '饮食偏好',
                    options: [
                        { value: 'healthy', label: '健康' },
                        { value: 'gourmet', label: '美食' },
                        { value: 'simple', label: '简单' },
                        { value: 'diverse', label: '多样' }
                    ],
                    default: 'healthy'
                },
                entertainment: {
                    type: 'multiSelect',
                    label: '娱乐方式',
                    options: [
                        { value: 'movies', label: '电影' },
                        { value: 'music', label: '音乐' },
                        { value: 'tv', label: '电视剧' },
                        { value: 'games', label: '游戏' },
                        { value: 'sports', label: '运动' },
                        { value: 'art', label: '艺术' }
                    ],
                    default: ['movies', 'music'],
                    max: 4
                }
            },

            // 职业发展配置
            career: {
                field: {
                    type: 'multiSelect',
                    label: '行业领域',
                    options: [
                        { value: '教育', label: '教育' },
                        { value: '医生', label: '医疗' },
                        { value: '艺术', label: '艺术' },
                        { value: '商务', label: '商务' },
                        { value: '科技', label: '科技' },
                        { value: '音乐', label: '音乐' },
                        { value: '体育', label: '体育' },
                        { value: '旅游', label: '旅游' },
                        { value: '餐饮', label: '餐饮' },
                        { value: '设计', label: '设计' },
                        { value: '媒体', label: '媒体' },
                        { value: '游戏', label: '游戏' },
                        { value: '环保', label: '环保' },
                        { value: '时尚', label: '时尚' },
                        { value: '兽医', label: '兽医' },
                        { value: '公益', label: '公益' },
                        { value: '健康', label: '健康' },
                        { value: '表演', label: '表演' },
                        { value: '出版', label: '出版' }
                    ],
                    default: ['教育'],
                    max: 3
                },
                level: {
                    type: 'select',
                    label: '职业阶段',
                    options: [
                        { value: 'entry', label: '入门' },
                        { value: 'mid-career', label: '中层' },
                        { value: 'senior', label: '高级' },
                        { value: 'expert', label: '专家' }
                    ],
                    default: 'mid-career'
                },
                workStyle: {
                    type: 'select',
                    label: '工作风格',
                    options: [
                        { value: 'independent', label: '独立' },
                        { value: 'collaborative', label: '协作' },
                        { value: 'leadership', label: '领导' }
                    ],
                    default: 'collaborative'
                },
                ambition: {
                    type: 'select',
                    label: '事业心',
                    options: [
                        { value: 'high', label: '高' },
                        { value: 'moderate', label: '中等' },
                        { value: 'balanced', label: '平衡' }
                    ],
                    default: 'moderate'
                },
                workLifeBalance: {
                    type: 'select',
                    label: '工作生活平衡',
                    options: [
                        { value: 'work-focused', label: '工作为重' },
                        { value: 'balanced', label: '平衡' },
                        { value: 'life-focused', label: '生活为重' }
                    ],
                    default: 'balanced'
                },
                futureGoals: {
                    type: 'multiSelect',
                    label: '未来规划',
                    options: [
                        { value: 'specialization', label: '专业化' },
                        { value: 'management', label: '管理' },
                        { value: 'entrepreneurship', label: '创业' },
                        { value: 'research', label: '研究' },
                        { value: 'teaching', label: '教学' }
                    ],
                    default: ['specialization'],
                    max: 2
                }
            },

            // 价值观配置
            values: {
                family: {
                    type: 'select',
                    label: '家庭观念',
                    options: [
                        { value: 'traditional', label: '传统' },
                        { value: 'modern', label: '现代' },
                        { value: 'flexible', label: '灵活' }
                    ],
                    default: 'traditional'
                },
                finance: {
                    type: 'select',
                    label: '理财观念',
                    options: [
                        { value: 'conservative', label: '保守' },
                        { value: 'moderate', label: '稳健' },
                        { value: 'aggressive', label: '积极' }
                    ],
                    default: 'conservative'
                },
                education: {
                    type: 'select',
                    label: '教育观念',
                    options: [
                        { value: 'formal', label: '正规' },
                        { value: 'continuous', label: '持续' },
                        { value: 'practical', label: '实用' }
                    ],
                    default: 'continuous'
                },
                religion: {
                    type: 'select',
                    label: '宗教观念',
                    options: [
                        { value: 'practicing', label: '虔诚' },
                        { value: 'respectful', label: '尊重' },
                        { value: 'secular', label: '世俗' }
                    ],
                    default: 'respectful'
                },
                politics: {
                    type: 'select',
                    label: '政治倾向',
                    options: [
                        { value: 'conservative', label: '保守' },
                        { value: 'moderate', label: '温和' },
                        { value: 'liberal', label: '开放' }
                    ],
                    default: 'moderate'
                },
                environmental: {
                    type: 'select',
                    label: '环保意识',
                    options: [
                        { value: 'conscious', label: '强烈' },
                        { value: 'moderate', label: '一般' },
                        { value: 'indifferent', label: '无所谓' }
                    ],
                    default: 'conscious'
                }
            },

            // 关系期望配置
            relationship: {
                commitment: {
                    type: 'select',
                    label: '承诺程度',
                    options: [
                        { value: 'casual', label: '随缘' },
                        { value: 'serious', label: '认真' },
                        { value: 'marriage-focused', label: '以结婚为目的' }
                    ],
                    default: 'serious'
                },
                timeline: {
                    type: 'select',
                    label: '时间规划',
                    options: [
                        { value: 'immediate', label: '立即' },
                        { value: '6-months', label: '6个月内' },
                        { value: '1-2-years', label: '1-2年' },
                        { value: 'flexible', label: '随缘' }
                    ],
                    default: '1-2-years'
                },
                living: {
                    type: 'select',
                    label: '居住安排',
                    options: [
                        { value: 'together', label: '同居' },
                        { value: 'separate', label: '分开住' },
                        { value: 'flexible', label: '灵活' }
                    ],
                    default: 'separate'
                },
                children: {
                    type: 'select',
                    label: '生育观念',
                    options: [
                        { value: 'want', label: '想要' },
                        { value: 'open', label: '开放' },
                        { value: 'not-now', label: '暂时不要' },
                        { value: 'never', label: '不要' }
                    ],
                    default: 'open'
                },
                marriage: {
                    type: 'select',
                    label: '婚姻观念',
                    options: [
                        { value: 'traditional', label: '传统' },
                        { value: 'modern', label: '现代' },
                        { value: 'flexible', label: '灵活' }
                    ],
                    default: 'traditional'
                },
                independence: {
                    type: 'select',
                    label: '独立性',
                    options: [
                        { value: 'high', label: '高' },
                        { value: 'balanced', label: '平衡' },
                        { value: 'interdependent', label: '相互依赖' }
                    ],
                    default: 'balanced'
                }
            },

            // 文化背景配置
            cultural: {
                background: {
                    type: 'select',
                    label: '文化背景',
                    options: [
                        { value: 'chinese', label: '中国' },
                        { value: 'international', label: '国际' },
                        { value: 'mixed', label: '混合' }
                    ],
                    default: 'chinese'
                },
                language: {
                    type: 'multiSelect',
                    label: '语言能力',
                    options: [
                        { value: 'mandarin', label: '普通话' },
                        { value: 'english', label: '英语' },
                        { value: 'cantonese', label: '粤语' },
                        { value: 'other', label: '其他' }
                    ],
                    default: ['mandarin'],
                    max: 3
                },
                education: {
                    type: 'select',
                    label: '教育背景',
                    options: [
                        { value: 'high-school', label: '高中' },
                        { value: 'university', label: '大学' },
                        { value: 'graduate', label: '研究生' }
                    ],
                    default: 'university'
                },
                region: {
                    type: 'select',
                    label: '地域',
                    options: [
                        { value: 'northern', label: '北方' },
                        { value: 'southern', label: '南方' },
                        { value: 'eastern', label: '东部' },
                        { value: 'western', label: '西部' },
                        { value: 'overseas', label: '海外' }
                    ],
                    default: 'eastern'
                },
                traditions: {
                    type: 'select',
                    label: '传统观念',
                    options: [
                        { value: 'traditional', label: '传统' },
                        { value: 'modern', label: '现代' },
                        { value: 'mixed', label: '混合' }
                    ],
                    default: 'modern'
                },
                socialClass: {
                    type: 'select',
                    label: '社会阶层',
                    options: [
                        { value: 'working', label: '工薪' },
                        { value: 'middle', label: '中产' },
                        { value: 'upper', label: '富裕' }
                    ],
                    default: 'middle'
                }
            },

            // 健康生活习惯配置
            health: {
                fitness: {
                    type: 'select',
                    label: '健身习惯',
                    options: [
                        { value: 'active', label: '积极' },
                        { value: 'moderate', label: '一般' },
                        { value: 'casual', label: '偶尔' }
                    ],
                    default: 'active'
                },
                diet: {
                    type: 'select',
                    label: '饮食习惯',
                    options: [
                        { value: 'vegetarian', label: '素食' },
                        { value: 'balanced', label: '均衡' },
                        { value: 'flexible', label: '灵活' }
                    ],
                    default: 'balanced'
                },
                sleep: {
                    type: 'select',
                    label: '作息习惯',
                    options: [
                        { value: 'early-bird', label: '早睡早起' },
                        { value: 'night-owl', label: '夜猫子' },
                        { value: 'regular', label: '规律' }
                    ],
                    default: 'regular'
                },
                stress: {
                    type: 'select',
                    label: '压力水平',
                    options: [
                        { value: 'low', label: '低' },
                        { value: 'moderate', label: '中等' },
                        { value: 'high', label: '高' }
                    ],
                    default: 'low'
                }
            }
        };
    }

    // 获取配置
    getConfig() {
        return this.config;
    }

    // 获取默认值
    getDefaults() {
        const defaults = {};
        for (const category in this.config) {
            defaults[category] = {};
            for (const field in this.config[category]) {
                const fieldConfig = this.config[category][field];
                if (fieldConfig.default !== undefined) {
                    defaults[category][field] = fieldConfig.default;
                }
            }
        }
        return defaults;
    }

    // 验证表单数据
    validateFormData(formData) {
        const validated = {};
        for (const category in this.config) {
            validated[category] = {};
            for (const field in this.config[category]) {
                const fieldConfig = this.config[category][field];
                const value = formData[category]?.[field];
                
                if (value !== undefined) {
                    validated[category][field] = value;
                } else if (fieldConfig.default !== undefined) {
                    validated[category][field] = fieldConfig.default;
                }
            }
        }
        return validated;
    }

    // 生成表单HTML
    generateFormHTML() {
        let html = '<div class="portrait-form">';
        
        for (const category in this.config) {
            html += `<div class="form-category">
                <h3 class="category-title">${this.getCategoryTitle(category)}</h3>
                <div class="category-fields">`;
            
            for (const field in this.config[category]) {
                const fieldConfig = this.config[category][field];
                html += this.generateFieldHTML(category, field, fieldConfig);
            }
            
            html += '</div></div>';
        }
        
        html += '</div>';
        return html;
    }

    // 获取分类标题
    getCategoryTitle(category) {
        const titles = {
            appearance: '外貌特征',
            personality: '性格特征',
            lifestyle: '生活方式',
            career: '职业发展',
            values: '价值观',
            relationship: '关系期望',
            cultural: '文化背景',
            health: '健康习惯'
        };
        return titles[category] || category;
    }

    // 生成字段HTML
    generateFieldHTML(category, field, config) {
        const fieldId = `${category}_${field}`;
        const fieldName = `${category}[${field}]`;
        
        let html = `<div class="form-field">
            <label for="${fieldId}" class="field-label">${config.label}</label>`;
        
        switch (config.type) {
            case 'range':
                html += `<input type="range" id="${fieldId}" name="${fieldName}" 
                    min="${config.min}" max="${config.max}" step="${config.step}" 
                    value="${config.default}" class="range-input">
                <span class="range-value">${config.default}${config.unit}</span>`;
                break;
                
            case 'select':
                html += `<select id="${fieldId}" name="${fieldName}" class="select-input">`;
                for (const option of config.options) {
                    const selected = option.value === config.default ? 'selected' : '';
                    html += `<option value="${option.value}" ${selected}>${option.label}</option>`;
                }
                html += '</select>';
                break;
                
            case 'multiSelect':
                html += `<div class="multi-select-container">`;
                for (const option of config.options) {
                    const checked = config.default.includes(option.value) ? 'checked' : '';
                    html += `<label class="checkbox-label">
                        <input type="checkbox" name="${fieldName}" value="${option.value}" ${checked}>
                        <span class="checkbox-text">${option.label}</span>
                    </label>`;
                }
                html += '</div>';
                break;
        }
        
        html += '</div>';
        return html;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PortraitAI, PortraitFormConfig };
} else {
    // 浏览器环境，暴露到全局作用域
    window.PortraitAI = PortraitAI;
    if (typeof PortraitFormConfig !== 'undefined') {
        window.PortraitFormConfig = PortraitFormConfig;
    }
}