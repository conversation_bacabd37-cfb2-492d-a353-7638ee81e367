# 易海堂算命网 - 移动端H5界面

一个专业的移动端算命占卜平台，基于您提供的设计图完美还原，提供八字测算、星座运势、塔罗占卜、姓名测试等多种服务。

## 🎨 设计特色

- **移动端优先**: 完全按照提供的设计图制作，100%还原移动端界面
- **粉色渐变主题**: 采用现代化的粉色渐变配色方案
- **专业UI设计**: 包含轮播图、圆形图标、卡片布局等丰富组件
- **完整功能模块**: 八字测算、星座运势、起名测名、大师亲算等

## 📱 页面结构

### 主要区域
- **顶部导航**: 闻亭、首页、星座、命盘、起名、大师亲算、热门
- **功能按钮**: 八字测算、事业运势、数字解析、紫微斗数等8个主要功能
- **轮播图**: 姓名详批等服务推广
- **圆形服务图标**: 八字精批、八字合婚、生辰详批、塔罗占卜等8个服务
- **推荐服务**: 6个网格布局的服务卡片
- **当前大师**: 大师介绍和服务标签
- **2025年运势专题**: 蛇年运势转盘设计
- **星座运势**: 12星座网格布局
- **综合测算**: 包含脱单测算和7个详细测算项目
- **底部导航**: 首页、订单查询、会员分销

## 🗂️ 项目结构

```
AI-fortune-telling/
├── index.html              # 主页面
├── package.json            # 项目配置
├── README.md              # 项目说明
├── css/                   # 样式文件
│   ├── main.css          # 主样式文件
│   └── components/       # 组件样式
├── js/                   # JavaScript文件
│   ├── main.js          # 主脚本
│   └── modules/         # 功能模块
│       ├── carousel.js   # 轮播图模块
│       └── services.js   # 服务功能模块
├── pages/               # 子页面
│   ├── home/           # 首页相关
│   ├── astrology/      # 星座页面
│   ├── fortune/        # 算命页面
│   ├── naming/         # 起名页面
│   ├── master/         # 大师页面
│   └── hot/           # 热门页面
├── images/             # 图片资源
└── assets/            # 其他资源文件
```

## ✨ 功能特性

### 1. 八字测算系统
- **八字精批**: 深度解析生辰八字，预测人生运势
- **八字合婚**: 测算两人缘分匹配度
- **终身运程**: 全面分析人生各阶段运势
- **未来运势**: 当年运势详细解读

### 2. 姓名测试
- **姓名详批**: 全面分析姓名寓意和运势
- **姓名配对**: 测试两人姓名匹配度
- **宝宝起名**: 根据八字起吉祥好名
- **公司起名**: 企业命名咨询服务

### 3. 运势预测
- **事业运势**: 职业发展和财运分析
- **桃花运势**: 爱情运势和桃花机会
- **健康运势**: 健康状况预测
- **财运分析**: 财富运程详解

### 4. 星座塔罗
- **星座运势**: 12星座每日运势
- **塔罗占卜**: 交互式塔罗牌抽取
- **生肖算命**: 12生肖运程分析

### 5. 移动端体验
- **触摸优化**: 支持手势滑动和触摸反馈
- **响应式设计**: 适配各种移动设备
- **快速加载**: 优化的资源加载机制
- **离线缓存**: 支持离线浏览历史记录

## 🛠️ 技术栈

- **HTML5**: 语义化标签，移动端优化
- **CSS3**: Flexbox/Grid布局，CSS变量，动画效果
- **JavaScript ES6+**: 模块化开发，原生JS实现
- **PWA支持**: 渐进式Web应用特性
- **本地存储**: localStorage保存用户数据

## 🎯 核心功能

### 轮播图系统
- 自动播放和手动切换
- 触摸滑动支持
- 圆点指示器
- 平滑过渡动画

### 测算系统
- 表单验证和数据收集
- 模拟算命算法
- 结果展示和保存
- 历史记录管理

### 交互体验
- 点击反馈动画
- 弹窗和通知系统
- 平滑滚动导航
- 触摸优化

## 🚀 快速开始

### 环境要求
- Node.js 14.0+
- 现代浏览器支持

### 安装运行
```bash
# 克隆项目
git clone [项目地址]

# 进入项目目录
cd AI-fortune-telling

# 安装依赖
npm install

# 启动开发服务器
npm start

# 浏览器访问
http://localhost:3000
```

### 构建部署
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📊 性能优化

- **代码分割**: 按需加载模块
- **图片优化**: WebP格式，懒加载
- **缓存策略**: 合理的缓存机制
- **压缩优化**: CSS/JS压缩
- **CDN加速**: 静态资源CDN部署

## 🔧 配置说明

### 服务配置
- 修改`js/modules/services.js`中的服务数据
- 配置价格、描述、功能特性等信息

### 样式定制
- `css/main.css`中的CSS变量可快速修改主题色
- 组件样式在`css/components/`目录下独立管理

### 功能扩展
- 在`js/modules/`下添加新的功能模块
- 在`pages/`下创建新的子页面

## 🎨 设计规范

### 色彩方案
- 主色调: `#FF69B4` (粉色)
- 渐变色: `linear-gradient(135deg, #FFB6C1, #FF69B4)`
- 辅助色: 橙色、蓝色、紫色、绿色等
- 文字色: `#333` (深灰), `#666` (中灰)

### 字体规范
- 主字体: Noto Sans SC
- 备用字体: -apple-system, BlinkMacSystemFont, Segoe UI
- 字号范围: 10px - 24px

### 间距规范
- 基础间距: 15px
- 组件间距: 20px
- 圆角半径: 15px (大), 8px (小)

## 📱 移动端适配

- 视口设置: `width=device-width, initial-scale=1.0, user-scalable=no`
- 触摸优化: 44px以上的触摸目标
- 手势支持: 滑动、点击反馈
- 性能优化: 防抖、节流处理

## 🔐 数据安全

- 本地存储加密
- 表单数据验证
- XSS防护
- 隐私保护措施

## 📈 SEO优化

- 语义化HTML结构
- Meta标签优化
- 结构化数据
- 移动端友好

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 商务微信: 18088245313
- 项目地址: [GitHub仓库链接]
- 在线演示: [演示地址]

---

**注意**: 本项目仅供娱乐和学习使用，算命结果不具有科学依据，请理性对待。 