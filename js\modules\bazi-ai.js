/**
 * 八字精批AI分析模块
 * 基于传统八字命理学进行全面分析
 */
class BaziAnalysisAI {
    constructor() {
        this.initializeKnowledgeBase();
    }

    // 初始化八字命理学知识库
    initializeKnowledgeBase() {
        this.baziKnowledge = {
            // 天干配置
            tiangan: {
                jia: { name: '甲', element: '木', type: 'yang', traits: ['主导性强', '有领导才能'] },
                yi: { name: '乙', element: '木', type: 'yin', traits: ['柔韧坚韧', '适应性强'] },
                bing: { name: '丙', element: '火', type: 'yang', traits: ['热情开朗', '富有活力'] },
                ding: { name: '丁', element: '火', type: 'yin', traits: ['内敛温暖', '思维敏捷'] },
                wu: { name: '戊', element: '土', type: 'yang', traits: ['稳重踏实', '可靠诚信'] },
                ji: { name: '己', element: '土', type: 'yin', traits: ['细腻周到', '善于协调'] },
                geng: { name: '庚', element: '金', type: 'yang', traits: ['果断坚毅', '正义感强'] },
                xin: { name: '辛', element: '金', type: 'yin', traits: ['精致敏感', '审美能力强'] },
                ren: { name: '壬', element: '水', type: 'yang', traits: ['智慧灵活', '适应力强'] },
                gui: { name: '癸', element: '水', type: 'yin', traits: ['深邃内敛', '直觉敏锐'] }
            },

            // 地支配置
            dizhi: {
                zi: { name: '子', element: '水', animal: '鼠' },
                chou: { name: '丑', element: '土', animal: '牛' },
                yin: { name: '寅', element: '木', animal: '虎' },
                mao: { name: '卯', element: '木', animal: '兔' },
                chen: { name: '辰', element: '土', animal: '龙' },
                si: { name: '巳', element: '火', animal: '蛇' },
                wu: { name: '午', element: '火', animal: '马' },
                wei: { name: '未', element: '土', animal: '羊' },
                shen: { name: '申', element: '金', animal: '猴' },
                you: { name: '酉', element: '金', animal: '鸡' },
                xu: { name: '戌', element: '土', animal: '狗' },
                hai: { name: '亥', element: '水', animal: '猪' }
            },

            // 五行特征
            wuxing: {
                wood: { name: '木', trait: '仁慈', careers: ['教育', '文化', '医疗'], colors: ['绿色', '青色'] },
                fire: { name: '火', trait: '礼貌', careers: ['娱乐', '广告', '电子'], colors: ['红色', '紫色'] },
                earth: { name: '土', trait: '诚信', careers: ['房地产', '农业', '建筑'], colors: ['黄色', '土色'] },
                metal: { name: '金', trait: '义气', careers: ['金融', '机械', '汽车'], colors: ['白色', '金色'] },
                water: { name: '水', trait: '智慧', careers: ['物流', '旅游', '通讯'], colors: ['黑色', '蓝色'] }
            },

            // 十神配置
            shishen: {
                bijian: { name: '比肩', meaning: '兄弟朋友', traits: ['自立自强', '竞争意识', '团队合作'] },
                jiecai: { name: '劫财', meaning: '争夺竞争', traits: ['冲动行事', '好胜心强', '易有争执'] },
                shishan: { name: '食神', meaning: '才华表达', traits: ['多才多艺', '表达能力强', '享受生活'] },
                shangguang: { name: '伤官', meaning: '创新反叛', traits: ['创新精神', '不拘一格', '容易叛逆'] },
                piancan: { name: '偏财', meaning: '横财机遇', traits: ['投资眼光', '商业头脑', '偏财运好'] },
                zhengcai: { name: '正财', meaning: '正当收入', traits: ['稳定收入', '理财有道', '务实节俭'] },
                qisha: { name: '七杀', meaning: '权威压力', traits: ['权威性格', '承受压力', '执行力强'] },
                zhengguan: { name: '正官', meaning: '地位名声', traits: ['责任感强', '地位崇高', '正直守法'] },
                pianyin: { name: '偏印', meaning: '直觉智慧', traits: ['直觉敏锐', '学习能力强', '思维独特'] },
                zhengyin: { name: '正印', meaning: '知识学问', traits: ['学识渊博', '贵人相助', '母爱关怀'] }
            },

            // 大运流年分析
            dayun: {
                description: '大运是人生运势的十年周期，影响人生各个阶段的发展',
                cycles: [
                    { age: '0-9', phase: '童年期', focus: '健康成长，基础教育' },
                    { age: '10-19', phase: '青少年期', focus: '学习成长，性格形成' },
                    { age: '20-29', phase: '青年期', focus: '事业起步，恋爱结婚' },
                    { age: '30-39', phase: '壮年期', focus: '事业发展，家庭建设' },
                    { age: '40-49', phase: '中年期', focus: '事业巅峰，财富积累' },
                    { age: '50-59', phase: '成熟期', focus: '稳固发展，传承经验' },
                    { age: '60+', phase: '老年期', focus: '享受生活，颐养天年' }
                ]
            },

            // 2025年流年分析
            liunian2025: {
                year: '乙巳年',
                tiangan: '乙木',
                dizhi: '巳火',
                characteristics: '木火通明，利于文化创意和科技发展',
                favorable: ['教育', '文化', '科技', '新能源', '环保'],
                unfavorable: ['传统制造', '重工业', '资源开采']
            }
        };
    }

    // 计算生辰八字（简化算法）
    calculateBazi(birthDate, birthHour) {
        const date = new Date(birthDate);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        // 简化的天干地支计算
        const yearGan = Object.keys(this.baziKnowledge.tiangan)[(year - 4) % 10];
        const yearZhi = Object.keys(this.baziKnowledge.dizhi)[(year - 4) % 12];
        const monthGan = Object.keys(this.baziKnowledge.tiangan)[(month + year) % 10];
        const monthZhi = Object.keys(this.baziKnowledge.dizhi)[(month + 1) % 12];
        const dayGan = Object.keys(this.baziKnowledge.tiangan)[(year * 365 + month * 30 + day) % 10];
        const dayZhi = Object.keys(this.baziKnowledge.dizhi)[(year * 365 + month * 30 + day) % 12];
        
        const hourZhi = birthHour;
        const hourGan = Object.keys(this.baziKnowledge.tiangan)[(Object.keys(this.baziKnowledge.tiangan).indexOf(dayGan) * 2) % 10];

        return {
            year: { gan: yearGan, zhi: yearZhi },
            month: { gan: monthGan, zhi: monthZhi },
            day: { gan: dayGan, zhi: dayZhi },
            hour: { gan: hourGan, zhi: hourZhi },
            dayGan: dayGan
        };
    }

    // 分析五行分布
    analyzeFiveElements(bazi) {
        const elements = { 木: 0, 火: 0, 土: 0, 金: 0, 水: 0 };
        
        // 统计各柱的五行
        Object.values(bazi).forEach(pillar => {
            if (pillar.gan && this.baziKnowledge.tiangan[pillar.gan]) {
                const ganElement = this.baziKnowledge.tiangan[pillar.gan].element;
                elements[ganElement]++;
            }
            if (pillar.zhi && this.baziKnowledge.dizhi[pillar.zhi]) {
                const zhiElement = this.baziKnowledge.dizhi[pillar.zhi].element;
                elements[zhiElement]++;
            }
        });

        const sorted = Object.entries(elements).sort((a, b) => b[1] - a[1]);
        
        return {
            elements,
            strongest: sorted[0][0],
            weakest: sorted[sorted.length - 1][0]
        };
    }

    // 显示八字排盘
    displayBaziPillars(bazi) {
        document.getElementById('yearPillar').textContent = this.baziKnowledge.tiangan[bazi.year.gan].name + this.baziKnowledge.dizhi[bazi.year.zhi].name;
        document.getElementById('monthPillar').textContent = this.baziKnowledge.tiangan[bazi.month.gan].name + this.baziKnowledge.dizhi[bazi.month.zhi].name;
        document.getElementById('dayPillar').textContent = this.baziKnowledge.tiangan[bazi.day.gan].name + this.baziKnowledge.dizhi[bazi.day.zhi].name;
        document.getElementById('hourPillar').textContent = this.baziKnowledge.tiangan[bazi.hour.gan].name + this.baziKnowledge.dizhi[bazi.hour.zhi].name;
    }

    // 生成AI提示词
    generatePrompt(userData, bazi, fiveElements) {
        const { userName, gender, birthDate, birthHour } = userData;
        const dayGanInfo = this.baziKnowledge.tiangan[bazi.dayGan];
        const genderText = gender === 'male' ? '男性' : '女性';
        const score = this.calculateBaziScore(bazi, fiveElements);

        // 转换为中文天干地支
        const yearGanChinese = this.baziKnowledge.tiangan[bazi.year.gan].name;
        const yearZhiChinese = this.baziKnowledge.dizhi[bazi.year.zhi].name;
        const monthGanChinese = this.baziKnowledge.tiangan[bazi.month.gan].name;
        const monthZhiChinese = this.baziKnowledge.dizhi[bazi.month.zhi].name;
        const dayGanChinese = this.baziKnowledge.tiangan[bazi.day.gan].name;
        const dayZhiChinese = this.baziKnowledge.dizhi[bazi.day.zhi].name;
        const hourGanChinese = this.baziKnowledge.tiangan[bazi.hour.gan].name;
        const hourZhiChinese = this.baziKnowledge.dizhi[bazi.hour.zhi].name;

        this.displayBaziPillars(bazi);

        return `请为${userName}（${genderText}，生于${birthDate}，${this.baziKnowledge.dizhi[birthHour].name}时）进行八字精批分析。

八字信息：
- 年柱：${yearGanChinese}${yearZhiChinese}
- 月柱：${monthGanChinese}${monthZhiChinese}
- 日柱：${dayGanChinese}${dayZhiChinese}
- 时柱：${hourGanChinese}${hourZhiChinese}
- 日主：${dayGanInfo.name}${dayGanInfo.element}
- 五行分布：${Object.entries(fiveElements.elements).map(([k,v]) => `${k}${v}个`).join('、')}
- 最强五行：${fiveElements.strongest}
- 最弱五行：${fiveElements.weakest}

请严格按照以下JSON格式返回分析结果，不要包含任何JSON之外的文本。注意：八字必须使用中文汉字，不要使用拼音：

{
  "basic": {
    "name": "${userName}",
    "gender": "${gender}",
    "birthDate": "${birthDate}",
    "birthHour": "${birthHour}",
    "bazi": "${yearGanChinese}${yearZhiChinese} ${monthGanChinese}${monthZhiChinese} ${dayGanChinese}${dayZhiChinese} ${hourGanChinese}${hourZhiChinese}",
    "dayMaster": "${dayGanInfo.name}${dayGanInfo.element}",
    "score": ${score}
  },
  "fiveElements": {
    "distribution": ${JSON.stringify(fiveElements.elements)},
    "strongest": "${fiveElements.strongest}",
    "weakest": "${fiveElements.weakest}",
    "balance": "五行平衡状况分析"
  },
  "sections": {
    "pattern": "八字格局分析：详细分析日主强弱、五行平衡、格局特点等，要求200字以上",
    "character": "性格特征分析：详细分析性格优缺点、行为特征、思维方式等，要求200字以上",
    "career": "事业运势分析：详细分析适合的职业方向、事业发展趋势、工作建议等，要求200字以上",
    "wealth": "财运分析：详细分析财运状况、理财建议、投资方向等，要求200字以上",
    "marriage": "感情婚姻分析：详细分析感情状况、婚姻运势、配偶特征等，要求200字以上",
    "health": "健康运势分析：详细分析健康状况、养生建议、注意事项等，要求200字以上",
    "timing": "未来运势分析：详细分析今年运势、机遇挑战、发展建议等，要求200字以上",
    "enhancement": "开运建议：详细提供开运方法、幸运颜色、有利方位、吉祥物品等建议，要求200字以上"
  },
  "recommendations": [
    "具体建议1",
    "具体建议2",
    "具体建议3"
  ],
  "luckyElements": {
    "colors": ["幸运颜色1", "幸运颜色2"],
    "directions": ["有利方位1", "有利方位2"],
    "numbers": ["幸运数字1", "幸运数字2"],
    "stones": ["幸运宝石1", "幸运宝石2"]
  }
}

请确保返回的是有效的JSON格式，每个分析部分都要详细且不少于200字。`;
    }

    // AI八字分析
    async analyzeBaziWithAI(userData) {
        try {
            // 计算八字
            const bazi = this.calculateBazi(userData.birthDate, userData.birthHour);
            const fiveElements = this.analyzeFiveElements(bazi);
            const prompt = this.generatePrompt(userData, bazi, fiveElements);
            const systemPrompt = "你是一个专业的八字命理分析师，精通传统命理学理论，能够进行准确的八字分析和运势预测。请严格按照用户要求的JSON格式返回分析结果。重要：八字信息必须使用中文汉字（如甲子、乙丑），绝对不要使用拼音格式（如jia zi、yi chou）。";

            // 使用统一AI调用接口，自动根据配置选择服务
            const aiResponse = await window.aIService.callAI(prompt, systemPrompt, {
                enableFallback: true  // 启用服务降级
            });
            
            return this.parseAIResponse(aiResponse, userData, bazi, fiveElements);
        } catch (error) {
            console.error('AI分析失败:', error);
            const bazi = this.calculateBazi(userData.birthDate, userData.birthHour);
            const fiveElements = this.analyzeFiveElements(bazi);
            return this.generateLocalAnalysis(userData, bazi, fiveElements);
        }
    }

    // 解析AI响应
    parseAIResponse(aiResponse, userData, bazi, fiveElements) {
        try {
            // 尝试解析JSON格式的响应
            aiResponse = this.cleanAIResponse(aiResponse)
            const parsedResponse = JSON.parse(aiResponse);

            // 验证JSON结构是否完整
            if (parsedResponse.basic && parsedResponse.sections) {
                return {
                    success: true,
                    baziAnalysis: {
                        method: 'ai',
                        basic: parsedResponse.basic,
                        fiveElements: parsedResponse.fiveElements || {
                            distribution: fiveElements.elements,
                            strongest: fiveElements.strongest,
                            weakest: fiveElements.weakest,
                            balance: this.analyzeFiveElementsBalance(fiveElements)
                        },
                        sections: parsedResponse.sections,
                        recommendations: parsedResponse.recommendations || [],
                        luckyElements: parsedResponse.luckyElements || this.generateDefaultLuckyElements(fiveElements),
                        timestamp: new Date().toISOString()
                    }
                };
            }
        } catch (error) {
            console.warn('JSON解析失败，尝试文本解析:', error);
        }

        // 如果JSON解析失败，降级到文本解析
        return this.parseTextResponse(aiResponse, userData, bazi, fiveElements);
    }

        // 清理AI响应，提取JSON部分
    cleanAIResponse(response) {
        console.log('🧹 开始清理AI响应...');

        let cleanedContent = response;

        // 步骤1: 如果响应包含 "content": 开头，说明是包装格式
        if (response.includes('"content":')) {
            console.log('📦 检测到包装格式响应');

            // 尝试提取content字段的值
            try {
                const contentMatch = response.match(/"content":\s*"([^"]*(?:\\.[^"]*)*)"/);
                if (contentMatch && contentMatch[1]) {
                    // 解码转义字符
                    cleanedContent = contentMatch[1]
                        .replace(/\\n/g, '\n')
                        .replace(/\\"/g, '"')
                        .replace(/\\\\/g, '\\');
                    console.log('✅ 提取到content内容:', cleanedContent);
                }
            } catch (error) {
                console.warn('⚠️ 提取content失败:', error);
            }
        }

        // 步骤2: 移除markdown代码块标记
        if (cleanedContent.includes('```json')) {
            console.log('📝 检测到markdown代码块');
            const jsonMatch = cleanedContent.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch && jsonMatch[1]) {
                cleanedContent = jsonMatch[1].trim();
                console.log('✂️ 提取markdown中的JSON:', cleanedContent);
            }
        }

        // 步骤3: 查找JSON对象的开始和结束
        let jsonStart = cleanedContent.indexOf('{');
        let jsonEnd = cleanedContent.lastIndexOf('}');

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
            const jsonPart = cleanedContent.substring(jsonStart, jsonEnd + 1);
            console.log('🎯 最终提取的JSON:', jsonPart);
            return jsonPart;
        }

        // 如果找不到完整的JSON，返回清理后的内容
        console.log('🔄 未找到完整JSON，返回清理后内容');
        return cleanedContent;
    }

    // 文本响应解析（降级方案）
    parseTextResponse(aiResponse, userData, bazi, fiveElements) {
        const score = this.calculateBaziScore(bazi, fiveElements);
        const dayGanInfo = this.baziKnowledge.tiangan[bazi.dayGan];

        // 转换为中文天干地支
        const yearGanChinese = this.baziKnowledge.tiangan[bazi.year.gan].name;
        const yearZhiChinese = this.baziKnowledge.dizhi[bazi.year.zhi].name;
        const monthGanChinese = this.baziKnowledge.tiangan[bazi.month.gan].name;
        const monthZhiChinese = this.baziKnowledge.dizhi[bazi.month.zhi].name;
        const dayGanChinese = this.baziKnowledge.tiangan[bazi.day.gan].name;
        const dayZhiChinese = this.baziKnowledge.dizhi[bazi.day.zhi].name;
        const hourGanChinese = this.baziKnowledge.tiangan[bazi.hour.gan].name;
        const hourZhiChinese = this.baziKnowledge.dizhi[bazi.hour.zhi].name;

        // 提取各个分析部分
        const sections = this.extractSections(aiResponse);

        return {
            success: true,
            baziAnalysis: {
                method: 'ai',
                basic: {
                    name: userData.userName,
                    gender: userData.gender,
                    birthDate: userData.birthDate,
                    birthHour: userData.birthHour,
                    bazi: `${yearGanChinese}${yearZhiChinese} ${monthGanChinese}${monthZhiChinese} ${dayGanChinese}${dayZhiChinese} ${hourGanChinese}${hourZhiChinese}`,
                    dayMaster: `${dayGanInfo.name}${dayGanInfo.element}`,
                    score: score
                },
                fiveElements: {
                    distribution: fiveElements.elements,
                    strongest: fiveElements.strongest,
                    weakest: fiveElements.weakest,
                    balance: this.analyzeFiveElementsBalance(fiveElements)
                },
                sections: {
                    pattern: sections.pattern || `八字格局分析：${dayGanInfo.name}${dayGanInfo.element}日主，五行${this.analyzeFiveElementsBalance(fiveElements)}`,
                    character: sections.character || `性格特征：${dayGanInfo.traits.join('、')}`,
                    career: sections.career || `事业运势：适合从事${this.getElementCareers(dayGanInfo.element).join('、')}等行业`,
                    wealth: sections.wealth || `财运分析：稳健发展，把握机遇`,
                    marriage: sections.marriage || `感情婚姻：真诚待人，缘分自来`,
                    health: sections.health || `健康运势：注意${dayGanInfo.element}属性保养，保持规律作息`,
                    timing: sections.timing || `未来运势：2025年乙巳年，木火通明，发展机遇良好`,
                    enhancement: sections.enhancement || `开运建议：使用${this.getLuckyColors(fiveElements.weakest).join('、')}等颜色开运`
                },
                recommendations: this.extractRecommendations(aiResponse, dayGanInfo),
                luckyElements: this.extractLuckyElements(aiResponse, fiveElements),
                timestamp: new Date().toISOString()
            }
        };
    }

    // 生成默认幸运元素
    generateDefaultLuckyElements(fiveElements) {
        const weakestElement = fiveElements.weakest;
        const luckyColors = this.getLuckyColors(weakestElement);
        const luckyDirections = this.getLuckyDirections(weakestElement);

        return {
            colors: luckyColors,
            directions: luckyDirections,
            numbers: this.getLuckyNumbers(weakestElement),
            stones: this.getLuckyStones(weakestElement)
        };
    }

    // 获取幸运数字
    getLuckyNumbers(element) {
        const numberMap = {
            '金': ['4', '9'],
            '木': ['3', '8'],
            '水': ['1', '6'],
            '火': ['2', '7'],
            '土': ['5', '0']
        };
        return numberMap[element] || ['6', '8'];
    }

    // 获取幸运宝石
    getLuckyStones(element) {
        const stoneMap = {
            '金': ['白水晶', '银饰'],
            '木': ['绿松石', '翡翠'],
            '水': ['黑曜石', '蓝宝石'],
            '火': ['红玛瑙', '石榴石'],
            '土': ['黄水晶', '琥珀']
        };
        return stoneMap[element] || ['白水晶', '黄水晶'];
    }

    // 获取幸运方位
    getLuckyDirections(element) {
        const directionMap = {
            '金': ['西方', '西北'],
            '木': ['东方', '东南'],
            '水': ['北方'],
            '火': ['南方'],
            '土': ['中央', '西南', '东北']
        };
        return directionMap[element] || ['东方', '南方'];
    }

    // 获取五行性格特征
    getElementCharacteristics(element) {
        const characteristicsMap = {
            '金': '意志坚定，做事果断，有较强的组织能力和领导才能',
            '木': '性格温和，富有同情心，具有创造力和进取精神',
            '水': '聪明灵活，适应能力强，善于思考和学习',
            '火': '热情开朗，积极主动，具有很强的表达能力',
            '土': '稳重踏实，诚实可靠，具有很强的责任心'
        };
        return characteristicsMap[element] || '性格平和，具有良好的适应能力';
    }

    // 提取摘要
    extractSummary(content, dayGanInfo) {
        // 尝试提取开头段落作为摘要
        const firstParagraph = content.split(/\n\s*\n/)[0];
        if (firstParagraph && firstParagraph.length > 50) {
            return firstParagraph.substring(0, 200) + '...';
        }
        
        // 如果没有合适的段落，生成默认摘要
        return `您的日主为${dayGanInfo.name}${dayGanInfo.element}，${dayGanInfo.traits[0]}。根据八字分析，您具有${dayGanInfo.traits.join('、')}的特质。在2025年乙巳年，建议您发挥自身优势，把握时机发展。`;
    }
    
    // 提取各个分析部分
    extractSections(content) {
        const sections = {};
        
                 // 定义各部分的匹配模式
         const patterns = {
             pattern: /【八字格局分析】[\s\S]*?(?=【性格特征分析】|$)/i,
             character: /【性格特征分析】[\s\S]*?(?=【事业运势分析】|$)/i,
             career: /【事业运势分析】[\s\S]*?(?=【财运分析】|$)/i,
             wealth: /【财运分析】[\s\S]*?(?=【感情婚姻分析】|$)/i,
             marriage: /【感情婚姻分析】[\s\S]*?(?=【健康运势分析】|$)/i,
             health: /【健康运势分析】[\s\S]*?(?=【未来运势分析】|$)/i,
             timing: /【未来运势分析】[\s\S]*?(?=【开运建议】|$)/i,
             enhancement: /【开运建议】[\s\S]*?(?=$)/i
         };
        
        // 提取各部分内容
        Object.keys(patterns).forEach(key => {
            const match = content.match(patterns[key]);
            if (match && match[0]) {
                // 清理内容，移除标题部分
                let text = match[0];
                const titleMatch = text.match(/【[^】]*】/);
                if (titleMatch) {
                    text = text.substring(titleMatch[0].length);
                }
                sections[key] = text.trim();
            }
        });
        
        return sections;
    }
    
    // 提取建议
    extractRecommendations(content, dayGanInfo) {
        const recommendations = [];
        
                 // 尝试从开运建议部分提取
        const enhancementMatch = content.match(/【开运建议】[\s\S]*?(?=$)/i);
        if (enhancementMatch && enhancementMatch[0]) {
            // 移除标题
            const enhancementText = enhancementMatch[0].replace(/【开运建议】/, '').trim();
            
            // 按行或句号分割
            const lines = enhancementText.split(/[。；;]/);
            lines.forEach(line => {
                if (line.length > 10 && line.length < 100 && 
                    (line.includes('建议') || line.includes('可以') || line.includes('应该') || 
                     line.includes('宜') || line.includes('适合') || line.includes('注意'))) {
                    const cleanLine = line.replace(/^\d+[\.、]/, '').trim();
                    if (cleanLine.length > 5) {
                        recommendations.push(cleanLine);
                    }
                }
            });
        }
        
        // 如果没有提取到足够的建议，添加默认建议
        if (recommendations.length < 3) {
            recommendations.push(
                `发挥${dayGanInfo.traits[0]}的优势`,
                '保持五行平衡发展',
                '2025年把握木火通明的机遇',
                '注意身心健康，规律作息'
            );
        }
        
        return recommendations.slice(0, 6); // 最多返回6条建议
    }
    
    // 提取开运要素
    extractLuckyElements(content, fiveElements) {
        const elements = [];
        
        // 尝试从开运建议部分提取颜色、方位等
        const enhancementMatch = content.match(/【开运建议】[\s\S]*?(?=$)/i);
        if (enhancementMatch && enhancementMatch[0]) {
            // 移除标题
            const enhancementText = enhancementMatch[0].replace(/【开运建议】/, '').trim();
            
            // 提取颜色
            const colorMatches = enhancementText.match(/(红色|绿色|蓝色|黄色|黑色|白色|紫色|橙色|金色|银色)/g);
            if (colorMatches) {
                elements.push(...colorMatches.slice(0, 3));
            }
            
            // 提取方位
            const directionMatches = enhancementText.match(/(东方|南方|西方|北方|东南|西南|东北|西北)/g);
            if (directionMatches) {
                elements.push(...directionMatches.slice(0, 2));
            }
            
            // 提取其他开运要素
            const otherMatches = enhancementText.match(/(佩戴|饰品|水晶|玉石|手链|吊坠|摆件|植物|花卉)/g);
            if (otherMatches) {
                elements.push(...otherMatches.slice(0, 2));
            }
        }
        
        // 添加五行相关的开运要素
        elements.push(
            ...this.getLuckyColors(fiveElements.weakest),
            fiveElements.weakest + '系',
            '平衡五行'
        );
        
        // 去重并限制数量
        return [...new Set(elements)].slice(0, 8);
    }

    // 本地分析
    generateLocalAnalysis(userData, bazi, fiveElements) {
        const dayGanInfo = this.baziKnowledge.tiangan[bazi.dayGan];
        const score = this.calculateBaziScore(bazi, fiveElements);

        // 转换为中文天干地支
        const yearGanChinese = this.baziKnowledge.tiangan[bazi.year.gan].name;
        const yearZhiChinese = this.baziKnowledge.dizhi[bazi.year.zhi].name;
        const monthGanChinese = this.baziKnowledge.tiangan[bazi.month.gan].name;
        const monthZhiChinese = this.baziKnowledge.dizhi[bazi.month.zhi].name;
        const dayGanChinese = this.baziKnowledge.tiangan[bazi.day.gan].name;
        const dayZhiChinese = this.baziKnowledge.dizhi[bazi.day.zhi].name;
        const hourGanChinese = this.baziKnowledge.tiangan[bazi.hour.gan].name;
        const hourZhiChinese = this.baziKnowledge.dizhi[bazi.hour.zhi].name;

        return {
            success: true,
            baziAnalysis: {
                method: 'local',
                basic: {
                    name: userData.userName,
                    gender: userData.gender,
                    birthDate: userData.birthDate,
                    birthHour: userData.birthHour,
                    bazi: `${yearGanChinese}${yearZhiChinese} ${monthGanChinese}${monthZhiChinese} ${dayGanChinese}${dayZhiChinese} ${hourGanChinese}${hourZhiChinese}`,
                    dayMaster: `${dayGanInfo.name}${dayGanInfo.element}`,
                    score: score
                },
                fiveElements: {
                    distribution: fiveElements.elements,
                    strongest: fiveElements.strongest,
                    weakest: fiveElements.weakest,
                    balance: this.analyzeFiveElementsBalance(fiveElements)
                },
                sections: {
                    pattern: `八字格局分析：日主${dayGanInfo.name}${dayGanInfo.element}，${dayGanInfo.type === 'yang' ? '阳性' : '阴性'}特质，具有${dayGanInfo.traits.join('、')}的特征。五行分布${this.analyzeFiveElementsBalance(fiveElements)}，整体格局稳定。根据传统命理学理论，此八字格局适合稳健发展，注重内在修养和品德提升。`,
                    character: `性格特征分析：您具有${dayGanInfo.traits.join('、')}的性格特点。${dayGanInfo.element}属性的人通常${this.getElementCharacteristics(dayGanInfo.element)}。建议发挥自身优势，注意平衡发展，培养耐心和毅力。在人际交往中保持真诚，在工作中发挥专业能力。`,
                    career: `事业运势分析：根据您的八字特点，适合从事${this.getElementCareers(dayGanInfo.element).join('、')}等行业。${dayGanInfo.element}属性的人在这些领域容易发挥优势，获得成功。建议选择与自身特质相符的职业方向，稳步发展，不宜急功近利。`,
                    wealth: `财运分析：财运整体稳健，适合稳健型投资和理财。不宜投机取巧，建议通过正当途径积累财富。${dayGanInfo.element}属性的人理财观念较为保守，这是优势也是特点。建议多学习理财知识，合理规划财务。`,
                    marriage: `感情婚姻分析：感情方面注重真诚和稳定，不喜欢浮躁的感情关系。${dayGanInfo.element}属性的人在感情中比较专一，重视家庭和谐。建议在选择伴侣时注重品格和价值观的匹配，用心经营感情，婚姻生活会比较美满。`,
                    health: `健康运势分析：整体健康状况良好，但需要注意${dayGanInfo.element}属性相关的保养。建议保持规律的作息时间，适当运动，注意饮食平衡。根据五行理论，可以通过调节生活习惯来维护身体健康，预防疾病的发生。`,
                    timing: `未来运势分析：2025年乙巳年，木火通明，对您的发展比较有利。这一年适合把握机遇，稳步前进。建议在工作和生活中保持积极态度，抓住有利时机。同时要注意防范小人，保持谨慎的态度。`,
                    enhancement: `开运建议：建议使用${this.getLuckyColors(fiveElements.weakest).join('、')}等颜色来增强运势。可以在服装、家居装饰中多使用这些颜色。同时建议佩戴${this.getLuckyStones(fiveElements.weakest).join('、')}等饰品，有助于平衡五行，提升整体运势。`
                },
                recommendations: [
                    `发挥${dayGanInfo.traits[0]}的优势，在工作中展现专业能力`,
                    `关注${this.getElementCareers(dayGanInfo.element)[0]}等行业发展机会`,
                    `使用${this.getLuckyColors(fiveElements.weakest)[0]}等颜色开运`,
                    '保持五行平衡，注重身心健康发展',
                    '培养耐心和毅力，稳步实现人生目标'
                ],
                luckyElements: this.generateDefaultLuckyElements(fiveElements),
                timestamp: new Date().toISOString()
            }
        };
    }

    // 计算八字评分
    calculateBaziScore(bazi, fiveElements) {
        const baseScore = 75;
        const balance = this.analyzeFiveElementsBalance(fiveElements);
        let adjustment = 0;
        
        if (balance.includes('平衡')) adjustment += 15;
        else if (balance.includes('较好')) adjustment += 10;
        else adjustment += 5;
        
        return Math.min(Math.max(baseScore + adjustment, 60), 95);
    }

    // 分析五行平衡
    analyzeFiveElementsBalance(fiveElements) {
        const counts = Object.values(fiveElements.elements);
        const max = Math.max(...counts);
        const min = Math.min(...counts);
        
        if (max - min <= 1) return '平衡';
        if (max - min <= 2) return '较好平衡';
        return '需要调理';
    }

    // 获取五行对应职业
    getElementCareers(element) {
        const careerMap = {
            木: ['教育', '文化', '医疗'],
            火: ['娱乐', '广告', '电子'],
            土: ['房地产', '农业', '建筑'],
            金: ['金融', '机械', '汽车'],
            水: ['物流', '旅游', '通讯']
        };
        return careerMap[element] || ['服务业'];
    }

    // 获取幸运颜色
    getLuckyColors(element) {
        const colorMap = {
            木: ['绿色', '青色'],
            火: ['红色', '紫色'],
            土: ['黄色', '土色'],
            金: ['白色', '金色'],
            水: ['黑色', '蓝色']
        };
        return colorMap[element] || ['白色'];
    }

    // 获取开运要素
    getLuckyElements(fiveElements) {
        return [
            ...this.getLuckyColors(fiveElements.weakest),
            fiveElements.weakest + '系',
            '平衡五行',
            '规律作息'
        ];
    }
}

// 导出
window.BaziAnalysisAI = BaziAnalysisAI; 