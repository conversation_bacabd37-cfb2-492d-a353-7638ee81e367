/**
 * 姓名详批AI分析模块
 * 集成DeepSeek API进行深度姓名学分析
 */

class NameAnalysisAI {
    constructor() {
        this.isAnalyzing = false;
        this.lastAnalysis = null;
        this.initializeKnowledgeBase();
        // 初始化认证服务
        if (window.initializeAuthServices) {
            window.initializeAuthServices();
        }
    }

    // 初始化姓名学知识库
    initializeKnowledgeBase() {
        this.knowledgeBase = {
            // 姓名学五行对应表
            charWuxing: {
                '木': ['林', '森', '杨', '柳', '桃', '梅', '兰', '竹', '菊', '松', '柏', '枫', '桂', '荷', '莲', '芸', '芳', '茗', '茜', '蓉', '薇', '菲', '芝', '芬'],
                '火': ['炎', '焱', '阳', '明', '亮', '辉', '灿', '烁', '煜', '晖', '旭', '昊', '晨', '曦', '暖', '炽', '焕', '烨', '熠', '炫', '烽', '焰', '炀', '照'],
                '土': ['地', '山', '岩', '石', '田', '圭', '坤', '培', '城', '垒', '埔', '堂', '墨', '壁', '坡', '峰', '岳', '崇', '岭', '峻', '岗', '坪', '垦', '境'],
                '金': ['金', '银', '铜', '铁', '钢', '锋', '锐', '钧', '铭', '鑫', '钊', '钰', '铭', '锦', '镇', '钟', '铃', '锡', '钦', '针', '钻', '铨', '锌', '银'],
                '水': ['水', '江', '河', '海', '洋', '湖', '波', '涛', '泽', '润', '浩', '淼', '沁', '沂', '汐', '汉', '汪', '沈', '沉', '沐', '沙', '沛', '沫', '河']
            },

            // 字义内涵库
            charMeanings: {
                '伟': '伟大、宏伟，寓意志向远大，成就非凡',
                '丽': '美丽、秀丽，寓意容貌姣好，品格优雅',
                '强': '强壮、坚强，寓意意志坚定，体魄健壮',
                '敏': '敏捷、聪敏，寓意才思敏捷，反应灵敏',
                '静': '安静、文静，寓意性格温和，内心平和',
                '杰': '杰出、英杰，寓意才华出众，卓越超群',
                '涛': '波涛、浪涛，寓意气势磅礴，胸怀宽广',
                '明': '光明、聪明，寓意智慧明达，前程光明',
                '超': '超越、超凡，寓意超越常人，出类拔萃',
                '秀': '秀美、优秀，寓意品德高尚，才华横溢',
                '霞': '彩霞、朝霞，寓意绚烂多彩，美好如画',
                '平': '平和、公平，寓意性格平和，处事公正',
                '刚': '刚强、刚毅，寓意性格坚强，意志坚定',
                '桂': '桂花、月桂，寓意高雅脱俗，品格高洁',
                '娟': '娟秀、婵娟，寓意姿态优美，温柔可人',
                '兰': '兰花、幽兰，寓意品格高雅，气质脱俗',
                '凤': '凤凰、神凤，寓意高贵吉祥，气度非凡',
                '洁': '洁净、纯洁，寓意品格纯净，心地善良',
                '梅': '梅花、寒梅，寓意坚韧不拔，高洁傲骨',
                '琳': '美玉、琳琅，寓意珍贵美好，才华出众',
                '素': '朴素、素雅，寓意品格纯朴，气质清雅',
                '云': '白云、彩云，寓意飘逸自然，心胸开阔',
                '莲': '莲花、荷莲，寓意清净高洁，出淤泥而不染',
                '真': '真实、纯真，寓意性格真诚，品格纯真',
                '环': '环形、循环，寓意完美圆满，和谐统一',
                '雪': '雪花、白雪，寓意纯洁无瑕，品格高尚',
                '荣': '荣誉、繁荣，寓意荣华富贵，成就辉煌',
                '爱': '爱心、仁爱，寓意慈爱善良，富有爱心',
                '妹': '妹妹、少女，寓意青春可爱，活泼天真',
                '香': '芳香、香气，寓意气质芳香，令人喜爱',
                '月': '月亮、明月，寓意清雅脱俗，如月般美好',
                '莺': '黄莺、歌莺，寓意声音悦耳，才华横溢',
                '媛': '美女、淑媛，寓意美丽端庄，气质优雅',
                '瑞': '祥瑞、吉瑞，寓意吉祥如意，福气满满',
                '凡': '平凡、非凡，寓意朴实无华，不凡品格',
                '佳': '佳美、最佳，寓意品德优良，才华出众',
                '嘉': '嘉奖、嘉许，寓意德才兼备，受人称赞',
                '琼': '美玉、琼瑶，寓意珍贵美好，如玉品格',
                '勤': '勤奋、勤劳，寓意努力上进，勤劳朴实',
                '珍': '珍贵、珍宝，寓意珍贵难得，值得珍惜',
                '贞': '贞洁、坚贞，寓意品格坚贞，意志坚定',
                '毅': '毅力、坚毅，寓意意志坚强，持之以恒',
                '智': '智慧、聪智，寓意聪明智慧，才思敏捷',
                '淑': '淑女、温淑，寓意温和善良，品德高尚',
                '惠': '恩惠、智惠，寓意聪明贤惠，心地善良',
                '晶': '水晶、晶莹，寓意纯洁透明，品格高洁',
                '妍': '美妍、妍丽，寓意美丽动人，容貌姣好',
                '茜': '茜草、鲜艳，寓意美丽鲜艳，充满活力',
                '秋': '秋天、金秋，寓意成熟稳重，收获满满',
                '珊': '珊瑚、美珊，寓意珍贵美丽，如珊瑚般艳丽',
                '莎': '莎草、绿莎，寓意生机勃勃，富有活力',
                '锦': '锦绣、锦缎，寓意前程似锦，生活美好',
                '黛': '黛色、眉黛，寓意美丽动人，如画眉般秀美',
                '青': '青春、青翠，寓意青春活力，朝气蓬勃',
                '倩': '倩影、美倩，寓意美丽动人，姿态优美',
                '婷': '婷婷、娉婷，寓意姿态优美，袅娜多姿',
                '姣': '姣好、娇美，寓意容貌姣好，美丽动人',
                '婉': '婉约、温婉，寓意温和柔美，举止优雅',
                '娴': '娴静、娴雅，寓意文静优雅，技艺精湛',
                '瑾': '美玉、瑾瑜，寓意品德高尚，如美玉无瑕',
                '颖': '聪颖、机颖，寓意聪明机智，才华出众'
            },

            // 三才配置吉凶
            sancaiConfig: {
                '111': { name: '木木木', luck: '大吉', desc: '性格温和，上进心强，意志坚定，成功运佳' },
                '112': { name: '木木火', luck: '大吉', desc: '性格活泼，有进取心，易获成功，名利双收' },
                '113': { name: '木木土', luck: '凶', desc: '虽有成功运，但基础不稳，容易变动' },
                '121': { name: '木火木', luck: '大吉', desc: '性格积极，有领导能力，事业发展顺利' },
                '122': { name: '木火火', luck: '大吉', desc: '热情奔放，富有创造力，容易获得成功' },
                '131': { name: '木土木', luck: '凶', desc: '性格固执，发展受限，需要调整心态' },
                '211': { name: '火木木', luck: '吉', desc: '有才华和进取心，但需要稳定基础' },
                '212': { name: '火木火', luck: '大吉', desc: '才华横溢，领导能力强，成功运极佳' },
                '221': { name: '火火木', luck: '吉', desc: '性格热情，有冲劲，但需要耐心' },
                '222': { name: '火火火', luck: '凶', desc: '性格急躁，容易冲动，需要控制情绪' },
                '311': { name: '土木木', luck: '凶', desc: '虽有才华，但发展受阻，需要坚持' },
                '312': { name: '土木火', luck: '半吉', desc: '有一定才能，但需要努力才能成功' }
            },

            // 姓名笔画吉凶数理
            strokeLuck: {
                1: { luck: '大吉', meaning: '万物开泰，最大吉数' },
                3: { luck: '大吉', meaning: '立身兴家，德智体全' },
                5: { luck: '大吉', meaning: '福禄寿全，和合完壁' },
                6: { luck: '大吉', meaning: '安稳吉庆，天德地祥' },
                7: { luck: '吉', meaning: '刚毅果断，勇往直前' },
                8: { luck: '大吉', meaning: '意志坚强，勤勉发展' },
                11: { luck: '大吉', meaning: '草木逢春，枝叶沾露' },
                13: { luck: '大吉', meaning: '天才卓越，智勇双全' },
                15: { luck: '大吉', meaning: '福寿双全，立身兴家' },
                16: { luck: '大吉', meaning: '贵人相助，能成大事' },
                17: { luck: '半吉', meaning: '权威刚强，突破万难' },
                18: { luck: '大吉', meaning: '有志竟成，内外和合' },
                21: { luck: '大吉', meaning: '光风霁月，万物确立' },
                23: { luck: '大吉', meaning: '旭日东升，壮丽壮观' },
                24: { luck: '大吉', meaning: '家门余庆，金钱丰盈' },
                25: { luck: '半吉', meaning: '英俊敏捷，刚毅果断' },
                29: { luck: '半吉', meaning: '智谋优秀，财力归集' },
                31: { luck: '大吉', meaning: '智勇得志，心想事成' },
                32: { luck: '大吉', meaning: '龙在池中，风云际会' },
                33: { luck: '大吉', meaning: '家门隆昌，才德开展' },
                35: { luck: '大吉', meaning: '温和平静，智达通畅' },
                37: { luck: '大吉', meaning: '权威显达，猛虎添翼' },
                39: { luck: '半吉', meaning: '富贵荣华，财帛丰盈' },
                41: { luck: '大吉', meaning: '天赋吉运，德望兼备' },
                45: { luck: '大吉', meaning: '杨柳遇春，绿叶发枝' },
                47: { luck: '大吉', meaning: '花开之象，万事如意' },
                48: { luck: '大吉', meaning: '美花丰实，德智兼备' },
                52: { luck: '半吉', meaning: '草木逢春，雨过天晴' },
                57: { luck: '半吉', meaning: '寒雪青松，夜莺吟春' },
                61: { luck: '大吉', meaning: '名利双收，繁荣富贵' },
                63: { luck: '大吉', meaning: '身心平安，雨露惠泽' },
                65: { luck: '大吉', meaning: '富贵长寿，家运隆昌' },
                67: { luck: '大吉', meaning: '时来运转，事事如意' },
                68: { luck: '大吉', meaning: '思虑周详，计划力行' },
                81: { luck: '大吉', meaning: '万物回春，还元复始' }
            }
        };
    }

    // 生成AI姓名分析提示词
    generateNameAnalysisPrompt(userData, basicAnalysis) {
        const { name, gender, birthDate, birthHour, focus } = userData;
        const { nameScore, strokes, wuxing, analysis } = basicAnalysis;

        const genderText = gender === 'male' ? '男性' : '女性';

        const prompt = `请对以下姓名进行深度详批分析：

姓名信息：
- 姓名：${name}
- 性别：${genderText}
- 出生日期：${birthDate}
- 出生时辰：${this.getTimeDescription(birthHour)}
- 基础评分：${nameScore}分
- 天格：${strokes.tianGe}画
- 人格：${strokes.renGe}画
- 地格：${strokes.diGe}画
- 外格：${strokes.waiGe}画
- 总格：${strokes.zongGe}画
- 姓名五行：${wuxing.name.join('、')}

请严格按照以下JSON格式返回分析结果，不要包含任何JSON之外的文本,JSON的Value中不要包含双引号：

{
  "basic": {
    "name": "${name}",
    "gender": "${gender}",
    "birthDate": "${birthDate}",
    "birthHour": "${birthHour}",
    "score": ${nameScore}
  },
  "strokes": {
    "tianGe": ${strokes.tianGe},
    "renGe": ${strokes.renGe},
    "diGe": ${strokes.diGe},
    "waiGe": ${strokes.waiGe},
    "zongGe": ${strokes.zongGe}
  },
  "wuxing": {
    "name": ${JSON.stringify(wuxing.name)},
    "main": "${wuxing.main}",
    "balance": "五行平衡状况分析"
  },
  "sections": {
    "overall": "姓名综合评价：整体评价姓名的吉凶寓意、文化内涵、音律美感，要求200字以上",
    "meaning": "字义深度解析：分析姓名中每个字的深层含义、文化背景、象征意义，要求200字以上",
    "character": "性格命运预测：根据姓名学分析性格特点、天赋才能、人生格局，要求200字以上",
    "career": "事业财运指导：预测适合的职业方向、财运走势、成功概率，要求200字以上",
    "marriage": "感情婚姻运势：分析恋爱运、婚姻运、家庭关系、子女运，要求200字以上",
    "health": "健康运势评估：从姓名学角度分析健康状况、易患疾病、养生建议，要求200字以上",
    "enhancement": "改名优化建议：如果需要改善，提供具体的改名方向和字选建议，要求200字以上",
    "guidance": "人生发展指导：提供人生各阶段的发展建议、注意事项、开运方法，要求200字以上"
  },
  "recommendations": [
    "具体建议1",
    "具体建议2",
    "具体建议3"
  ],
  "luckyElements": {
    "colors": ["幸运颜色1", "幸运颜色2"],
    "numbers": ["幸运数字1", "幸运数字2"],
    "directions": ["有利方位1", "有利方位2"],
    "stones": ["幸运宝石1", "幸运宝石2"]
  },
  "timestamp": "${new Date().toISOString()}"
}

请用专业的姓名学术语，每个分析部分至少200字，语言要生动有趣，富有感染力。`;

        return prompt;
    }

    // 获取时辰描述
    getTimeDescription(birthHour) {
        const timeMap = {
            'zi': '子时 (23:00-01:00)',
            'chou': '丑时 (01:00-03:00)',
            'yin': '寅时 (03:00-05:00)',
            'mao': '卯时 (05:00-07:00)',
            'chen': '辰时 (07:00-09:00)',
            'si': '巳时 (09:00-11:00)',
            'wu': '午时 (11:00-13:00)',
            'wei': '未时 (13:00-15:00)',
            'shen': '申时 (15:00-17:00)',
            'you': '酉时 (17:00-19:00)',
            'xu': '戌时 (19:00-21:00)',
            'hai': '亥时 (21:00-23:00)'
        };
        return timeMap[birthHour] || birthHour;
    }

    // 调用AI接口进行深度分析
    async generateAIAnalysis(userData, basicAnalysis) {
        if (!window.aIService) {
            throw new Error('AI服务未初始化，请检查配置');
        }

        console.log('开始AI姓名详批分析:', userData.name);

        try {
            const prompt = this.generateNameAnalysisPrompt(userData, basicAnalysis);
            console.log('AI分析提示词:', prompt);

            // 使用统一AI调用接口
            const systemPrompt = "你是一位精通中华传统姓名学的专业大师，能够进行准确的姓名分析和运势预测。请严格按照用户要求的JSON格式返回分析结果，不要包含任何JSON之外的文本。确保返回的是有效的JSON格式，每个分析部分都要详细且不少于200字。";
            const aiResponse = await window.aIService.callAI(prompt, systemPrompt, {
                enableFallback: true  // 启用服务降级
            });

            console.log('AI分析响应:', aiResponse);
            return this.parseAINameResponse(aiResponse, userData, basicAnalysis);

        } catch (error) {
            console.error('AI姓名分析失败:', error);
            throw error;
        }
    }

    // 清理AI响应，提取JSON部分
    cleanAIResponse(response) {
        console.log('🧹 开始清理AI响应...');

        let cleanedContent = response;

        // 步骤1: 如果响应包含 "content": 开头，说明是包装格式
        if (response.includes('"content":')) {
            console.log('📦 检测到包装格式响应');

            // 尝试提取content字段的值
            try {
                const contentMatch = response.match(/"content":\s*"([^"]*(?:\\.[^"]*)*)"/);
                if (contentMatch && contentMatch[1]) {
                    // 解码转义字符
                    cleanedContent = contentMatch[1]
                        .replace(/\\n/g, '\n')
                        .replace(/\\"/g, '"')
                        .replace(/\\\\/g, '\\');
                    console.log('✅ 提取到content内容:', cleanedContent);
                }
            } catch (error) {
                console.warn('⚠️ 提取content失败:', error);
            }
        }

        // 步骤2: 移除markdown代码块标记
        if (cleanedContent.includes('```json')) {
            console.log('📝 检测到markdown代码块');
            const jsonMatch = cleanedContent.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch && jsonMatch[1]) {
                cleanedContent = jsonMatch[1].trim();
                console.log('✂️ 提取markdown中的JSON:', cleanedContent);
            }
        }

        // 步骤3: 查找JSON对象的开始和结束
        let jsonStart = cleanedContent.indexOf('{');
        let jsonEnd = cleanedContent.lastIndexOf('}');

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
            const jsonPart = cleanedContent.substring(jsonStart, jsonEnd + 1);
            console.log('🎯 最终提取的JSON:', jsonPart);
            return jsonPart;
        }

        // 如果找不到完整的JSON，返回清理后的内容
        console.log('🔄 未找到完整JSON，返回清理后内容');
        return cleanedContent;
    }

    // 解析AI姓名分析响应
    parseAINameResponse(response, userData, basicAnalysis) {
        try {
            
            // 清理响应内容，提取JSON部分
            let cleanedResponse = this.cleanAIResponse(response);
            
            const parsedResponse = JSON.parse(cleanedResponse);

            // 验证JSON结构是否完整
            if (parsedResponse.basic && parsedResponse.sections) {
                return {
                    success: true,
                    nameAnalysis: {
                        method: 'ai',
                        basic: parsedResponse.basic,
                        strokes: parsedResponse.strokes,
                        wuxing: parsedResponse.wuxing,
                        sections: parsedResponse.sections,
                        recommendations: parsedResponse.recommendations || [],
                        luckyElements: parsedResponse.luckyElements || {},
                        timestamp: parsedResponse.timestamp || new Date().toISOString()
                    }
                };
            }
            
        } catch (error) {
            console.warn('JSON解析失败，尝试文本解析:', error);
        }

        // 如果JSON解析失败，降级到文本解析
        return this.parseTextNameResponse(response, userData, basicAnalysis);
    }

    // 文本响应解析（降级方案）
    parseTextNameResponse(response, userData, basicAnalysis) {
        try {
            // 尝试从响应中提取结构化信息
            const sections = {
                overall: this.extractSection(response, ['姓名综合评价', '整体评价', '综合分析']),
                meaning: this.extractSection(response, ['字义深度解析', '字义分析', '寓意解析']),
                character: this.extractSection(response, ['性格命运预测', '性格分析', '命运预测']),
                career: this.extractSection(response, ['事业财运指导', '事业运势', '财运分析']),
                marriage: this.extractSection(response, ['感情婚姻运势', '婚姻运势', '感情分析']),
                health: this.extractSection(response, ['健康运势评估', '健康分析', '健康运势']),
                enhancement: this.extractSection(response, ['改名优化建议', '改名建议', '优化建议']),
                guidance: this.extractSection(response, ['人生发展指导', '发展建议', '人生指导'])
            };

            return {
                success: true,
                nameAnalysis: {
                    method: 'ai',
                    basic: {
                        name: userData.name,
                        gender: userData.gender,
                        birthDate: userData.birthDate,
                        birthHour: userData.birthHour,
                        score: this.extractAIScore(response) || this.calculateAIScore(sections)
                    },
                    strokes: basicAnalysis.strokes,
                    wuxing: basicAnalysis.wuxing,
                    sections: sections,
                    recommendations: this.extractRecommendations(response),
                    luckyElements: this.extractLuckyElements(response),
                    timestamp: new Date().toISOString()
                }
            };

        } catch (error) {
            console.warn('AI响应解析失败，使用原始文本:', error);
            return {
                success: true,
                nameAnalysis: {
                    method: 'ai',
                    basic: {
                        name: userData.name,
                        gender: userData.gender,
                        birthDate: userData.birthDate,
                        birthHour: userData.birthHour,
                        score: 75
                    },
                    strokes: basicAnalysis.strokes,
                    wuxing: basicAnalysis.wuxing,
                    sections: { overall: response.substring(0, 500) + '...' },
                    recommendations: ['建议详细咨询专业姓名学大师'],
                    luckyElements: {
                        colors: ['红色', '黄色'],
                        numbers: ['6', '8'],
                        directions: ['东方', '南方'],
                        stones: ['玉石', '水晶']
                    },
                    timestamp: new Date().toISOString()
                }
            };
        }
    }

    // 提取特定章节内容
    extractSection(text, keywords) {
        for (const keyword of keywords) {
            const regex = new RegExp(`[*]*\\s*${keyword}[*]*[：:]?([\\s\\S]*?)(?=\\n\\n|\\n[*]*\\s*[一-龥]+[*]*[：:]|$)`, 'i');
            const match = text.match(regex);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return '';
    }

    // 生成AI摘要
    generateAISummary(text, userData) {
        const sentences = text.split(/[。！？]/);
        let summary = `${userData.name}的姓名详批：`;
        
        // 提取关键评价句子
        const keyPhrases = ['总体', '整体', '综合', '寓意', '运势', '五行', '性格'];
        const relevantSentences = sentences.filter(sentence => 
            keyPhrases.some(phrase => sentence.includes(phrase)) && sentence.length > 10
        );
        
        if (relevantSentences.length > 0) {
            summary += relevantSentences.slice(0, 2).join('。') + '。';
        } else {
            summary += sentences.slice(1, 3).join('。') + '。';
        }
        
        return summary.length > 150 ? summary.substring(0, 150) + '...' : summary;
    }

    // 提取建议
    extractRecommendations(text) {
        const recommendations = [];
        const lines = text.split('\n');
        
        for (const line of lines) {
            if (line.includes('建议') || line.includes('推荐') || line.includes('应该') || line.includes('宜') || line.includes('适合')) {
                const cleanLine = line.trim().replace(/^[•\-\*\d\.]+/, '').trim();
                if (cleanLine.length > 5) {
                    recommendations.push(cleanLine);
                }
            }
        }
        
        return recommendations.length > 0 ? recommendations.slice(0, 6) : 
               ['定期关注姓名对运势的影响', '保持积极乐观的心态', '结合个人努力改善运势'];
    }

    // 提取AI评分
    extractAIScore(text) {
        const scoreMatch = text.match(/(\d{1,2})[分点]/);
        if (scoreMatch) {
            const score = parseInt(scoreMatch[1]);
            return score >= 30 && score <= 100 ? score : null;
        }
        return null;
    }

    // 计算AI评分
    calculateAIScore(sections) {
        let score = 70;
        
        // 根据分析内容的详细程度和积极性调整分数
        const positiveWords = ['吉', '好', '佳', '优', '利', '旺', '顺', '成功', '发达', '兴旺'];
        const negativeWords = ['凶', '差', '不利', '阻碍', '困难', '破财', '病灾', '不顺'];
        
        const allContent = Object.values(sections).join(' ');
        
        const positiveCount = positiveWords.reduce((count, word) => 
            count + (allContent.match(new RegExp(word, 'g')) || []).length, 0);
        const negativeCount = negativeWords.reduce((count, word) => 
            count + (allContent.match(new RegExp(word, 'g')) || []).length, 0);
        
        score += positiveCount * 2 - negativeCount * 3;
        
        return Math.min(95, Math.max(40, score));
    }

    // 提取开运要素
    extractLuckyElements(text) {
        const elements = [];
        const elementKeywords = {
            '木': ['绿色', '东方', '春季', '植物', '森林'],
            '火': ['红色', '南方', '夏季', '阳光', '火焰'],
            '土': ['黄色', '中央', '长夏', '大地', '山石'],
            '金': ['白色', '西方', '秋季', '金属', '宝石'],
            '水': ['黑色', '北方', '冬季', '海洋', '流水']
        };
        
        for (const [element, keywords] of Object.entries(elementKeywords)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                elements.push(element);
            }
        }
        
        return elements.length > 0 ? elements : ['根据具体情况确定'];
    }

    // 提取改善建议
    extractImproveAdvice(text) {
        const advice = [];
        const lines = text.split('\n');
        
        for (const line of lines) {
            if (line.includes('改善') || line.includes('改名') || line.includes('调整') || line.includes('优化')) {
                const cleanLine = line.trim().replace(/^[•\-\*\d\.]+/, '').trim();
                if (cleanLine.length > 5) {
                    advice.push(cleanLine);
                }
            }
        }
        
        return advice.length > 0 ? advice.slice(0, 4) : 
               ['保持姓名的正面能量', '结合个人努力提升运势', '关注五行平衡'];
    }

    // 生成本地姓名分析（AI失败时的降级方案）
    generateLocalAnalysis(userData, basicAnalysis) {
        const { name, gender } = userData;
        const { nameScore, strokes, wuxing } = basicAnalysis;

        // 生成各个分析章节
        const sections = {
            overall: this.generateLocalOverall(name, nameScore),
            meaning: this.generateLocalMeaning(name),
            character: this.generateLocalPersonality(name, gender, wuxing),
            career: this.generateLocalCareer(wuxing),
            marriage: this.generateLocalMarriage(gender, wuxing),
            health: this.generateLocalHealth(wuxing),
            enhancement: this.generateLocalEnhancement(name, wuxing),
            guidance: this.generateLocalGuidance(name, gender)
        };

        return {
            success: true,
            nameAnalysis: {
                method: 'local',
                basic: {
                    name: userData.name,
                    gender: userData.gender,
                    birthDate: userData.birthDate,
                    birthHour: userData.birthHour,
                    score: nameScore
                },
                strokes: strokes,
                wuxing: wuxing,
                sections: sections,
                recommendations: this.generateDefaultRecommendations(wuxing),
                luckyElements: this.generateDefaultLuckyElements(wuxing),
                timestamp: new Date().toISOString()
            }
        };

    }

    // 生成本地事业分析
    generateLocalCareer(wuxing) {
        const element = wuxing.main;
        const careers = {
            '木': '适合从事教育、文化、医疗、环保等行业，具有良好的创造力和沟通能力，事业发展稳步上升。',
            '火': '适合从事娱乐、广告、电子、能源等行业，具有强烈的进取心和领导能力，事业发展迅速。',
            '土': '适合从事房地产、农业、建筑、金融等行业，具有踏实稳重的工作态度，事业发展稳健。',
            '金': '适合从事金融、机械、汽车、珠宝等行业，具有敏锐的商业嗅觉，事业发展有潜力。',
            '水': '适合从事物流、旅游、通讯、贸易等行业，具有灵活的思维和适应能力，事业发展多变。'
        };
        return careers[element] || '根据个人兴趣和能力选择合适的职业方向，发挥自身优势。';
    }

    // 生成本地婚姻分析
    generateLocalMarriage(gender, wuxing) {
        const element = wuxing.main;
        const genderText = gender === 'male' ? '男性' : '女性';
        const marriages = {
            '木': `${genderText}在感情方面温和体贴，重视家庭和谐，婚姻生活美满，配偶多为善良温柔之人。`,
            '火': `${genderText}在感情方面热情主动，追求浪漫，婚姻生活充满激情，配偶多为开朗活泼之人。`,
            '土': `${genderText}在感情方面稳重踏实，重视责任，婚姻生活稳定，配偶多为可靠诚实之人。`,
            '金': `${genderText}在感情方面理性务实，追求品质，婚姻生活有条理，配偶多为能干优秀之人。`,
            '水': `${genderText}在感情方面灵活变通，善于沟通，婚姻生活和谐，配偶多为聪明机智之人。`
        };
        return marriages[element] || '感情运势良好，婚姻生活和谐美满。';
    }

    // 生成本地健康分析
    generateLocalHealth(wuxing) {
        const element = wuxing.main;
        const healths = {
            '木': '身体健康状况良好，注意肝胆和眼部保养，多接触大自然，保持心情愉悦。',
            '火': '精力充沛，注意心脏和血液循环，避免过度劳累，保持规律作息。',
            '土': '体质较好，注意脾胃消化系统，饮食要规律，适量运动增强体质。',
            '金': '抵抗力强，注意肺部和呼吸系统，保持空气清新，避免吸烟。',
            '水': '新陈代谢良好，注意肾脏和泌尿系统，多喝水，避免过度疲劳。'
        };
        return healths[element] || '注意身体健康，保持良好的生活习惯。';
    }

    // 生成本地改名建议
    generateLocalEnhancement(name, wuxing) {
        const element = wuxing.main;
        const suggestions = {
            '木': '如需改名，建议选择带有木字旁或草字头的字，如林、森、芳、茗等，有助于增强运势。',
            '火': '如需改名，建议选择带有火字旁或日字旁的字，如炎、明、晖、煜等，有助于增强运势。',
            '土': '如需改名，建议选择带有土字旁或山字旁的字，如培、城、峰、岳等，有助于增强运势。',
            '金': '如需改名，建议选择带有金字旁或玉字旁的字，如钰、铭、琳、瑞等，有助于增强运势。',
            '水': '如需改名，建议选择带有水字旁或雨字头的字，如涛、润、霖、泽等，有助于增强运势。'
        };
        return suggestions[element] || '现有姓名配置良好，如需改名请咨询专业姓名学大师。';
    }

    // 生成本地人生指导
    generateLocalGuidance(name, gender) {
        const genderText = gender === 'male' ? '男性' : '女性';
        return `作为${genderText}，建议在人生发展中保持积极向上的心态，发挥自身优势，抓住机遇，克服困难。在事业上要有明确目标，在感情上要真诚待人，在健康上要注重保养。通过不断学习和努力，必能实现人生价值，获得幸福美满的生活。`;
    }

    // 生成默认建议
    generateDefaultRecommendations(wuxing) {
        const element = wuxing.main;
        const recommendations = {
            '木': ['多接触绿色植物，有助于增强运势', '选择东方或东南方向发展', '春季是最佳发展时机'],
            '火': ['多穿红色或紫色衣服，增强个人气场', '选择南方发展事业', '夏季运势最佳'],
            '土': ['多穿黄色或土色衣服，稳定运势', '选择中部地区发展', '长夏季节运势良好'],
            '金': ['多佩戴金属饰品，增强财运', '选择西方发展事业', '秋季是发展良机'],
            '水': ['多穿黑色或蓝色衣服，增强智慧', '选择北方发展事业', '冬季运势最佳']
        };
        return recommendations[element] || ['保持积极心态', '发挥个人优势', '抓住发展机遇'];
    }

    // 生成默认开运要素
    generateDefaultLuckyElements(wuxing) {
        const element = wuxing.main;
        const elements = {
            '木': {
                colors: ['绿色', '青色'],
                numbers: ['3', '8'],
                directions: ['东方', '东南'],
                stones: ['绿松石', '翡翠']
            },
            '火': {
                colors: ['红色', '紫色'],
                numbers: ['2', '7'],
                directions: ['南方'],
                stones: ['红玛瑙', '石榴石']
            },
            '土': {
                colors: ['黄色', '土色'],
                numbers: ['5', '0'],
                directions: ['中央', '西南', '东北'],
                stones: ['黄水晶', '琥珀']
            },
            '金': {
                colors: ['白色', '金色'],
                numbers: ['4', '9'],
                directions: ['西方', '西北'],
                stones: ['白水晶', '银饰']
            },
            '水': {
                colors: ['黑色', '蓝色'],
                numbers: ['1', '6'],
                directions: ['北方'],
                stones: ['黑曜石', '蓝宝石']
            }
        };
        return elements[element] || {
            colors: ['根据个人喜好'],
            numbers: ['6', '8'],
            directions: ['东方', '南方'],
            stones: ['水晶', '玉石']
        };
    }

    // 生成本地综合评价
    generateLocalOverall(name, score) {
        if (score >= 85) {
            return `${name}这个名字寓意深远，五行配置协调，是一个非常吉祥的好名字。名字既有文化内涵，又符合现代审美，能够为人生带来积极的能量。`;
        } else if (score >= 70) {
            return `${name}这个名字整体运势良好，基本符合姓名学的吉祥要求。名字有一定的文化底蕴，对人生发展有一定的积极作用。`;
        } else if (score >= 55) {
            return `${name}这个名字基本合格，但在某些方面还有提升空间。建议在使用中注意发挥名字的正面能量。`;
        } else {
            return `${name}这个名字在姓名学配置上存在一些不足，建议考虑适当的调整或优化，以更好地发挥名字的积极作用。`;
        }
    }

    // 生成本地字义分析
    generateLocalMeaning(name) {
        const meanings = [];
        for (let i = 0; i < name.length; i++) {
            const char = name[i];
            const meaning = this.knowledgeBase.charMeanings[char];
            if (meaning) {
                meanings.push(`"${char}"字${meaning}`);
            } else {
                meanings.push(`"${char}"字寓意美好，体现了深厚的文化内涵`);
            }
        }
        return meanings.join('；') + '。整体名字搭配和谐，寓意深远。';
    }

    // 生成本地五行分析
    generateLocalWuxing(wuxing) {
        const nameElements = wuxing.name.join('、');
        const baziElements = `年${wuxing.bazi.year}、月${wuxing.bazi.month}、日${wuxing.bazi.day}、时${wuxing.bazi.hour}`;
        
        return `姓名五行为${nameElements}，八字五行为${baziElements}。主要五行${wuxing.main}，${this.getWuxingDescription(wuxing.main)}姓名五行与八字五行配合${this.checkWuxingMatch(wuxing)}。`;
    }

    // 获取五行描述
    getWuxingDescription(element) {
        const descriptions = {
            '木': '代表生长、发展、仁慈，主创造力和进取精神。',
            '火': '代表热情、光明、礼仪，主积极向上和领导能力。',
            '土': '代表稳重、诚信、包容，主踏实可靠和组织能力。',
            '金': '代表坚毅、正义、决断，主执行力和判断能力。',
            '水': '代表智慧、灵活、流动，主适应力和学习能力。'
        };
        return descriptions[element] || '';
    }

    // 检查五行匹配度
    checkWuxingMatch(wuxing) {
        const nameSet = new Set(wuxing.name);
        const baziSet = new Set(Object.values(wuxing.bazi));
        const intersection = [...nameSet].filter(x => baziSet.has(x));
        
        if (intersection.length >= 2) {
            return '较好，有助于增强个人运势';
        } else if (intersection.length === 1) {
            return '尚可，基本能够平衡发展';
        } else {
            return '一般，建议关注五行平衡';
        }
    }

    // 生成本地三才分析
    generateLocalSancai(strokes) {
        const tianGe = strokes.tianGe % 10;
        const renGe = strokes.renGe % 10;
        const diGe = strokes.diGe % 10;
        
        const sancaiKey = tianGe.toString() + renGe.toString() + diGe.toString();
        const sancaiInfo = this.knowledgeBase.sancaiConfig[sancaiKey.substring(0, 3)];
        
        if (sancaiInfo) {
            return `三才配置为${sancaiInfo.name}，${sancaiInfo.luck}，${sancaiInfo.desc}`;
        } else {
            return `天格${tianGe}、人格${renGe}、地格${diGe}，三才配置基本合理，有助于人生发展。`;
        }
    }

    // 生成本地性格分析
    generateLocalPersonality(name, gender, wuxing) {
        const element = wuxing.main;
        const personalities = {
            '木': '性格温和善良，富有同情心和进取精神，具有创造力和艺术天赋',
            '火': '性格热情开朗，具有领导才能和社交能力，积极向上，富有激情',
            '土': '性格稳重可靠，做事踏实认真，具有很强的责任心和组织能力',
            '金': '性格坚毅果断，具有很强的判断力和执行力，追求完美，注重原则',
            '水': '性格聪明机敏，善于思考和学习，适应能力强，富有智慧和直觉'
        };
        
        const genderTrait = gender === 'male' ? '刚毅、进取' : '温柔、细腻';
        
        return `根据姓名学分析，您${personalities[element]}。结合性别特征，表现出${genderTrait}的特质，在人际交往中容易获得他人好感。`;
    }

    // 生成本地事业分析
    generateLocalCareer(wuxing, score) {
        const element = wuxing.main;
        const careers = {
            '木': '教育、文化、医疗、环保等行业',
            '火': '管理、销售、传媒、娱乐等行业',
            '土': '建筑、农业、房地产、金融等行业',
            '金': '金融、法律、技术、制造等行业',
            '水': '科研、IT、物流、贸易等行业'
        };
        
        const successRate = score >= 80 ? '很高' : score >= 65 ? '较高' : '一般';
        
        return `适合从事${careers[element]}，事业成功概率${successRate}。建议发挥个人优势，选择与姓名五行相配的职业方向。`;
    }

    // 生成本地建议
    generateLocalSuggestions(score, wuxing) {
        const suggestions = [];
        
        if (score >= 80) {
            suggestions.push('姓名配置很好，建议保持现有名字，充分发挥其积极作用');
        } else if (score >= 65) {
            suggestions.push('姓名基本良好，可以考虑在重要场合使用全名增强运势');
        } else {
            suggestions.push('姓名配置有改善空间，建议考虑调整或使用化名优化运势');
        }
        
        suggestions.push(`多接触与${wuxing.main}行相关的事物，有助于增强个人能量场`);
        
        return suggestions.join('；') + '。';
    }

    // 生成本地建议列表
    generateLocalRecommendations(score, wuxing) {
        const recs = ['保持积极乐观的心态，名字只是助力，关键在于个人努力'];
        
        if (score >= 70) {
            recs.push('在重要场合使用全名，避免使用简称或昵称');
            recs.push('可以考虑在名片或印章上使用繁体字增强能量');
        } else {
            recs.push('考虑使用化名或艺名来补充姓名能量的不足');
            recs.push('通过调整生活方式和习惯来改善整体运势');
        }
        
        recs.push(`多接触${wuxing.main}行元素的颜色和方位，有助于运势提升`);
        
        return recs;
    }

    // 提取本地开运要素
    extractLocalLuckyElements(wuxing) {
        const elementColors = {
            '木': ['绿色', '青色'],
            '火': ['红色', '橙色'],
            '土': ['黄色', '棕色'],
            '金': ['白色', '金色'],
            '水': ['黑色', '蓝色']
        };
        
        return elementColors[wuxing.main] || ['根据个人五行确定'];
    }

    // 生成本地改善建议
    generateLocalImproveAdvice(score) {
        if (score >= 80) {
            return ['继续保持现有名字的正面能量', '在重要时刻充分利用姓名的积极作用'];
        } else if (score >= 65) {
            return ['可以考虑微调名字的使用方式', '注意发挥姓名的优势部分'];
        } else {
            return ['建议考虑改名或使用化名', '通过其他方式补强运势', '寻求专业指导'];
        }
    }

    // 获取评分描述
    getScoreDescription(score) {
        if (score >= 85) return '配置优秀，是一个非常吉祥的好名字';
        if (score >= 70) return '配置良好，整体运势不错';
        if (score >= 55) return '配置一般，有改善空间';
        return '配置需要改善，建议优化';
    }

    // 完整的AI姓名分析流程
    async analyzeNameWithAI(userData, basicAnalysis) {
        this.isAnalyzing = true;
        
        try {
            console.log('开始AI姓名详批分析:', userData.name);

            const result = await this.generateAIAnalysis(userData, basicAnalysis);
            this.lastAnalysis = result;
            return result;
        } catch (error) {
            console.error('姓名AI分析失败:', error);
            throw error;
        } finally {
            this.isAnalyzing = false;
        }
    }
}

// 导出模块
window.NameAnalysisAI = NameAnalysisAI; 